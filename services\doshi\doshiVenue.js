const doshiiHelper = require('../../helper/doshiiHelper');
const BarModel = require('../../database/models').bar; //import modal always Capital
const ProductModel = require('../../database/models').product;
const SubCategoryModel = require('../../database/models').sub_category;
const SurcountModel = require('../../database/models').pos_surcounts;
const ProductExtraModel = require('../../database/models').product_extras;
const VenueUserModel = require('../../database/models').venue_user;
const ProductFoodOptionsModel =
	require('../../database/models').product_food_options;
const ProductVariantTypesModel =
	require('../../database/models').product_variant_types;
const ProductVariantSubTypesModel =
	require('../../database/models').product_variant_sub_types;
const StripeWebhookEvent =
	require('../../database/models').stripe_webhook_event; //import modal always Capital
const BarPOSSubscriptionsModel =
	require('../../database/models').bar_pos_subscriptions; //import modal always Capital
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const locationId = 'QppDXWnyL';

const { getDoshiAuthToken } = require('../../helper/doshiiHelper');
const { postData } = require('../../helper/axiosHelper');
const moment = require('moment');

const isVenueValid = async () => {
	try {
		const getAuthDoshiToken = await doshiiHelper.isVenueIdValid(locationId);
		return getAuthDoshiToken;
	} catch (error) {
		console.log('error ', error);
		throw error;
	}
};

const getDoshiMenuItems = async () => {
	try {
		const getMenu = await doshiiHelper.getDoshiMenuItems(locationId);
		return getMenu;
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const getDoshiiReferralLink = async (req) => {
	try {
		let barDetails = await BarModel.findOne({
			where: {
				id: req.body.bar_id
			}
		});
		let venueOwnerDetails = await VenueUserModel.findOne({
			where: {
				id: req.body.venue_user_id
			}
		});
		const getData = await doshiiHelper.getDoshiiReferralLink(
			barDetails,
			venueOwnerDetails
		);
		barDetails.update({
			posStatus: '0'
		});
		return {
			status: 1,
			message: 'Referral link fetched successfully',
			data: getData
		};
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const doshiiConnectWebhook = async (requestData) => {
	try {
		if (requestData.event == 'location_subscription') {
			if (requestData.data.status == 'subscribed') {
				if (requestData.data.mappedLocationId) {
					let barDetails = await BarModel.findOne({
						where: {
							id: requestData.data.mappedLocationId
						}
					});
					if (barDetails) {
						barDetails.update({
							venueId: requestData.data.id,
							posStatus: '1'
						});
						return 1;
					} else {
						return 2;
					}
				} else {
					return 2;
				}
			} else if (requestData.data.status == 'unsubscribed') {
				let barDetails = await BarModel.findOne({
					where: {
						venueId: requestData.data.id
					}
				});
				if (barDetails) {
					await barDetails.update({
						venueId: null,
						posStatus: '-1'
					});
					await ProductModel.update(
						{
							isDeleted: 'Yes'
						},
						{
							where: {
								barID: barDetails.id
							}
						}
					);
					return 1;
				} else {
					return 2;
				}
			}
		} else {
			return 3;
		}
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const doshiiMenuWebhook = async (requestData) => {
	try {
		let barDetails = await BarModel.findOne({
			where: {
				venueId: requestData.data.locationId,
				posStatus: '1'
			}
		});
		if (!barDetails) {
			return 0;
		} else {
			if (requestData.event === 'app_menu_item_updated') {
				if (requestData.data.action === 'updated') {
					const updateProduct = await doshiiHelper.updateMenuDoshiiWebhook(
						requestData
					);
					await doshiiHelper.setCategoryAndProductSequence(
						requestData.data.locationId,
						barDetails.dataValues.id
					);
					return updateProduct;
				} else if (requestData.data.action === 'deleted') {
					const deleteProduct = await doshiiHelper.updateMenuDoshiiWebhook(
						requestData
					);
					await doshiiHelper.setCategoryAndProductSequence(
						requestData.data.locationId,
						barDetails.dataValues.id
					);
					return deleteProduct;
				}
			} else if (requestData.event === 'app_menu_category_updated') {
				await doshiiHelper.setCategoryAndProductSequence(
					requestData.data.locationId,
					barDetails.dataValues.id
				);
				// const updateCategory = await doshiiHelper.updateCategoryDoshiiWebhook(
				// 	requestData
				// );
				return '1';
			} else if (requestData.event === 'app_menu_updated') {
				const getDoshiiFilteredAppMenuResponse =
					await doshiiHelper.getDoshiiFilteredAppMenu(
						requestData.data.locationId,
						barDetails.dataValues.id
					);
				await doshiiHelper.setCategoryAndProductSequence(
					requestData.data.locationId,
					barDetails.dataValues.id
				);
				return getDoshiiFilteredAppMenuResponse;
			}
		}
	} catch (error) {
		console.log('error ', error);
		return 0;
	}
};
const doshiiOrderWebhook = async (requestData) => {
	try {
		const { event, data } = requestData;
		const updateOrder = await doshiiHelper.updateOrderFromDoshiiWebhook(data);
		return updateOrder;
	} catch (error) {
		console.log('error ', error);
		throw error;
	}
};

const getMenuFromDoshiiAndUpdate = async (requestData) => {
	try {
		const barId = requestData.bar_id;

		const checkIfBarPresent = await BarModel.findOne({
			where: {
				id: barId,
				posStatus: '1'
			}
		});

		if (!checkIfBarPresent) {
			return 3; // bar is not present
		} else if (checkIfBarPresent.dataValues.venueId === null) {
			return 2; // bar is not connect to any pos venue
		} else {
			const getDoshiiFilteredAppMenuResponse =
				await doshiiHelper.getDoshiiFilteredAppMenu(
					checkIfBarPresent.dataValues.venueId,
					barId
				);
			await doshiiHelper.setCategoryAndProductSequence(
				checkIfBarPresent.dataValues.venueId,
				barId
			);
			return getDoshiiFilteredAppMenuResponse;
		}
	} catch (error) {
		console.log('error ', error);
		throw error;
	}
};

const stripeSubscription = async (req) => {
	let barData = await BarModel.findOne({
		attributes: ['id', 'email', 'restaurantName', 'stripeCustomerID'],
		where: {
			id: req.body.bar_id,
			isDeleted: 'No'
		}
	});
	if (barData) {
		var barStripeID = barData.stripeCustomerID;
		if (barStripeID === '' || barStripeID === null) {
			let newCustomer = await stripe.customers.create({
				description: barData.id + '_' + barData.restaurantName,
				email: barData.email
			});
			if (newCustomer) {
				barStripeID = newCustomer.id;
				await BarModel.update(
					{
						stripeCustomerID: barStripeID
					},
					{
						where: {
							id: barData.id
						}
					}
				);
				let newSubscription = await stripe.subscriptions.create({
					customer: barStripeID,
					items: [
						{
							price: process.env.STRIPE_POS_PAYMENT_ID
						}
					],
					payment_behavior: 'default_incomplete',
					payment_settings: {
						save_default_payment_method: 'on_subscription'
					},
					expand: ['latest_invoice.payment_intent']
				});
				if (newSubscription) {
					await BarPOSSubscriptionsModel.create({
						barID: req.body.bar_id,
						subscription_id: newSubscription.id,
						payment_intent_id: newSubscription.latest_invoice.payment_intent.id,
						payment_status: 'Pending',
						start_date: new Date(
							newSubscription.latest_invoice.period_start * 1000
						),
						end_date: new Date(newSubscription.latest_invoice.period_end * 1000)
					});
					return newSubscription.latest_invoice.payment_intent.client_secret;
				} else {
					return 0;
				}
			} else {
				return 0;
			}
		} else {
			let newSubscription = await stripe.subscriptions.create({
				customer: barStripeID,
				items: [
					{
						price: process.env.STRIPE_POS_PAYMENT_ID
					}
				],
				payment_behavior: 'default_incomplete',
				payment_settings: {
					save_default_payment_method: 'on_subscription',
					payment_method_types: ['card']
				},
				expand: ['latest_invoice.payment_intent']
			});
			if (newSubscription) {
				await BarPOSSubscriptionsModel.create({
					barID: barData.id,
					subscription_id: newSubscription.id,
					payment_intent_id: newSubscription.latest_invoice.payment_intent.id,
					payment_status: 'Pending',
					start_date: new Date(
						newSubscription.latest_invoice.period_start * 1000
					),
					end_date: new Date(newSubscription.latest_invoice.period_end * 1000)
				});
				return newSubscription.latest_invoice.payment_intent.client_secret;
			} else {
				return 0;
			}
		}
	} else {
		return 0;
	}
};

const stripeCompleteSubscription = async (req) => {
	try {
		let barDetails = await BarModel.findOne({
			where: {
				id: req.body.bar_id
			}
		});
		if (barDetails) {
			let barSubscriptionDetails = await BarPOSSubscriptionsModel.findOne({
				where: {
					barID: req.body.bar_id,
					payment_intent_id: req.body.payment_intent
				}
			});
			if (barSubscriptionDetails) {
				const paymentIntent = await stripe.paymentIntents.retrieve(
					req.body.payment_intent
				);
				await stripe.customers.update(barDetails.stripeCustomerID, {
					invoice_settings: {
						default_payment_method: paymentIntent.payment_method
					}
				});
				if (paymentIntent.status == 'succeeded') {
					await barSubscriptionDetails.update({
						payment_intent_client_secret: req.body.payment_intent_client_secret,
						payment_status: 'Completed'
					});
					await barDetails.update({
						posStatus: '0'
					});
					let venueOwnerDetails = await VenueUserModel.findOne({
						where: {
							id: req.body.venue_user_id ? req.body.venue_user_id : 41
						}
					});
					const getData = await doshiiHelper.getDoshiiReferralLink(
						barDetails,
						venueOwnerDetails
					);
					barDetails.update({
						posStatus: '0',
						posReferralLink: getData.link
					});
					return getData;
				} else {
					return 0;
				}
			} else {
				return 0;
			}
		} else {
			return 0;
		}
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const posSubscriptionEnable = async (req) => {
	try {
		let barDetails = await BarModel.findOne({
			where: {
				id: req.body.bar_id
			}
		});
		if (barDetails) {
			let venueOwnerDetails = await VenueUserModel.findOne({
				where: {
					id: req.user_id ? req.user_id : 41
				}
			});
			const getData = await doshiiHelper.getDoshiiReferralLink(
				barDetails,
				venueOwnerDetails
			);
			barDetails.update({
				posStatus: '0',
				posReferralLink: getData.link
			});
			return getData;
		} else {
			return 0;
		}
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const posSubscriptionDisable = async (req) => {
	try {
		let barDetails = await BarModel.findOne({
			where: {
				id: req.body.bar_id
			}
		});
		if (barDetails) {
			await barDetails.update({
				venueId: null,
				posReferralLink: null,
				posStatus: '-1'
			});
			await ProductModel.update(
				{
					isDeleted: 'Yes'
				},
				{
					where: {
						barID: barDetails.id
					}
				}
			);
			return 1;
		} else {
			return 0;
		}
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

const stripeWebhook = async (req) => {
	try {
		if (req.body.data) {
			let subscriptionReq = req.body.data.object;
			let barSubscriptionDetails = await BarPOSSubscriptionsModel.findOne({
				where: {
					subscription_id: subscriptionReq.id
				}
			});
			if (barSubscriptionDetails) {
				let startDate = new Date(subscriptionReq.current_period_start * 1000);
				let endDate = new Date(subscriptionReq.current_period_end * 1000);
				BarPOSSubscriptionsModel.update(
					{
						start_date: startDate,
						end_date: endDate,
						subscription_status: subscriptionReq.status
					},
					{
						where: {
							subscription_id: subscriptionReq.id
						}
					}
				);
			}
		}
		await StripeWebhookEvent.create({
			eventID: req.body.id,
			subscriptionID: req.body.data.object.id,
			type: req.body.type,
			data: JSON.stringify(req.body.data)
		});
		return 1;
	} catch (error) {
		console.log(' error ', error);
		throw error;
	}
};

module.exports = {
	isVenueValid,
	getDoshiMenuItems,
	getDoshiiReferralLink,
	doshiiConnectWebhook,
	doshiiMenuWebhook,
	stripeSubscription,
	stripeCompleteSubscription,
	posSubscriptionEnable,
	posSubscriptionDisable,
	stripeWebhook,
	doshiiOrderWebhook,
	getMenuFromDoshiiAndUpdate
};
