/*Messages,status code and services require*/
require('dotenv').config();

const moment = require('moment');

const BarModel = require('../../../database/models').bar;
const SubCategoryUpsellModel =
	require('../../../database/models').venue_sub_category_upsell;
const BarCategorySequenceModel =
	require('../../../database/models').bar_category_sequence;
const SubCategoryModel = require('../../../database/models').sub_category;
const { sequelize } = require('../../../database/models');
const message = require('../../../config/cmsMessage').cmsMessage;
const Sequelize = require('sequelize');

const Op = Sequelize.Op;

module.exports = {
	async addVenueSubCategoryUpsell(req, res) {
		const {
			bar_id: barID,
			parent_sub_category_id: parentSubCategoryID,
			child_sub_category_id: childSubCategoryID
		} = req.body || {};

		try {
			if (parentSubCategoryID == childSubCategoryID) {
				return {
					code: 0,
					message: message.SUBCATEGORYIDSAME
				};
			}
			const [subCategoriesExist, isEntryExist] = await Promise.all([
				SubCategoryModel.count({
					where: {
						id: [parentSubCategoryID, childSubCategoryID]
					}
				}),
				SubCategoryUpsellModel.count({
					where: {
						barID,
						parentSubCategoryID
					}
				})
			]);

			if (subCategoriesExist !== 2) {
				return {
					code: 0,
					message: message.SUBCATEGORYNOTFOUND
				};
			}
			if (isEntryExist) {
				return {
					code: 0,
					message: message.SUBCATEGORYLINKEXIST
				};
			}
			const data = await SubCategoryUpsellModel.create({
				barID,
				parentSubCategoryID,
				childSubCategoryID
			});
			return {
				code: 1,
				data,
				message: message.SUBCATEGORYLINKADDED
			};
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async updateVenueSubCategoryUpsell(req, res) {
		const {
			bar_id: barID,
			parent_sub_category_id: parentSubCategoryID,
			child_sub_category_id: childSubCategoryID
		} = req.body || {};

		try {
			if (parentSubCategoryID == childSubCategoryID) {
				return {
					code: 0,
					message: message.SUBCATEGORYIDSAME
				};
			}
			const [subCategoriesExist, isEntryExist] = await Promise.all([
				SubCategoryModel.count({
					where: {
						id: [parentSubCategoryID, childSubCategoryID]
					}
				}),
				SubCategoryUpsellModel.count({
					where: {
						barID,
						parentSubCategoryID
					}
				})
			]);
			if (subCategoriesExist !== 2) {
				return {
					code: 0,
					message: message.SUBCATEGORYNOTFOUND
				};
			}
			if (!isEntryExist) {
				return {
					code: 0,
					message: message.SUBCATEGORYLINKNOTFOUND
				};
			}
			await SubCategoryUpsellModel.update(
				{
					childSubCategoryID
				},
				{ where: { barID, parentSubCategoryID } }
			);
			return {
				code: 1,
				message: message.SUBCATEGORYLINKUPDATED
			};
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async deleteVenueSubCategoryUpsell(req, res) {
		const { bar_id: barID, parent_sub_category_id: parentSubCategoryID } =
			req.body || {};

		try {
			const [subCategoryExist, isEntryExist] = await Promise.all([
				SubCategoryModel.count({
					where: {
						id: parentSubCategoryID
					}
				}),
				SubCategoryUpsellModel.count({
					where: {
						barID,
						parentSubCategoryID
					}
				})
			]);
			if (!subCategoryExist) {
				return {
					code: 0,
					message: message.SUBCATEGORYNOTFOUND
				};
			}
			if (!isEntryExist) {
				return {
					code: 0,
					message: message.SUBCATEGORYLINKNOTFOUND
				};
			}
			await SubCategoryUpsellModel.destroy({
				where: {
					barID,
					parentSubCategoryID
				}
			});
			return {
				code: 1,
				message: message.SUBCATEGORYUNLINKED
			};
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async getVenueSubCategoryUpsell(req, res) {
		var barID = req.body.bar_id;

		try {
			const barData = await BarModel.findOne({
				attributes: ['id', 'restaurantName', 'posStatus'],
				where: {
					id: barID
				}
			});
			if (
				barData.dataValues.posStatus &&
				barData.dataValues.posStatus === '1'
			) {
				var whereClause = {
					categoryID: '-1',
					isDeleted: 'No'
				};
			} else {
				var whereClause = {
					[Op.not]: [{ categoryID: '-1' }],
					isDeleted: 'No'
				};
			}

			let data = await SubCategoryModel.findAll({
				attributes: ['id', 'categoryID', 'name'],
				include: [
					{
						model: BarCategorySequenceModel,
						as: 'bar_category_sequence',
						required: false,
						attributes: [
							[
								Sequelize.fn(
									'coalesce',
									Sequelize.col('subCategorySequence'),
									1000000000000
								),
								'subCategorySequence'
							]
						],
						where: { barId: barID }
					},
					{
						model: SubCategoryUpsellModel,
						as: 'childSubCategoryLink',
						required: false,
						attributes: ['childSubCategoryID'],
						include: {
							model: SubCategoryModel,
							as: 'subCategory',
							attributes: ['name']
						},
						where: { barID }
					}
				],
				order: [
					[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
					'id'
				],
				where: {
					...whereClause,
					[Op.and]: [
						Sequelize.literal(`(
						SELECT COUNT(product.id) 
						FROM product 
						WHERE product.subCategoryID = sub_category.id 
						  AND product.isDeleted = "No" 
						  AND product.status = "Active" 
						  AND product.barID = ${barID}
					  ) > 0`)
					]
				}
			});

			if (data.length > 0) {
				return data;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
};
