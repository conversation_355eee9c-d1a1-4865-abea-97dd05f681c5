require('dotenv').config();
const BarModel = require('../../database/models').bar;
const { Sequelize, QueryTypes } = require('sequelize');
const moment = require("moment");
const Op = Sequelize.Op;


module.exports = {

    async changeStatus(req) {
        try {
            let barData = await BarModel.findOne({
                where: {
                    id: req.body.bar_id
                }
            })

            let docketTrialEndDate;
            if (!barData.docketTrialEndDate) {
                docketTrialEndDate = moment().add(7, 'day');
            }

            //Change status
            await BarModel.update({ docketTrialEndDate: docketTrialEndDate, docketCommission: 1, docketStatus: String(1 - (Number(barData.docketStatus))), }, {
                where: {
                    id: req.body.bar_id
                },
                returning: true

            });


            return {
                currentStatus: 1 - (Number(barData.docketStatus))
            }

        } catch (error) {
            console.log(error);
            throw error;
        }
    },
};
