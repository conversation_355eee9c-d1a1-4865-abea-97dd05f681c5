const message = require('../../config/cmsMessage.js').cmsMessage;
const status = require('../../config/status.js').status;
const segmentService = require('../../services/venue/segmentService.js');
const segmentValidation = require('../../validations/venue/segmentValidation.js');

module.exports = {
    /* Get Segment List */
    async list(req, res) {
        try {
            const valid = await segmentValidation.getSegmentListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let SegmentList = await segmentService.getSegmentList(req);

            if (SegmentList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    SegmentList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

     /* Get All Segment List (bar_id optional) */
     async allSegmentList(req, res) {
        try {
            const valid = await segmentValidation.getAllSegmentListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            let SegmentList = await segmentService.getAllSegmentList(req);

            if (SegmentList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    SegmentList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    
    /* Export Segment List */
    async exportList(req, res) {
        try {
            const valid = await segmentValidation.getSegmentListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let exportSegmentList = await segmentService.exportSegmentList(req);

            if (exportSegmentList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    exportSegmentList,
                    message.DATA_EXPORTED_SUCCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Get Segment Details */
    async details(req, res) {
        try {
            const valid = await segmentValidation.getSegmentDetailsValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let SegmentData = await segmentService.getSegmentDetails(req);

            if (SegmentData) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    SegmentData,
                    message.SEGMENTDETAILSFETCHED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Get Segment Customer List */
    async customerList(req, res) {
        try {
            const valid = await segmentValidation.getCustomerListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let CustomerList = await segmentService.getCustomerList(req);

            if (CustomerList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Export Segment Customer List */
    async exportCustomerList(req, res) {
        try {
            const valid = await segmentValidation.exportCustomerListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let CustomerList = await segmentService.exportCustomerList(req);

            if (CustomerList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerList,
                    message.DATA_EXPORTED_SUCCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Download Customers List */
    async download(req, res) {
        try {
            const valid = await segmentValidation.downloadSegmentValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let SegmentList = await segmentService.downloadSegmentList(req);

            if (SegmentList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    SegmentList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    }
};
