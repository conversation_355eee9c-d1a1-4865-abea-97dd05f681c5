const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;
const productService = require('../../../services/v2/venue/productService');
const productValidation = require('../../../validations/v2/venue/productValidation');

module.exports = {
	async getVenueSubCategories(req, res) {
		try {
			const valid = await productValidation.getVenueCategoriesValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let venueCategoriesList = await productService.getVenueSubCategories(
				req,
				res
			);

			if (venueCategoriesList) {
				return response(
					res,
					status.SUCCESSSTATUS,
					venueCategoriesList,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getSegmentTags(req, res) {
		try {
			const valid = await productValidation.getSegmentTagsValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await productService.getSegmentTags(req, res);

			return response(
				res,
				status.SUCCESSSTATUS,
				data,
				message.LISTFETCHEDSUCESSFULLY,
				status.SUCCESS
			);
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async globalPriceUpdate(req, res) {
		try {
			const valid = await productValidation.globalPriceUpdateValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let priceUpdateData = await productService.globalPriceUpdate(req, res);

			if (priceUpdateData) {
				return response(
					res,
					status.SUCCESSSTATUS,
					priceUpdateData,
					message.UPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
