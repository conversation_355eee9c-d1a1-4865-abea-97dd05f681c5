CREATE TABLE `mytab`.`subscription` ( `id` INT NOT NULL AUTO_INCREMENT , `name` VARCHAR(500) NOT NULL , `price` INT NOT NULL , `created_at` DATETIME NOT NULL , `updated_at` DATETIME NOT NULL , `deleted_at` DATETIME NULL DEFAULT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;
CREATE TABLE `mytab`.`venue_user_subscription` ( `id` INT NOT NULL AUTO_INCREMENT , `subscription_id` INT NOT NULL , `user_id` INT NOT NULL ,`status` int NOT NULL ,`created_at` DATETIME NOT NULL  , `updated_at` DATETIME NOT NULL  , `deleted_at` DATETIME NULL DEFAULT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;
