const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const doshiService = require('../../services/doshi/doshiVenue');
const doshiHelper = require('../../helper/doshiiHelper');

const isVenueValid = async (req, res) => {
	try {
		const checkVenueIsValid = await doshiService.isVenueValid();
		return res.json(checkVenueIsValid);
	} catch (error) {
		console.log('error ', error);
	}
};

const getDoshiMenuItems = async (req, res) => {
	try {
		const getMenu = await doshiService.getDoshiMenuItems();
		return res.json(getMenu);
	} catch (error) {
		console.log('error ', error);
	}
};

const doshiiConnectWebhook = async (req, res) => {
	try {
		let { verify } = req.query;
		if (verify) {
			return res.status(200).send(verify);
		}
		let requestData = req.body;
		const getResponse = await doshiService.doshiiConnectWebhook(requestData);
		if (getResponse == 1) {
			return res.status(200).json({ message: 'Webhook received successfully' });
		} else if (getResponse == 2) {
			return res.status(200).json({ message: 'Invalid venue id' });
		} else {
			return res.status(200).json({ message: 'Invalid webhook event' });
		}
	} catch (error) {
		console.log('error ', error);
	}
};

const doshiiMenuWebhook = async (req, res) => {
	try {
		let requestBody = req.body;
		const getMenuWebhook = await doshiService.doshiiMenuWebhook(requestBody);
		if (getMenuWebhook === 0) {
			return res.status(200).json({
				message: 'Something went wrong, please try after sometime.'
			});
		} else {
			return res.status(200).json({
				message: 'Menu webhook called'
			});
		}
	} catch (error) {
		console.log('error ', error);
	}
};

const doshiiOrderWebhook = async (req, res) => {
	try {
		let requestBody = req.body;
		const getOrderWebhook = await doshiService.doshiiOrderWebhook(requestBody);
		if (getOrderWebhook === 0) {
			return res.status(200).json({
				message: 'Invalid Webhook'
			});
		} else {
			return res.status(200).json({
				message: 'Order webhook called'
			});
		}
	} catch (error) {
		console.log('error  ', error);
	}
};

const createOrderInDoshii = async (req, res) => {
	try {
		const { payload, locationId, orderId } = req.body;
		await doshiHelper.getOrderDetailsById(orderId);

		const createOrderInDoshii = await doshiHelper.createOrderInDoshii(
			payload,
			locationId,
			orderId
		);
		if (createOrderInDoshii === 0) {
			return res.status(400).json({
				message: 'Unable to create the order in doshii!!'
			});
		} else {
			return res.status(200).json({
				data: createOrderInDoshii,
				message: 'Order Created!!'
			});
		}
	} catch (error) {
		console.log('error ', error);
	}
};

const verifyDoshiiWebhook = async (req, res, next) => {
	try {
		if (req.query.verify) {
			const { verify } = req.query;
			return res.status(200).send(verify);
		} else {
			next();
		}
	} catch (error) {
		console.log('error ', error);
		return res.status(200).json({
			message: 'Internal Server Error'
		});
	}
};

const doshiTestFuntion = async (req, res) => {
	let testDoshiiRes;
	// await doshiService.isVenueValid
	// testDoshiiRes = await doshiService.isVenueValid();
	testDoshiiRes = await doshiHelper.orderCancel('VEN2592532');
	return res.json(testDoshiiRes);
};

const getDoshiiReferralLink = async (req, res) => {
	let doshiiResponse = await doshiService.getDoshiiReferralLink(req);
	return res.json(doshiiResponse);
};

const getMenuFromDoshii = async (req, res) => {
	try {
		const { barId } = req.body;
		const getOrderFromDoshii = await doshiService.getMenuFromDoshiiAndUpdate(
			req.body
		);

		if (getOrderFromDoshii === 3) {
			return res.status(200).json({
				message: 'Venue does not have POS active subscription',
				status: 0
			});
		} else if (getOrderFromDoshii === 2) {
			return res.status(200).json({
				message: 'Venue does not have POS active subscription.',
				status: 0
			});
		} else if (getOrderFromDoshii === 0) {
			return res.status(200).json({
				message: 'Unable to fetch the menu from Pos, Try again after sometime.',
				status: 0
			});
		} else if (getOrderFromDoshii === 4) {
			return res.status(200).json({
				message: 'No menu items found!',
				status: 0
			});
		} else {
			return res.status(200).json({
				message: 'Your manual menu sync is completed.',
				status: 1
			});
		}
	} catch (error) {
		console.log('error ', error);
		return res.status(200).json({
			message: 'Internal Server Error'
		});
	}
};

const stripeSubscription = async (req, res) => {
	try {
		let doshiiResponse = await doshiService.stripeSubscription(req);
		if (doshiiResponse) {
			return response(
				res,
				status.SUCCESSSTATUS,
				doshiiResponse,
				message.SUBSCRIPTIONCRESTED,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.SOMETHINGWENTWRONG,
				status.ERROR
			);
		}
	} catch (error) {
		console.log(error);
		// response on internal server error
		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			{},
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};

const stripeCompleteSubscription = async (req, res) => {
	try {
		let doshiiResponse = await doshiService.stripeCompleteSubscription(req);
		if (doshiiResponse) {
			return response(
				res,
				status.SUCCESSSTATUS,
				doshiiResponse,
				message.SUBSCRIPTIONCRESTED,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.SOMETHINGWENTWRONG,
				status.ERROR
			);
		}
	} catch (error) {
		console.log(error);
		// response on internal server error
		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			{},
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};

const posSubscriptionsEnable = async (req, res) => {
	try {
		let doshiiResponse = await doshiService.posSubscriptionEnable(req);
		if (doshiiResponse) {
			return response(
				res,
				status.SUCCESSSTATUS,
				doshiiResponse,
				message.POSFEATUREACTIVATED,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.SOMETHINGWENTWRONG,
				status.ERROR
			);
		}
	} catch (error) {
		console.log(error);
		// response on internal server error
		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			{},
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};
const posSubscriptionDisable = async (req, res) => {
	try {
		let doshiiResponse = await doshiService.posSubscriptionDisable(req);
		if (doshiiResponse) {
			return response(
				res,
				status.SUCCESSSTATUS,
				doshiiResponse,
				message.POSFEATUREDEACTIVATED,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.SOMETHINGWENTWRONG,
				status.ERROR
			);
		}
	} catch (error) {
		console.log(error);
		// response on internal server error
		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			{},
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};

const stripeWebhook = async (req, res) => {
	try {
		let stripeWebhookResponse = await doshiService.stripeWebhook(req);
		if (stripeWebhookResponse) {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.WEBHOOKSAVESUCCESSFULLY,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.SUCCESSSTATUS,
				{},
				message.SOMETHINGWENTWRONG,
				status.ERROR
			);
		}
	} catch (error) {
		console.log(error);
		// response on internal server error
		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			{},
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};

module.exports = {
	isVenueValid,
	getDoshiMenuItems,
	doshiTestFuntion,
	doshiiConnectWebhook,
	getDoshiiReferralLink,
	verifyDoshiiWebhook,
	doshiiMenuWebhook,
	stripeSubscription,
	stripeCompleteSubscription,
	posSubscriptionsEnable,
	posSubscriptionDisable,
	stripeWebhook,
	doshiiOrderWebhook,
	createOrderInDoshii,
	getMenuFromDoshii
};
