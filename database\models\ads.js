'use strict';
const { Model } = require('sequelize');
const constant = require('../../config/constant');

module.exports = (sequelize, DataTypes) => {
    class ads extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The models/index file will call this method automatically.
         */
        static associate(models) {
            // define association here
            models.bar.hasMany(ads, {
                foreignKey: 'bar_id'
            });
            ads.belongsTo(models.bar, {
                foreignKey: 'bar_id'
            });
            ads.hasMany(models.ads_segment, {
                foreignKey: 'adsID',
                as: 'segments'
            });
            ads.hasMany(models.ads_users, {
                foreignKey: 'adsID',
                as: 'eligibleUsers'
            });
            ads.belongsTo(models.campaign, {
                foreignKey: 'campaign_id',
                targetKey: 'id'
            });
            ads.hasMany(models.payments, {
                foreignKey: 'ad_id',
                as: 'payments'
            });
        }
    }

    ads.init(
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true
            },
            invoice_no: {
                type: DataTypes.STRING(255),
                allowNull: false
            },            
            campaign_id: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            bar_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: 'bar',
                    key: 'id'
                }
            },
            objective: {
                type: DataTypes.ENUM('CPM', 'CPC', 'CPA'),
                allowNull: true
            },
            ad_title: {
                type: DataTypes.STRING(255),
                allowNull: true
            },
            ad_description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            state: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            city: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            suburb: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            call_to_action_url: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            media_url: {
                type: DataTypes.STRING(500),
                get() {
                    if (
                        this.getDataValue('media_url') != '' &&
                        this.getDataValue('media_url') != null
                    ) {
                        return (
                            constant.AWSS3PRIVATEURL +
                            constant.AWSS3ADVERTISERFOLDER +
                            this.getDataValue('media_url')
                        );
                    } else {
                        return '';
                    }
                },
                defaultValue: null
            },
            call_to_action: {
                type: DataTypes.ENUM('learn_more', 'order_now', 'buy_tickets', 'view_menu', 'visit_website'),
                allowNull: true
            },
            eligibility_type: {
                type: DataTypes.ENUM('all_mytab_customers','mytab_customer_segments','your_venues_customers','your_venues_customers_segment'),
                allowNull: true
            },
            save_card: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: 0
            },
            combined_eligibility: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: 0
            },
            created_by_type: {
                type: DataTypes.ENUM('venue', 'advertiser', 'cms'),
                allowNull: true
            },
            start_date: {
                type: DataTypes.DATE,
                allowNull: true
            },
            end_date: {
                type: DataTypes.DATE,
                allowNull: true
            },
            start_time: {
                type: DataTypes.TIME,
                allowNull: true
            },
            end_time: {
                type: DataTypes.TIME,
                allowNull: true
            },
            daily_budget: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            ad_status: {
                type: DataTypes.ENUM('New', 'Approved', 'Rejected'),
                allowNull: false,
                defaultValue: 'New'
            },
            status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
            pause_status: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            },
            pause_by_admin: {
                type: DataTypes.ENUM('Yes', 'No'),
                allowNull: true,
                defaultValue: false
            },
            created_by_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: DataTypes.NOW
            },
            deleted_at: {
                type: DataTypes.DATE,
                allowNull: true
            },            
            deleted_by_admin: {
                type: DataTypes.ENUM('Yes', 'No'),
                allowNull: true,
                defaultValue: false
            },
        },
        {
            sequelize,
            modelName: 'ads',
            tableName: 'ads',
            timestamps: true,
            paranoid: true,
            underscored: true,
            createdAt: 'created_at',
            updatedAt: 'updated_at',
            deletedAt: 'deleted_at'
        }
    );

    return ads;
};