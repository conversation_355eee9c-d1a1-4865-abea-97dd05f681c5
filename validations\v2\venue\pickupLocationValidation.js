const Joi = require('joi');

module.exports = {
	async add(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			address: Joi.string().required().messages({
				'any.required': 'address is required',
				'string.empty': 'address is required'
			})
		});
		return schema.validate(req.body);
	},
	async list(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			})
		});
		return schema.validate(req.body);
	},
	async edit(req) {
		const schema = Joi.object({
			id: Joi.string().optional().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			}),
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			address: Joi.string().optional().messages({
				'string.empty': 'address is required'
			}),
			sub_category_ids: Joi.array().optional()
			// updateCategoryProducts: Joi.boolean().optional()
		});
		return schema.validate(req.body);
	},
	async delete(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			id: Joi.string().required().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			})
		});
		return schema.validate(req.body);
	}
};
