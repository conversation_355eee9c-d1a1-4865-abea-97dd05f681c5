// Used to interact with AWS Service

require('dotenv').config();

var AWS = require('aws-sdk');
const fs = require('fs');
const constant = require('../config/constant');

AWS.config.update({
	secretAccessKey: process.env.S3_SECRETKEY,
	accessKeyId: process.env.S3_ACCESSKEY,
	region: process.env.S3_REGION
});

var s3 = new AWS.S3({
	params: {
		Bucket: process.env.S3_BUCKET_NAME
	}
});

// Create an STS client
// const sts = new AWS.STS();

// async function assumeRole() {
// 	const params = {
// 		RoleArn: constant.AWSS3ARN,
// 		RoleSessionName: 'awsMyTabS3AccessRole'
// 	};

// 	try {
// 		const data = await sts.assumeRole(params).promise();
// 		return {
// 			accessKeyId: data.Credentials.AccessKeyId,
// 			secretAccessKey: data.Credentials.SecretAccessKey,
// 			sessionToken: data.Credentials.SessionToken
// 		};
// 	} catch (error) {
// 		console.log('Error assuming role:', error);
// 		throw error;
// 	}
// }

function s3Get(path) {
	try {
		// const credentials = await assumeRole();
		// AWS.config.update({
		// 	accessKeyId: credentials.accessKeyId,
		// 	secretAccessKey: credentials.secretAccessKey,
		// 	sessionToken: credentials.sessionToken,
		// 	region: process.env.S3_REGION
		// });S3_BUCKET_NAME

		// var s3 = new AWS.S3();

		const url = s3.getSignedUrl('getObject', {
			Bucket: process.env.S3_BUCKET_NAME,
			Key: path,
			Expires: process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 // time in seconds: e.g. 60 * 5 = 5 mins
		});
		return url;
	} catch (e) {
		console.log('functionS3Get -> e', e);
		// reject({ message: 'Could not get file', err: e });
		return '';
	}
}

module.exports = s3Get;
