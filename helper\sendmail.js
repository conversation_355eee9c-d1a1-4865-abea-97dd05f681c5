require('dotenv').config();

const ejs = require('ejs');
const nodemailer = require('nodemailer');
const constant = require('../config/constant');
const message = require('../config/cmsMessage').cmsMessage;
const status = require('../config/status').status;

var sendmail = async function (
	res,
	email,
	subject,
	mailbody,
	attachments = '',
	replyToEmail = ''
) {
	try {
		const today = new Date();
		const year = today.getFullYear();

		let options = {
			host: constant.MAIL_HOST,
			port: constant.MAIL_PORT,
			secure: false,
			auth: {
				user: constant.MAIL_FROM,
				pass: constant.MAIL_PASSWORD
			}
		};

		var transporter = nodemailer.createTransport(options);

		let html_data = await ejs.renderFile(__dirname + '/email.ejs', {
			TITLE: subject,
			HTML_BODY: mailbody,
			LOGOURL: process.env.LOGOURL,
			APPNAME: 'MyTab',
			APPCOLOR: process.env.APPCOLOR,
			YEAR: year
		});

		let mailOption = {
			//from: '"' + constant.APP_NAME + '" <"'+constant.MAIL_FROM_AUTH+'">',
			from: '"MyTab" <' + constant.MAIL_FROM_AUTH + '>',
			to: email,
			html: html_data,
			subject: subject
		};
		if (replyToEmail != '') {
			mailOption.replayTo = replyToEmail;
		}

		if (attachments != '') {
			mailOption.attachments = attachments;
		}

		let mailResponse = await transporter.sendMail(mailOption);

		return mailResponse;
	} catch (error) {
		console.log('error-------', error);

		return response(
			res,
			status.INTERNALSERVERERRORSTATUS,
			[],
			message.INTERNALSERVERERROR,
			status.ERROR
		);
	}
};

exports.sendmail = sendmail;
