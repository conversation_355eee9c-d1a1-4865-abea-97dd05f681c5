const Joi = require('joi');

module.exports = {
	/* add Product Validation */
	async addProductValidation(req) {
		const schema = Joi.object({
			category_id: Joi.number().required().messages({
				'any.required': 'Category id is required.',
				'number.empty': 'Category id is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			subcategory_id: Joi.number().required().messages({
				'any.required': 'Subcategory id is required.',
				'number.empty': 'Subcategory id is required.'
			}),
			name: Joi.string().required().messages({
				'any.required': 'Name is required.',
				'string.empty': 'Name is required.'
			}),
			description: Joi.string().required().messages({
				'any.required': 'Description is required.',
				'string.empty': 'Description is required.'
			}),
			price: Joi.number().required().messages({
				'any.required': 'price  is required.',
				'number.empty': 'price is required.'
			}),
			isDailyStockRenewal: Joi.string().valid('Yes', 'No').optional().messages({
				'any.only': 'Daily Stock renewal must be Yes Or No.'
			}),
			dailyStockRenewal: Joi.number().optional(),
			isStockLimit: Joi.string().valid('Yes', 'No').optional().messages({
				'any.only': 'Daily Stock renewal must be Yes Or No.'
			}),
			stock: Joi.number().optional(),
			serviceType: Joi.string()
				.valid('PICKUP', 'TABLE', 'BOTH')
				.optional()
				.messages({
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be [Takeaway,Table service,Both].'
				}),
			pickuplocation_id: Joi.number().required().messages({
				'any.required': 'Pickup location is required.',
				'number.empty': 'Pickup location id is required.'
			}),
			// calorie: Joi.number().optional().messages({
			// 	'number.empty': 'Calorie value must be number.'
			// }),
			// fat: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Fat value must be number.'
			// }),
			// carbohydrates: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Carbohydrates value must be number.'
			// }),
			// protein: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Protein value must be number.'
			// }),
			// variants_types: Joi.array().items({
			// 	variant_type: Joi.string().required().messages({
			// 		'any.required': 'Variant type is required.',
			// 		'string.empty': 'Variant type is required.'
			// 	}),

			// 	price: Joi.number().required().messages({
			// 		'any.required': 'Price is required.',
			// 		'number.empty': 'Price is required.'
			// 	})
			// 	// Object schema
			// }),
			tags: Joi.array().optional().items(Joi.number()),
			product_tax: Joi.string().valid('gst', 'tax_free').required(),
			extra_items: Joi.array()
				.optional()
				.items(
					Joi.object({
						extra_item: Joi.string().required().messages({
							'any.required': 'Extra item is required.',
							'string.empty': 'Extra item must be string.'
						}),
						price: Joi.number().required().messages({
							'any.required': 'Price is required.',
							'number.empty': 'Price must be number.'
						})
						// Object schema
					})
				),

			product_variant_types: Joi.array()
				.optional()
				.items(
					Joi.object({
						label: Joi.string().required().messages({
							'any.required': 'Label is required.',
							'string.empty': 'Label is required.'
						}),
						serviceType: Joi.string()
							.valid('PICKUP', 'TABLE', 'BOTH')
							.optional()
							.messages({
								'string.empty': 'Service Type is required.',
								'any.only':
									'Service Type must be [Takeaway,Table service,Both].'
							}),
						product_variant_sub_types: Joi.array().items({
							extra_item: Joi.string().required().messages({
								'any.required': 'Extra item is required.',
								'string.empty': 'Extra item is required.'
							}),
							price: Joi.number().required().messages({
								'any.required': 'Price is required.',
								'number.empty': 'Price  is required.'
							})
						})
					})
				),
			food_options: Joi.array().optional().items(Joi.number()),
			product_image: Joi.string().optional()
		});
		return schema.validate(req.body);
	},
	/* Delete Product Validation */
	async deleteProductValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			sub_category_id: Joi.number().required().messages({
				'any.required': 'subCategoryId is required.',
				'number.empty': 'subCategoryId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			})
		});
		return schema.validate(req.body);
	},
	/* Change Status Product Validation */
	async changeStatusProductValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			}),
			status: Joi.string().valid('Active', 'Inactive').required({
				'any.required': 'status is required.',
				'string.empty': 'status is required.'
			})
		});
		return schema.validate(req.body);
	},
	/* Edit Product Validation */
	async editProductValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			category_id: Joi.number().optional().messages({
				'any.required': 'category id is required.',
				'number.empty': 'category id is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required.',
				'number.empty': 'bar id is required.'
			}),
			subcategory_id: Joi.number().optional().messages({
				'any.required': 'subcategory id is required.',
				'number.empty': 'subcategory id is required.'
			}),
			name: Joi.string().optional().messages({
				'any.required': 'name is required.',
				'string.empty': 'name is required.'
			}),
			description: Joi.string().optional().messages({
				'any.required': 'description is required.',
				'string.empty': 'description is required.'
			}),
			price: Joi.number().optional().messages({
				'any.required': 'price  is required.',
				'number.empty': 'price is required.'
			}),
			isDailyStockRenewal: Joi.string().valid('Yes', 'No').optional().messages({
				'any.only': 'Daily Stock renewal must be Yes Or No.'
			}),
			dailyStockRenewal: Joi.number().optional(),
			isStockLimit: Joi.string().valid('Yes', 'No').optional().messages({
				'any.only': 'Daily Stock renewal must be Yes Or No.'
			}),
			stock: Joi.number().optional(),
			serviceType: Joi.string()
				.valid('PICKUP', 'TABLE', 'BOTH')
				.optional()
				.messages({
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be [Takeaway,Table service,Both].'
				}),
			pickuplocation_id: Joi.number().optional().messages({
				'any.required': 'pickupLocation is required.',
				'number.empty': 'pickupLocation id is required.'
			}),
			// calorie: Joi.number().optional().messages({
			// 	'number.empty': "Calorie's value must be number."
			// }),
			// fat: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Fat value must be number.'
			// }),
			// carbohydrates: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Carbohydrates value must be number.'
			// }),
			// protein: Joi.number().optional().allow('null').messages({
			// 	'number.empty': 'Protein value must be number.'
			// }),
			added_tags: Joi.array().optional().items(Joi.number()),
			deleted_tags: Joi.array().optional().items(Joi.number()),
			product_tax: Joi.string().valid('gst', 'tax_free').optional(),
			variants_types: Joi.array()
				.optional()
				.items({
					variant_type: Joi.string().required().messages({
						'any.required': 'varianttype is required.',
						'string.empty': 'varienttype is required.'
					}),
					price: Joi.number().required().messages({
						'any.required': 'price is required.',
						'number.empty': 'price  is required.'
					}),
					id: Joi.string().optional().messages({
						'any.required': 'varianttype is required.',
						'string.empty': 'varienttype is required.'
					})
					// Object schema
				}),
			extra_items: Joi.array()
				.optional()
				.items(
					Joi.object({
						extra_item: Joi.string().required().messages({
							'any.required': 'extraItem is required.',
							'string.empty': 'extraItem is required.'
						}),
						price: Joi.number().required().messages({
							'any.required': 'price is required.',
							'number.empty': 'price  is required.'
						}),
						id: Joi.string().optional().messages({
							'any.required': 'varianttype is required.',
							'string.empty': 'varienttype is required.'
						})
						// Object schema
					})
				),
			product_variant_types: Joi.array()
				.optional()
				.items(
					Joi.object({
						id: Joi.string().optional().messages({
							'any.required': 'id1 is required.',
							'string.empty': 'id1 is required.'
						}),
						label: Joi.string().required().messages({
							'any.required': 'lable is required.',
							'string.empty': 'lable is required.'
						}),
						serviceType: Joi.string()
							.valid('PICKUP', 'TABLE', 'BOTH')
							.optional()
							.messages({
								'string.empty': 'Service Type is required.',
								'any.only':
									'Service Type must be [Takeaway,Table service,Both].'
							}),
						product_variant_sub_types: Joi.array().items({
							id: Joi.string().optional().messages({
								'any.required': 'id1 is required.',
								'string.empty': 'id1 is required.'
							}),
							extra_item: Joi.string().required().messages({
								'any.required': 'extraItem is required.',
								'string.empty': 'extraItem is required.'
							}),
							price: Joi.number().required().messages({
								'any.required': 'price is required.',
								'number.empty': 'price  is required.'
							})
						})
					})
				),

			delete_varient_type: Joi.array().optional(),
			deleted_product_variant_types_ids: Joi.array().optional(),
			deleted_product_variant_sub_types_ids: Joi.array().optional(),
			delete_product_extra: Joi.array().optional(),
			delete_food_option: Joi.array().optional(),
			food_options: Joi.array().optional().items(Joi.number()),
			product_image: Joi.string().optional()
		});
		return schema.validate(req.body);
	},
	//list product validation
	async listProductValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			}),
			serviceType: Joi.string()
				.valid('PICKUP', 'TABLE', 'BOTH')
				.required()
				.messages({
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be [Takeaway,Table service,Both].'
				}),
			search: Joi.string().allow(' ').optional(),
			showPopular: Joi.valid(0, 1).optional()
		});
		return schema.validate(req.body);
	},

	async listProductValidationV2(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			}),
			serviceType: Joi.string()
				.valid('PICKUP', 'TABLE', 'BOTH')
				.required()
				.messages({
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be [Takeaway,Table service,Both].'
				}),
			// search: Joi.string().allow(' ').optional(),
			// showPopular: Joi.valid(0, 1).optional(),
			sub_cat_id: Joi.number().required(),
			page: Joi.number().optional(),
			limit: Joi.number().optional()
		});
		return schema.validate(req.body);
	},

	async deleteProductVariantValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			product_id: Joi.number().required().messages({
				'any.required': 'productId is required.',
				'number.empty': 'productId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			})
		});
		return schema.validate(req.body);
	},
	async deleteProductExtrasValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			product_id: Joi.number().required().messages({
				'any.required': 'productId is required.',
				'number.empty': 'productId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getSingleProductValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required.',
				'number.empty': 'id is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getItemActiveHoursValidation(req) {
		const schema = Joi.object({
			sub_category_id: Joi.number().required().messages({
				'any.required': 'subCategoryId is required.',
				'number.empty': 'subCategoryId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			})
		});
		return schema.validate(req.body);
	},
	async addUpdateItemActiveHoursValidation(req) {
		const schema = Joi.object({
			sub_category_id: Joi.number().required().messages({
				'any.required': 'subCategoryId is required.',
				'number.empty': 'subCategoryId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			}),
			id: Joi.number().optional().allow(' '),
			active_hours: Joi.string().required().messages({
				'any.required': 'active hours is required.',
				'string.empty': 'active hours is required.'
			}),
			in_active_hours: Joi.string().required().messages({
				'any.required': 'inactive hours is required.',
				'string.empty': 'inactive hours  is required.'
			}),
			status: Joi.string().required().valid('0', '1'),
			week_day: Joi.string().required().messages({
				'any.required': 'inactive hours is required.',
				'string.empty': 'inactive hours  is required.'
			})
		});
		return schema.validate(req.body);
	},
	async productSequenceValidation(req) {
		const schema = Joi.object({
			sub_category_id: Joi.number().required().messages({
				'any.required': 'subCategoryId is required.',
				'number.empty': 'subCategoryId is required.'
			}),
			category_id: Joi.number().required().messages({
				'any.required': 'categoryId is required.',
				'number.empty': 'categoryId is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'barId is required.',
				'number.empty': 'barId is required.'
			}),
			product_ids: Joi.array().required().messages({
				'any.required': 'productIds is required.',
				'number.empty': 'productIds is required.'
			})
		});
		return schema.validate(req.body);
	}
};
