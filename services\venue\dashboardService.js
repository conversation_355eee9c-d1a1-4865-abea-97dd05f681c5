require('dotenv').config();
const orderModel = require('../../database/models').orders;
const orderItemsModel = require('../../database/models').order_items;
const operatingHoursModel = require('../../database/models').operating_hour;
const productModel = require('../../database/models').product;
const barModel = require('../../database/models').bar; //import modal always Capital
const convertHours = require('../../common/commonFunction').convertHours;
const moment = require('moment');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
module.exports = {
	async getVenueStatistics(req, res) {
		try {
			let orderWhere = {
				barID: req.body.bar_id,
				// isCanceled: 'No',
				paymentStatus: 'received',
				isDeleted: 'No',
				// refundStatus: { [Op.not]: ['Refunded'] }
			};
			let barDetails = await barModel.findOne({
				where: {
					id: req.body.bar_id
				},
				attributes: ['timezone']
			});

			let data = await orderModel.findAll({
				where: orderWhere,
				attributes: [
					[
						Sequelize.fn(
							'COUNT',
							Sequelize.fn('DISTINCT', Sequelize.col('userID'))
						),
						'total_user'
					],
					[Sequelize.literal('round(sum(total),2)'), 'amount'],
					[Sequelize.fn('COUNT', Sequelize.col('id')), 'total_order']
				]
			});

			//mininus partialy refunded amount from total gross sales
			let totalAmount = await orderModel.findOne({
				where: {
					barID: req.body.bar_id,
					paymentStatus: 'received'
				},
				attributes: [[Sequelize.literal('round(sum(total),2)'), 'amount']]
			});

			//get Total User Count
			let totalUserCount = await orderModel.findOne({
				where: {
					barID: req.body.bar_id
				},
				attributes: [
					[
						Sequelize.fn(
							'COUNT',
							Sequelize.fn('DISTINCT', Sequelize.col('userID'))
						),
						'total_user'
					]
				]
			});

			data[0].dataValues.amount = totalAmount?.dataValues?.amount;
			data[0].dataValues.total_user = totalUserCount?.dataValues?.total_user;
			const timezone = barDetails.dataValues.timezone;			
			return {list:data,timezone};
		} catch (err) {
			console.log('error', err);
			throw err;
		}
	},
	async getVenueStatisticsNew(req, res) {
		try {
			const currentDate = new Date();
			const currentMonthStart = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth(),
				1
			);
			const currentMonthEnd = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() + 1,
				0
			);

			// Calculate previous month start and end dates
			const previousMonthStart = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() - 1,
				1
			);
			const previousMonthEnd = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth(),
				0
			);

			let currentMonthData = await orderModel.findAll({
				where: {
					barID: req.body.bar_id,
					isCanceled: 'No',
					paymentStatus: 'received',
					isDeleted: 'No',
					refundStatus: { [Op.not]: ['Refunded'] },
					orderDate: {
						[Op.between]: [currentMonthStart, currentMonthEnd]
					}
				},
				attributes: [
					[
						Sequelize.fn(
							'COUNT',
							Sequelize.fn('DISTINCT', Sequelize.col('userID'))
						),
						'total_user'
					],
					[Sequelize.fn('COUNT', Sequelize.col('id')), 'total_order']
				]
			});
			let lastMonthData = await orderModel.findAll({
				where: {
					barID: req.body.bar_id,
					isCanceled: 'No',
					paymentStatus: 'received',
					isDeleted: 'No',
					refundStatus: { [Op.not]: ['Refunded'] },
					orderDate: {
						[Op.between]: [previousMonthStart, previousMonthEnd]
					}
				},
				attributes: [
					[
						Sequelize.fn(
							'COUNT',
							Sequelize.fn('DISTINCT', Sequelize.col('userID'))
						),
						'total_user'
					],
					[Sequelize.fn('COUNT', Sequelize.col('id')), 'total_order']
				]
			});

			//mininus partialy refunded amount from total gross sales
			let currentMonthTotalAmountData = await orderModel.findOne({
				where: {
					barID: req.body.bar_id,
					paymentStatus: 'received',
					isDeleted: 'No',
					isCanceled: 'No',
					orderDate: {
						[Op.between]: [currentMonthStart, currentMonthEnd]
					}
				},
				attributes: [[Sequelize.literal('round(sum(total),2)'), 'amount']]
			});

			let lastMonthTotalAmountData = await orderModel.findOne({
				where: {
					barID: req.body.bar_id,
					paymentStatus: 'received',
					isDeleted: 'No',
					isCanceled: 'No',
					orderDate: {
						[Op.between]: [previousMonthStart, previousMonthEnd]
					}
				},
				attributes: [[Sequelize.literal('round(sum(total),2)'), 'amount']]
			});

			// Sequelize query to find average spend per customer
			let avgSpendPerCustomerCurrentMonthData = await orderModel.findAll({
				where: {
					barID: req.body.bar_id,
					paymentStatus: 'received',
					isDeleted: 'No',
					isCanceled: 'No',
					orderDate: {
						[Op.between]: [currentMonthStart, currentMonthEnd]
					}
				},
				attributes: [
					'userID',
					[
						Sequelize.fn('AVG', Sequelize.literal('round(total,2)')),
						'avgAmount'
					]
				],
				group: ['userID']
			});

			let avgCustomerSpendCurrentMonth = 0;
			if (avgSpendPerCustomerCurrentMonthData.length > 0) {
				avgSpendPerCustomerCurrentMonthData.forEach((item) => {
					avgCustomerSpendCurrentMonth =
						avgCustomerSpendCurrentMonth + item.dataValues.avgAmount;
				});
				avgCustomerSpendCurrentMonth =
					avgCustomerSpendCurrentMonth /
					avgSpendPerCustomerCurrentMonthData.length;
			}

			let avgSpendPerCustomerLastMonthData = await orderModel.findAll({
				where: {
					barID: req.body.bar_id,
					paymentStatus: 'received',
					isDeleted: 'No',
					isCanceled: 'No',
					orderDate: {
						[Op.between]: [previousMonthStart, previousMonthEnd]
					}
				},
				attributes: [
					'userID',
					[
						Sequelize.fn('AVG', Sequelize.literal('round(total,2)')),
						'avgAmount'
					]
				],
				group: ['userID']
			});

			let avgCustomerSpendLastMonth = 0;
			if (avgSpendPerCustomerLastMonthData.length > 0) {
				avgSpendPerCustomerLastMonthData.forEach((item) => {
					avgCustomerSpendLastMonth =
						avgCustomerSpendLastMonth + item.dataValues.avgAmount;
				});
				avgCustomerSpendLastMonth =
					avgCustomerSpendLastMonth / avgSpendPerCustomerLastMonthData.length;
			}

			const currentMonthTotalUser =
				currentMonthData.length > 0
					? currentMonthData[0].dataValues.total_user || 0
					: 0;
			const lastMonthTotalUser =
				lastMonthData.length > 0
					? lastMonthData[0].dataValues.total_user || 0
					: 0;
			let percentageChangeTotalUser;
			if (lastMonthTotalUser !== 0) {
				percentageChangeTotalUser = (
					((currentMonthTotalUser - lastMonthTotalUser) / lastMonthTotalUser) *
					100
				).toFixed(2);
			} else {
				percentageChangeTotalUser =
					currentMonthTotalUser === 0 ? '0.00' : '100.00';
			}

			const currentMonthTotalOrder =
				currentMonthData.length > 0
					? currentMonthData[0].dataValues.total_order || 0
					: 0;
			const lastMonthTotalOrder =
				lastMonthData.length > 0
					? lastMonthData[0].dataValues.total_order || 0
					: 0;
			let percentageChangeTotalOrder;
			if (lastMonthTotalOrder !== 0) {
				percentageChangeTotalOrder = (
					((currentMonthTotalOrder - lastMonthTotalOrder) /
						lastMonthTotalOrder) *
					100
				).toFixed(2);
			} else {
				percentageChangeTotalOrder =
					currentMonthTotalOrder === 0 ? '0.00' : '100.00';
			}

			const currentMonthTotalAmount =
				currentMonthTotalAmountData.dataValues.amount || 0;
			const lastMonthTotalAmount =
				lastMonthTotalAmountData.dataValues.amount || 0;

			let percentageChangeTotalAmount;
			if (lastMonthTotalAmount !== 0) {
				percentageChangeTotalAmount = (
					((currentMonthTotalAmount - lastMonthTotalAmount) /
						lastMonthTotalAmount) *
					100
				).toFixed(2);
			} else {
				percentageChangeTotalAmount =
					currentMonthTotalAmount === 0 ? '0.00' : '100.00';
			}

			let percentageChangeAvgSpendCustomer;
			if (avgCustomerSpendLastMonth !== 0) {
				percentageChangeAvgSpendCustomer = (
					((avgCustomerSpendCurrentMonth - avgCustomerSpendLastMonth) /
						avgCustomerSpendLastMonth) *
					100
				).toFixed(2);
			} else {
				percentageChangeAvgSpendCustomer =
					avgCustomerSpendCurrentMonth === 0 ? '0.00' : '100.00';
			}

			return {
				total_user: Number(currentMonthTotalUser.toFixed(2)),
				// last_total_user: Number(lastMonthTotalUser.toFixed(2)),
				total_user_percentage: Number(percentageChangeTotalUser),
				total_order: Number(currentMonthTotalOrder.toFixed(2)),
				// last_total_order: Number(lastMonthTotalOrder.toFixed(2)),
				total_order_percentage: Number(percentageChangeTotalOrder),
				total_amount: Number(currentMonthTotalAmount.toFixed(2)),
				// last_total_amount: Number(lastMonthTotalAmount.toFixed(2)),
				total_amount_percentage: Number(percentageChangeTotalAmount),
				avg_spend: Number(avgCustomerSpendCurrentMonth.toFixed(2)),
				// last_avg_spend: Number(avgCustomerSpendLastMonth.toFixed(2)),
				avg_spend_percentage: Number(percentageChangeAvgSpendCustomer)
			};
		} catch (err) {
			console.log('error', err);
			throw err;
		}
	},

	async serviceTypeByPercentage(req, res) {
		let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');
		let tableCount = await orderModel.count({
			where: {
				orderServiceType: 'TABLE',
				barID: req.body.bar_id,
				convertedOrderDate: todayDate,
				isCanceled: 'No',
				paymentStatus: 'received',
				isDeleted: 'No',
				refundStatus: { [Op.not]: ['Refunded'] }
			}
		});
		let pickupCount = await orderModel.count({
			where: {
				orderServiceType: 'PICKUP',
				barID: req.body.bar_id,
				convertedOrderDate: todayDate,
				isCanceled: 'No',
				paymentStatus: 'received',
				isDeleted: 'No',
				refundStatus: { [Op.not]: ['Refunded'] }
			}
		});

		let tablePercentage = (tableCount * 100) / (tableCount + pickupCount);
		let takeawayPercentage = (pickupCount * 100) / (tableCount + pickupCount);
		data = [
			{
				name: 'Table Service',
				value: tablePercentage ? tablePercentage.toFixed(2) : 0
			},
			{
				name: 'Takeaway',
				value: takeawayPercentage ? takeawayPercentage.toFixed(2) : 0
			}
		];

		return data;
	},
	async ordersPerHours(req, res) {
		try {
			let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');

			let data = await orderModel.findAll({
				where: {
					convertedOrderDate: todayDate,
					barID: req.body.bar_id,
					isCanceled: 'No',
					paymentStatus: 'received',
					refundStatus: { [Op.not]: ['Refunded'] }
				},
				attributes: [
					'id',
					[
						Sequelize.literal('extract(HOUR FROM `orders`.`createdAt`)'),
						'hours'
					],
					// 'total',
					[Sequelize.fn('SUM', Sequelize.col('total')), 'total_count']
				],
				group: ['hours']
			});

			//convert hours utc to aus timezone
			data.map((value, index) => {
				let convertedHours = convertHours(value.dataValues.hours);
				data[index].dataValues.hours = convertedHours;
			});

			//today's day in aus
			let today = moment().tz('Australia/Perth').day();
			let operating_hour = await operatingHoursModel.findOne({
				where: {
					barID: req.body.bar_id,
					weekDay: today - 1
				}
			});
			//find starting and ending hours
			let starting_hours;
			let ending_hours;

			let starting_time = operating_hour.openingHours;
			starting_hours = Number(starting_time.split(':')[0]);

			let closing_time = operating_hour.closingHours;
			ending_hours = Number(closing_time.split(':')[0]);
			let currentHoursInAus = moment().tz('Australia/Perth').hours();

			if (currentHoursInAus > ending_hours) {
				ending_hours = operating_hour.closingHours;
			}
			if (currentHoursInAus < ending_hours) {
				ending_hours = currentHoursInAus;
			}

			let outputData = [];

			//bar not started yet
			if (ending_hours < starting_hours) {
				return outputData;
			}

			for (let x = starting_hours; x <= ending_hours; x++) {
				let isNotOrderPlaced = true;
				data.map((currentdata) => {
					if (currentdata.dataValues.hours == x) {
						outputData.push({
							time: x,
							count: Number(currentdata.dataValues.total_count).toFixed(2)
						});
						isNotOrderPlaced = false;
					}
				});

				//if not any order placed during this hour so add revenue 0 for this hour
				if (isNotOrderPlaced) {
					outputData.push({
						time: `${x}`,
						count: 0
					});
				}
			}

			return outputData;
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	async serviceTypeByRevenue(req, res) {
		try {
			let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');
			//find revenue by table
			let todayTableRevenue = 0;
			let tableRevenue = await orderModel.findAndCountAll({
				where: {
					orderServiceType: 'TABLE',
					barID: req.body.bar_id,
					convertedOrderDate: todayDate,
					isCanceled: 'No',
					paymentStatus: 'received',
					isDeleted: 'No',
					refundStatus: { [Op.not]: ['Refunded'] }
				},
				attributes: [[Sequelize.literal('round(sum( total),2)'), 'totalsum']]
			});

			todayTableRevenue = tableRevenue?.rows[0]?.dataValues?.totalsum ?? 0;

			//mininus partialy refunded amount from total revenue
			// let refundAmountTable = 0;
			// let refundedAmountforTable = await orderItemsModel.findAll({
			// 	where: {
			// 		isDeleted: 'No'
			// 	},
			// 	include: [
			// 		{
			// 			require: true,
			// 			model: orderModel,
			// 			where: {
			// 				refundStatus: 'PartialRefunded',
			// 				barID: req.body.bar_id,
			// 				orderDate: todayDate,
			// 				orderServiceType: 'TABLE',
			// 				isDeleted: 'No'
			// 			}
			// 		}
			// 	],

			// 	attributes: [[Sequelize.literal('sum(refundAmount)'), 'refundedAmount']]
			// });
			// refundAmountTable =
			// 	refundedAmountforTable[0]?.dataValues?.refundedAmount ?? 0;

			//find pickup revenue
			let todayPickupRevenue = 0;
			let pickupRevenue = await orderModel.findAndCountAll({
				where: {
					orderServiceType: 'PICKUP',
					barID: req.body.bar_id,
					convertedOrderDate: todayDate,
					isCanceled: 'No',
					paymentStatus: 'received',
					isDeleted: 'No',
					refundStatus: { [Op.not]: ['Refunded'] }
				},
				attributes: [[Sequelize.literal('round(sum( total),2)'), 'totalsum']]
			});

			todayPickupRevenue = pickupRevenue?.rows[0]?.dataValues?.totalsum ?? 0;

			//mininus partialy refunded amount from total revenue
			// let refundAmountPickup = 0;
			// let refundedAmountforPickup = await orderItemsModel.findAll({
			// 	where: {
			// 		isDeleted: 'No'
			// 	},
			// 	include: [
			// 		{
			// 			require: true,
			// 			model: orderModel,
			// 			where: {
			// 				refundStatus: 'PartialRefunded',
			// 				barID: req.body.bar_id,
			// 				orderDate: todayDate,
			// 				orderServiceType: 'PICKUP',
			// 				isDeleted: 'No'
			// 			}
			// 		}
			// 	],

			// 	attributes: [[Sequelize.literal('sum(refundAmount)'), 'refundedAmount']]
			// });

			// refundAmountPickup =
			// 	refundedAmountforPickup[0]?.dataValues?.refundedAmount ?? 0;

			data = [
				{
					name: 'Table Service',
					value: todayTableRevenue.toFixed(2)
				},
				{
					name: 'Takeaway',
					value: todayPickupRevenue.toFixed(2)
				}
			];

			return data;
		} catch (err) {
			console.log('error', err);
			throw err;
		}
	},

	async customerCountByTime(req, res) {
		try {
			let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');
			let data = await orderModel.findAll({
				where: {
					convertedOrderDate: todayDate,
					barID: req.body.bar_id,
					isCanceled: 'No',
					paymentStatus: 'received'
				},
				attributes: [
					'id',
					[
						Sequelize.literal('extract(HOUR FROM `orders`.`createdAt`)'),
						'hours'
					],
					[
						Sequelize.fn(
							'COUNT',
							Sequelize.fn('DISTINCT', Sequelize.col('userID'))
						),
						'total_user'
					]
				],
				group: ['hours']
			});

			//covert utc hours to aus hours
			data.map((value, index) => {
				let convertedHours = convertHours(value.dataValues.hours);
				data[index].dataValues.hours = convertedHours;
			});

			//today's day
			let today = moment().tz('Australia/Perth').day();
			let operating_hour = await operatingHoursModel.findOne({
				where: {
					barID: req.body.bar_id,
					weekDay: today - 1
				}
			});

			//find starting and ending hours
			let starting_hours;
			let ending_hours;

			let starting_time = operating_hour.openingHours;
			starting_hours = Number(starting_time.split(':')[0]);
			let closing_time = operating_hour.closingHours;
			ending_hours = Number(closing_time.split(':')[0]);
			let currentHoursInAus = moment().tz('Australia/Perth').hours();

			if (currentHoursInAus > ending_hours) {
				ending_hours = operating_hour.closingHours;
			}
			if (currentHoursInAus < ending_hours) {
				ending_hours = currentHoursInAus;
			}

			let outputData = [];

			//bar not started yet
			if (ending_hours < starting_hours) {
				return outputData;
			}

			for (let x = starting_hours; x <= ending_hours; x++) {
				let isNotOrderPlaced = true;
				data.map((currentdata) => {
					if (currentdata.dataValues.hours == x) {
						outputData.push({
							time: x,
							count: currentdata.dataValues.total_user
						});
						isNotOrderPlaced = false;
					}
				});

				//if not any order placed during this hour so add revenue 0 for this hour
				if (isNotOrderPlaced) {
					outputData.push({
						time: `${x}`,
						count: 0
					});
				}
			}
			return outputData;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	async mostOrderdItems(req, res) {
		let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');
		let data = await orderItemsModel.findAll({
			where: {
				isCanceled: 'No',
				isDeleted: 'No'
			},
			include: [
				{
					model: productModel,
					attributes: [],
					required: true
				},
				{
					required: true,
					model: orderModel,
					where: {
						convertedOrderDate: todayDate,
						isCanceled: 'No',
						paymentStatus: 'received',
						barID: req.body.bar_id,
						isDeleted: 'No'
					},
					attributes: []
				}
			],
			attributes: [
				'productID',
				[Sequelize.fn('sum', Sequelize.col('quantity')), 'soldQunitity'],
				[Sequelize.literal(`product.name`), 'productName']
			],
			group: ['productID'],
			order: Sequelize.literal('soldQunitity DESC'),
			limit: 5
		});

		return data;
	},
	async totalRevenue(req, res) {
		let todayDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');

		let orderWhere = {
			barID: req.body.bar_id,
			isCanceled: 'No',
			paymentStatus: 'received',
			isDeleted: 'No',
			refundStatus: { [Op.not]: ['Refunded'] },
			convertedOrderDate: todayDate
		};

		let data = await orderModel.findAll({
			where: orderWhere,
			attributes: [[Sequelize.literal('round(sum(total),2)'), 'amount']]
		});

		//mininus partialy refunded amount from total gross sales
		let refundedAmount = await orderItemsModel.findAll({
			where: {
				isDeleted: 'No'
			},
			include: [
				{
					require: true,
					model: orderModel,
					where: {
						refundStatus: 'PartialRefunded',
						barID: req.body.bar_id,
						isDeleted: 'No',
						isCanceled: 'No',
						paymentStatus: 'received',
						convertedOrderDate: todayDate
					}
				}
			],

			attributes: [[Sequelize.literal('sum(refundAmount)'), 'refundedAmount']]
		});

		if (refundedAmount[0].dataValues.refundedAmount) {
			data[0].dataValues.amount =
				data[0].dataValues.amount - refundedAmount[0].dataValues.refundedAmount;
		}
		if (data[0].dataValues.amount == null) {
			data[0].dataValues.amount = 0;
		}
		return data;
	}
};
