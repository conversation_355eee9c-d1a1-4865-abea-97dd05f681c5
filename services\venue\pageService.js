/*Messages,status code and services require*/
require('dotenv').config();
const constant = require('../../config/constant');
const commonFunction = require('../../common/commonFunction');
const helper = require('../../helper/generalHelper');
const Sequelize = require('sequelize');
const pageModel = require('../../database/models').pages;

module.exports = {
	async getVenueDetails(req, res) {
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id
				},
				include: [
					{
						model: OperatingHoursModel,
						attributes: [	
							'id',
							'weekDay',
							'openingHours',
							'closingHours',
							'isClosed'
						]
					}
				],
				order: [[Sequelize.literal('weekDay'), 'ASC']]
			});
			if (barDetails) {
				return barDetails;
			}
			return 0;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async terms(req, res) {
		try {
			let content = await pageModel.findOne({
				attributes: ['title', 'content'],
				where: {
					id: 3
				}
			});
			if (content) {
				return content;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async barPrivacyPolicy(req, res) {
		try {
			let content = await pageModel.findOne({
				attributes: ['title', 'content'],
				where: {
					id: 5
				}
			});
			if (content) {
				return content;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async faq(req, res) {
		try {
			let content = await pageModel.findOne({
				attributes: ['title', 'content'],
				where: {
					id: 2
				}
			});
			if (content) {
				return content;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async contactUs(req, res) {
		let name = req.body.name;
		let email = req.body.email;
		let message = req.body.message;
		try {
			let subject = 'MyTab Contact ';
			var mailbody =
				'<p><img src="' +
				// env.logo +
				'" title="MyTab" style="width: 100px;"/></p><p><strong>Hi,</strong></p><p>Name : ' +
				name +
				'</p><p>Email : <a href="mailto:' +
				email +
				'">' +
				email +
				'</a></p><p>Message : ' +
				message +
				'</p><p>Thanks,<br/>MyTab Team</p>';

			let data = await mail.sendmail(
				res,
				constant.BAREMAILTO,
				subject,
				mailbody
			);
			if (data) {
				return 1;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
};
