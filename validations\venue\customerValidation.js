const Joi = require('joi');

module.exports = {
	async getCustomerListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			sortBy: Joi.string()
				.trim()
				.optional()
				.valid(
					'newToOld',
					'oldToNew',
					'highestAvgOrderTotal',
					'lowestAvgOrderTotal',
					'highestOrderTotalCount',
					'lowestOrderTotalCount',
					'highestTotalOrderSpent',
					'lowestTotalOrderSpent',
					'highestSpent',
					'lowestSpent'
				)
				.messages({
					'string.base': 'Sort by must be a string'
				}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow(''),
			page: Joi.number().required().messages({
				'any.required': 'Page no is required',
				'number.base': 'Page no must be number'
			}),
			isDiscount: Joi.boolean()
		});
		return schema.validate(req.body);
	},
	async getCustomerDetailsValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			user_id: Joi.number().required().messages({
				'any.required': 'User id is required',
				'number.base': 'User id must be number'
			})
		});
		return schema.validate(req.body);
	},
	async getOrderListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			user_id: Joi.number().required().messages({
				'any.required': 'User id is required',
				'number.base': 'User id must be number'
			}),
			sortBy: Joi.string()
				.trim()
				.optional()
				.valid('newToOld', 'oldToNew', 'highestSpent', 'lowestSpent')
				.messages({
					'string.base': 'Sort by must be a string'
				}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow(''),
			page: Joi.number().required().messages({
				'any.required': 'Page no is required',
				'number.base': 'Page no must be number'
			})
		});
		return schema.validate(req.body);
	},
	async getSegmentListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			user_id: Joi.number().required().messages({
				'any.required': 'User id is required',
				'number.base': 'User id must be number'
			}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow(''),
			page: Joi.number().required().messages({
				'any.required': 'Page no is required',
				'number.base': 'Page no must be number'
			})
		});
		return schema.validate(req.body);
	},
	async downloadCustomerValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			type: Joi.number().optional().valid(1, 2, 3).messages({
				'number.base': 'Type must be number'
			}),
			userTypeFilter: Joi.number()
				.when('type', {
					is: 1,
					then: Joi.required().valid(1, 0),
					otherwise: Joi.optional()
				})
				.messages({
					'any.required': 'User Type Filter is required.'
				}),
			startDate: Joi.date()
				.when('type', {
					is: 1,
					then: Joi.required(),
					otherwise: Joi.optional()
				})
				.messages({
					'any.required': 'Start date is required.'
				}),
			endDate: Joi.date()
				.when('type', {
					is: 1,
					then: Joi.required(),
					otherwise: Joi.optional()
				})
				.messages({
					'any.required': 'End date is required.'
				}),
			page: Joi.number()
				.when('type', {
					is: 2,
					then: Joi.required(),
					otherwise: Joi.optional()
				})
				.messages({
					'any.required': 'Page no is required.'
				}),
			sortBy: Joi.string()
				.trim()
				.optional()
				.valid(
					'newToOld',
					'oldToNew',
					'highestAvgOrderTotal',
					'lowestAvgOrderTotal',
					'highestTotalOrderSpent',
					'lowestTotalOrderSpent',
					'highestSpent',
					'lowestSpent'
				)
				.messages({
					'string.base': 'Sort by must be a string'
				}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow(''),
			customerIds: Joi.array()
				.items(Joi.number().integer())
				.when('type', {
					is: 3,
					then: Joi.array()
						.items(Joi.number().integer())
						.min(1)
						.required()
						.messages({
							'any.required':
								'Customer IDs are required when type is selected customer.',
							'array.min': 'At least one customer ID must be provided.'
						}),
					otherwise: Joi.array().items(Joi.number().integer()).messages({
						'array.base': 'Customer IDs must be an array of integers.'
					})
				})
				.messages({
					'any.required': 'Customer Ids is required.'
				})
		});
		return schema.validate(req.body);
	}
};
