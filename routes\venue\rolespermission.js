var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/venueAuth").venueAuthorization;

/*require for security */
const RolePermissionController = require("../../controllers/venue/rolesPermissionController");
/* List Permission API */
router.get(
  "/permission-list",
  Authorization,
  RolePermissionController.listPermission
);

/* List Role With Permission API */
router.post(
  "/list",
  Authorization,
  RolePermissionController.listRolePermission
);

/* Add Role Permission API */
router.post("/add", Authorization, RolePermissionController.addRolePermission);

/* Update Role Permission API */
router.post(
  "/update",
  Authorization,
  RolePermissionController.updateRolePermission
);

/* Delete Role Permission API */
router.delete(
  "/delete",
  Authorization,
  RolePermissionController.deleteRolePermission
);

/* Status change Role API */
router.post(
  "/status-change",
  Authorization,
  RolePermissionController.changeStatusRolePermission
);

module.exports = router;
