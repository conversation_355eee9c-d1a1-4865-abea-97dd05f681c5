'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class bar_sub_category_wait_time extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			bar_sub_category_wait_time.belongsTo(models.sub_category, {
				foreignKey: 'subCategoryID'
			});
			models.sub_category.hasMany(bar_sub_category_wait_time, {
				foreignKey: 'subCategoryID'
			});

			bar_sub_category_wait_time.belongsTo(models.bar, { foreignKey: 'barID' });
			models.bar.hasMany(bar_sub_category_wait_time, { foreignKey: 'barID' });
			// define association here
		}
	}
	bar_sub_category_wait_time.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			barSubCategoryOpeningHoursID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar_sub_category_opening_hours',
					key: 'id'
				}
			},
			waitTimeType: DataTypes.ENUM('1', '2'),
			weekDay: DataTypes.SMALLINT,
			startTime: DataTypes.TIME,
			endTime: DataTypes.TIME,
			waitTime: DataTypes.TIME
		},
		{
			sequelize,
			modelName: 'bar_sub_category_wait_time',
			timestamps: true,
			freezeTableName: true
		}
	);
	return bar_sub_category_wait_time;
};
