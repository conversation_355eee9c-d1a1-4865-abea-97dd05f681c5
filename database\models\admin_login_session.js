"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class admin_login_session extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      admin_login_session.belongsTo(models.admin, {
        foreignKey: "admin_id",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  admin_login_session.init(
    {
      id: {
        type: DataTypes.INTEGER(12),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      admin_id: {
        type: DataTypes.INTEGER(12),
        allowNull: false,
        references: {
          model: "admin",
          key: "id",
        },
      },
      device_token: {
        type: DataTypes.STRING,
      },
      access_token: {
        type: DataTypes.STRING(512),
        allowNull: false,
      },
      device_type: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      device_token: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      device_name: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      device_location: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: "admin_login_session",
      timestamps: true,
    }
  );
  return admin_login_session;
};
