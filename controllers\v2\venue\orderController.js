const orderService = require('../../../services/v2/venue/orderService');
const orderValidation = require('../../../validations/venue/orderValidation');

const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;

module.exports = {
	/* Bar Order history */
	async barOrderHistory(req, res) {
		try {
			// validation
			const valid = await orderValidation.barOrderHistoryValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let orderHistoryList = await orderService.barOrderHistory(req, res);

			return response(
				res,
				status.SUCCESSSTATUS,
				orderHistoryList,
				message.ORDER_HISTORY_LIST_SUCCESSFULL,
				status.SUCCESS
			);
		} catch (error) {
			//response on internal server error
			console.log(error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async barOrderList(req, res) {
			try {
				// validation
				const valid = await orderValidation.barOrderListValidation(req);
				if (valid.error) {
					return response(
						res,
						status.BADREQUESTCODE,
						{},
						valid.error.details[0].message,
						status.ERROR
					);
				}
	
				let orderList = await orderService.barOrderList(req, res);
	
				return response(
					res,
					status.SUCCESSSTATUS,
					orderList,
					message.ORDER_HISTORY_LIST_SUCCESSFULL,
					status.SUCCESS
				);
			} catch (error) {
				//response on internal server error
				console.log(error);
				return response(
					res,
					status.INTERNALSERVERERRORSTATUS,
					[],
					message.INTERNALSERVERERROR,
					status.ERROR
				);
			}
		},
};
