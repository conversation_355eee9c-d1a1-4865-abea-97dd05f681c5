const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const campaignService = require("../../services/advertiser/campaignService");

const campaignValidation = require('../../validations/advertiser/campaignValidation');

module.exports = {
    /* Register */
    async addCampaign(req, res) {
        try {
            const valid = await campaignValidation.addCampaingValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }

            let campaignAdd = await campaignService.add(req, res);

            if (campaignAdd === 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNALEREADYEXIST,
                    status.ERROR
                );
            } else if (campaignAdd) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNSUCCESS,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* get campaignList */
    async getCampaignList(req, res) {
        try {
            const valid = await campaignValidation.getCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let campaignList = await campaignService.getCampaignList(req, res);
            if (campaignList == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (campaignList) {
                //response on old password mis-match
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    campaignList,
                    message.CAMPAIGNLIST,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* get all campaignList */
    async getAllCampaignList(req, res) {
        try {
            const valid = await campaignValidation.getAllCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            
            // No type validation needed for this endpoint
            let campaignList = await campaignService.getAllCampaignList(req);
            
            if (campaignList == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (campaignList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    campaignList,
                    message.CAMPAIGNLIST,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log("Error in getAllCampaignList:", error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
};
