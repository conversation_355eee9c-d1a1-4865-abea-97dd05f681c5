
// Validate ABN number
validateABN = (abn) => {

    if (abn.length != 11 || isNaN(parseInt(abn))) return false
  
    let weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
    let firstDigitProcessed = parseInt(abn.charAt(0)) -1 
    let weighted = firstDigitProcessed * weighting[0]
    
    for (var i = 1; i < abn.length; i++) {
      weighted += (parseInt(abn.charAt(i)) * weighting[i])
    }
  
    return (weighted % 89) == 0
  };
  
  // Validate ACN number
  validateACN = (acn) => {
  
    if (acn.length != 9 || isNaN(parseInt(acn))) return false
  
    var weighting = [8, 7, 6, 5, 4, 3, 2, 1]
    var weighted = 0
    for (var i = 0; i < weighting.length; i++) {
      weighted += (parseInt(acn.charAt(i)) * weighting[i]);
    }
    let checkDigit = 10 - (weighted % 10)
    checkDigit = checkDigit == 10 ? 0 : checkDigit
    return checkDigit == acn[8]
  };
 module.exports.validateBusinessRegisterid=(businessRegisterId)=>{

     if (!validateABN(businessRegisterId) && !validateACN(businessRegisterId)) {
         return true;
       }
       return false;
 }