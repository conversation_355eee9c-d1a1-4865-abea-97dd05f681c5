const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;
const securityService = require("../../services/cms/securityService");
const securityValidation = require("../../validations/cms/securityValidation");

module.exports = {
  /* Change Password */
  async changePassword(req, res) {
    try {
      // validation
      const valid = await securityValidation.changePasswordValidation(req);
      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let change_password = await securityService.changePassword(req, res);
      if (change_password == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.PASSWORDCHANGED,
          status.SUCCESS
        );
      } else if (change_password == 2) {
        //response on old password mis-match
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.INCORRECTCURRENTPASSWORD,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.EMAILNOTFOUND,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /* Change Password Date */
  async changePasswordDate(req, res) {
    try {
      let changePasswordDate = await securityService.changePasswordDate(
        req,
        res
      );
      if (changePasswordDate) {
        return response(
          res,
          status.SUCCESSSTATUS,
          changePasswordDate,
          message.PASSWORDCHANGEDDATE,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.NOTFOUND,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /* get device list */
  async getDeviceList(req, res) {
    try {
      let tokenList = await securityService.getDeviceList(req, res);
      if (tokenList == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.NOTFOUND,
          status.SUCCESS
        );
      } else if (tokenList) {
        return response(
          res,
          status.SUCCESSSTATUS,
          tokenList,
          message.TOKENLIST,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Logout Device */
  async logoutDevice(req, res) {
    try {
      // validation
      const valid = await securityValidation.logoutDeviceValidation(req);
      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let logout = await securityService.logoutDevice(req, res);

      if (logout) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.LOGOUTSUCCESS,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          message.NOTFOUND,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
};
