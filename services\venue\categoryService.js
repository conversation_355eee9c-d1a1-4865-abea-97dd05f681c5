const CategoryModel = require('../../database/models').category;
const SubCategoryModel = require('../../database/models').sub_category;
const BarModel = require('../../database/models').bar;
const BarCategorySequenceModel =
	require('../../database/models').bar_category_sequence;
const PickupLocationSubCategoryModel =
	require('../../database/models').pickup_location_sub_category;
const PickupLocation = require('../../database/models').pickup_location;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const moment = require('moment');
const ProductModel = require('../../database/models').product;
const productService = require('./productService');

module.exports = {
	/* Change Password */
	async getCategory() {
		try {
			let Categorydata = await CategoryModel.findAll({
				attributes: ['id', 'name'],
				where: {
					isDeleted: 'No'
				}
			});
			return Categorydata;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	/* get sub category */
	async getSubCategory(req, res) {
		const barData = await BarModel.findOne({
			attributes: [
				'id',
				'restaurantName',
				'managerName',
				'email',
				'countryCode',
				'mobile',
				'stripeID',
				'venueId',
				'attachedPosConfig',
				'docketCommission',
				'posStatus'
			],
			where: {
				id: req.body.bar_id
			}
		});
		var whereClause = [];

		whereClause.push({
			isDeleted: 'No'
		});

		if (barData.dataValues.posStatus && barData.dataValues.posStatus == '1') {
			whereClause.push({
				categoryID: '-1'
			});
		} else {
			whereClause.push({
				[Op.not]: [{ categoryID: '-1' }]
			});
		}
		const subCategoryList = await SubCategoryModel.findAll({
			attributes: [
				[Sequelize.literal(`sub_category.id`), 'subCategoryID'],
				'name',
				'id',
				'categoryID',
				[
					Sequelize.literal(
						`(select count(*) from product where product.barID = ${req.body.bar_id} and isDeleted = 'no' and subCategoryID = sub_category.id limit 1) `
					),
					'productCount'
				]
			],
			where: whereClause,
			include: [
				{
					model: BarCategorySequenceModel,
					as: 'bar_category_sequence',
					required: false,
					attributes: [
						[
							Sequelize.fn(
								'coalesce',
								Sequelize.col('subCategorySequence'),
								1000000000000
							),
							'subCategorySequence'
						]
					],
					where: { barId: req.body.bar_id }
				}
				// {
				// 	model: PickupLocationSubCategoryModel,
				// 	required: false,
				// 	where: {
				// 		barID: req.body.bar_id
				// 	}
				// }
			],
			order: [
				[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
				'id'
			]
		});
		if (subCategoryList) {
			return subCategoryList.filter(
				(category) => category.dataValues.productCount > 0
			);
		} else {
			return 0;
		}
	},

	async getCategoryList(req, res) {
		let whereClause = {
			isDeleted: 'No'
		};

		try {
			let data = await SubCategoryModel.findAll({
				where: whereClause,
				attributes: [
					'id',
					'categoryID',
					'name',
					[
						Sequelize.literal(
							'(select count(product.id) from product WHERE product.subCategoryID = `sub_category`.id AND product.isDeleted="No" AND product.status="Active" AND product.barID=' +
								req.body.bar_id +
								')'
						),
						'totalProduct'
					]
				],
				having: {
					totalProduct: {
						[Op.gt]: 0
					}
				}
			});

			return data;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},

	/* barSubCategorySequence */
	async barSubCategorySequence(req, res) {
		try {
			const barId = req.body.bar_id;
			const subCategoryIds = req.body.sub_category_ids;

			let count = await SubCategoryModel.count({
				where: { isDeleted: 'No', id: subCategoryIds }
			});

			if (count > 0) {
				await BarCategorySequenceModel.destroy({
					where: { barId: barId }
				});

				subCategoryIds.map(async (subcategory, index) => {
					await BarCategorySequenceModel.create({
						barId: barId,
						subCategorySequence: index + 1,
						subCategoryId: subcategory
					});
				});

				return 1;
			} else {
				return 0;
			}
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},

	/* getSubCategoryList */
	async getSubCategoryList(req, res) {
		let barId = req.body.bar_id;
		let whereClause = [
			{
				isDeleted: 'No'
			}
		];
		try {
			const barData = await BarModel.findOne({
				attributes: [
					'id',
					'restaurantName',
					'managerName',
					'email',
					'countryCode',
					'mobile',
					'stripeID',
					'venueId',
					'attachedPosConfig',
					'docketCommission',
					'posStatus'
				],
				where: {
					id: barId
				}
			});

			if (barData.dataValues.posStatus && barData.dataValues.posStatus == '1') {
				whereClause.push({
					categoryID: '-1'
				});
			} else {
				whereClause.push({
					[Op.not]: [{ categoryID: '-1' }]
				});
			}

			let data = await SubCategoryModel.findAll({
				attributes: [
					[Sequelize.literal(`sub_category.id`), 'subCategoryID'],
					'name',
					'id',
					'categoryID',
					[
						Sequelize.col('pickup_location_sub_category->pickup_location.id'),
						'pickupLocationId'
					],
					[
						Sequelize.col(
							'pickup_location_sub_category->pickup_location.address'
						),
						'pickupLocationAddress'
					]
				],
				where: whereClause,
				include: [
					{
						model: BarCategorySequenceModel,
						as: 'bar_category_sequence',
						required: false,
						attributes: [
							[
								Sequelize.fn(
									'coalesce',
									Sequelize.col('subCategorySequence'),
									1000000000000
								),
								'subCategorySequence'
							]
						],
						where: { barId: barId }
					},
					{
						model: PickupLocationSubCategoryModel,
						attributes: [],
						where: { barID: barId },
						include: [
							{
								model: PickupLocation,
								attributes: []
							}
						]
					}
				],
				order: [
					[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
					'id'
				]
			});

			return data;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},

	async getSubCategoryForManageMenu(req, res) {
		try {
			let barID = req.body.bar_id;

			let whereClause = [];
			whereClause.push({ isDeleted: 'No' });

			const barDetails = await BarModel.findOne({
				attributes: ['id', 'serviceType', 'posStatus'],
				where: { id: barID }
			});

			/*
			if (barDetails) {
				const { posStatus } = barDetails.dataValues;

				whereClause.push(
					posStatus == '1'
						? { categoryID: '-1' }
						: { [Op.not]: [{ categoryID: '-1' }] }
				);
			}
			*/

			if (
				barDetails.dataValues.posStatus &&
				barDetails.dataValues.posStatus == '1'
			) {
				whereClause.push({ categoryID: '-1' });
			} else {
				whereClause.push({ [Op.not]: [{ categoryID: '-1' }] });
			}

			const selectedServiceType = req.body.serviceType;
			let currentDay = moment().tz('Australia/Perth').isoWeekday();
			const currentTime = moment().tz('Australia/Perth').format('HH:mm:ss');

			let categories = await SubCategoryModel.findAll({
				where: whereClause,
				attributes: [
					'id',
					'categoryID',
					'name',
					[
						Sequelize.literal(
							`(SELECT IF(IA.status = '1' and CAST('${currentTime}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = WEEKDAY(CAST(NOW() AS DATE))HAVING isActive = 1)`
						),
						'operatingFlag'
					],
					[
						Sequelize.literal(`
							(select waitTime from sub_category_wait_time
								where
								barID = ${barID} and
								subCategoryID = sub_category.id and
								weekDay = '${currentDay - 1}' and
								startTime <= '${currentTime}' and
								endTime >= '${currentTime}' LIMIT 1
							)
						`),
						'waitTime'
					],
					[
						Sequelize.literal(`
							(select id from sub_category_wait_time
								where
								barID = ${barID} and
								subCategoryID = sub_category.id and
								weekDay = '${currentDay - 1}' and
								startTime <= '${currentTime}' and
								endTime >= '${currentTime}' LIMIT 1
							)
						`),
						'waitTimeId'
					]
				],
				include: [
					{
						model: BarCategorySequenceModel,
						as: 'bar_category_sequence',
						required: false,
						attributes: [
							[
								Sequelize.fn(
									'coalesce',
									Sequelize.col('subCategorySequence'),
									1000000000000
								),
								'subCategorySequence'
							]
						],
						where: { barId: barID }
					}
				],
				order: [
					[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
					'id'
				]
			});

			// return only sub categories which has products and if other conditions are true.
			let subCategories = [];
			let data = [];
			for (let i = 0; i < categories.length; i++) {
				subCategories.push(categories[i].id);

				let productWhere = [
					{ subCategoryID: categories[i].id },
					{ isDeleted: 'No' },
					{ barID: barID }
				];

				if (
					selectedServiceType &&
					selectedServiceType.toLowerCase() !== 'both'
				) {
					productWhere.push({
						[Op.or]: [
							{ serviceType: selectedServiceType },
							{ serviceType: 'BOTH' }
						]
					});
				}

				const productCount = await ProductModel.count({
					where: productWhere,
					raw: true
				});

				if (productCount) {
					data.push({
						categoryID: categories[i].id,
						mainCategoryID: categories[i].categoryID,
						categoryName: categories[i].name,
						categoryActive: categories[i].dataValues.operatingFlag
							? categories[i].dataValues.operatingFlag
							: 0
					});
				}
			}

			if (data.length) {
				// check if subcategories has popular products or not.
				const popularProducts = await productService.fetchPopularProductListV2(
					barID,
					null,
					subCategories,
					selectedServiceType
				);

				if (popularProducts.products.length) {
					// add Popular sub-category at 1st index
					data.unshift({
						categoryID: 0,
						maincategoryID: 0,
						categoryName: 'Popular',
						categoryActive: 1
					});
				}
			}

			return data;
		} catch (error) {
			throw error;
		}
	}
};
