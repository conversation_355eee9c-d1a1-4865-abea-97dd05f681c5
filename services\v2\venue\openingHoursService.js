/*Messages,status code and services require*/
require('dotenv').config();
const constant = require('../../../config/constant');
const moment = require('moment-timezone');
const BarModel = require('../../../database/models').bar; //import modal always Capital
const OperatingHoursModel = require('../../../database/models').operating_hour;
const ItemActiveHoursModel =
	require('../../../database/models').itemActiveHours;
const BarOpeningHoursModel =
	require('../../../database/models').bar_opening_hours;
const BarOpeningHoursUTCModel =
	require('../../../database/models').bar_opening_hours_utc;
const BarSubCategoryOpeningHoursModel =
	require('../../../database/models').bar_sub_category_opening_hours;
const BarSubCategoryOpeningHoursUTCModel =
	require('../../../database/models').bar_sub_category_opening_hours_utc;
const BarSubCategoryWaitTimeModel =
	require('../../../database/models').bar_sub_category_wait_time;
const BarSubCategoryWaitTimeUTCModel =
	require('../../../database/models').bar_sub_category_wait_time_utc;
const SubCategoryModel = require('../../../database/models').sub_category;
const BarCategorySequenceModel =
	require('../../../database/models').bar_category_sequence;
const Sequelize = require('sequelize');
const {
	convertOpeningHours,
	timeChunkArray
} = require('../../../common/commonFunction');
const Op = Sequelize.Op;

module.exports = {
	/* add venue */
	async addVenueOperatingHours(req, res) {
		try {
			const barID = req.body.bar_id;
			const requestOperatingHours = req.body.operating_hours;

			// await OperatingHoursModel.destroy({
			// 	where: {
			// 		barID: barID
			// 	}
			// });
			requestOperatingHours.map(async (opHours) => {
				await OperatingHoursModel.create({
					openingHours: opHours.opening_hours,
					closingHours: opHours.closing_hours,
					weekDay: opHours.week_day,
					isClosed: opHours.is_closed,
					barID: barID
				});
			});

			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getVenueOpeningHours(req, res) {
		try {
			const { bar_id: barID } = req.body;

			// Fetch bar details
			const barDetails = await BarModel.findOne({
				where: { isDeleted: 'No', id: barID }
			});

			if (!barDetails) {
				return res.status(404).json({ message: 'Bar not found' });
			}

			let whereClause = [{ isDeleted: 'No' }];

			if (barDetails.posStatus === '1') {
				whereClause.push({ categoryID: '-1' });
			} else {
				whereClause.push({ categoryID: { [Op.ne]: '-1' } });
			}

			// Fetch sub-category list
			const subCategoryList = await SubCategoryModel.findAll({
				attributes: [
					['id', 'subCategoryID'],
					'name',
					'id',
					'categoryID',
					[
						Sequelize.literal(
							`(SELECT COUNT(*) FROM product WHERE product.barID = ${barID} AND isDeleted = 'no' AND subCategoryID = sub_category.id LIMIT 1)`
						),
						'productCount'
					]
				],
				where: whereClause,
				include: [
					{
						model: BarCategorySequenceModel,
						as: 'bar_category_sequence',
						required: false,
						attributes: [
							[
								Sequelize.fn(
									'coalesce',
									Sequelize.col('subCategorySequence'),
									1000000000000
								),
								'subCategorySequence'
							]
						],
						where: { barId: barID }
					}
				],
				order: [
					[Sequelize.literal('bar_category_sequence.subCategorySequence')],
					['id']
				]
			});

			const filteredSubCategoryIDs = subCategoryList
				.filter((category) => category.dataValues.productCount > 0)
				.map((category) => category.dataValues.subCategoryID);

			const subCategoryNames = subCategoryList.reduce((acc, category) => {
				acc[category.dataValues.subCategoryID] = category.dataValues.name;
				return acc;
			}, {});

			// Fetch venue opening hours
			const barFetchResponse = await BarOpeningHoursModel.findAll({
				attributes: [
					'weekDay',
					[
						Sequelize.fn('date_format', Sequelize.col('openingHours'), '%H:%i'),
						'openingHours'
					],
					[
						Sequelize.fn('date_format', Sequelize.col('closingHours'), '%H:%i'),
						'closingHours'
					],
					[Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
				],
				where: { barID },
				order: [['weekDay'], ['openingHours']]
			});

			// Fetch sub-category opening hours
			const subCategoryFetchResponse =
				await BarSubCategoryOpeningHoursModel.findAll({
					attributes: [
						'weekDay',
						'subCategoryID',
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('openingHours'),
								'%H:%i'
							),
							'openingHours'
						],
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('closingHours'),
								'%H:%i'
							),
							'closingHours'
						],
						[Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
					],
					where: {
						barID,
						subCategoryID: { [Op.in]: filteredSubCategoryIDs }
					},
					order: [['weekDay'], ['openingHours']]
				});

			if (!barFetchResponse.length && !subCategoryFetchResponse.length) {
				return {
					matchCategoryOpeningHours: barDetails.matchCategoryOpeningHours,
					venueOpeningHours: [],
					subCategoryOpeningHours: []
				};
			}

			const groupedData = new Map();

			for (const {
				weekDay,
				openingHours,
				closingHours,
				isClosed
			} of barFetchResponse) {
				if (!groupedData.has(weekDay)) {
					groupedData.set(weekDay, { weekDay, isClosed, timeSlots: [] });
				}
				groupedData.get(weekDay).timeSlots.push({ openingHours, closingHours });
			}

			const subCategoryGroupedData = new Map();

			for (const {
				weekDay,
				subCategoryID,
				openingHours,
				closingHours,
				isClosed
			} of subCategoryFetchResponse) {
				if (!subCategoryGroupedData.has(subCategoryID)) {
					subCategoryGroupedData.set(subCategoryID, {
						subCategoryID,
						subCategoryName: subCategoryNames[subCategoryID],
						weekDays: new Map()
					});
				}

				const weekDayMap = subCategoryGroupedData.get(subCategoryID).weekDays;

				if (!weekDayMap.has(weekDay)) {
					weekDayMap.set(weekDay, { weekDay, isClosed, timeSlots: [] });
				}

				weekDayMap.get(weekDay).timeSlots.push({ openingHours, closingHours });
			}

			const formattedSubCategoryGroupedData = Array.from(
				subCategoryGroupedData.values()
			).map((subCategory) => ({
				subCategoryID: subCategory.subCategoryID,
				subCategoryName: subCategory.subCategoryName,
				weekDays: Array.from(subCategory.weekDays.values())
			}));

			formattedSubCategoryGroupedData.sort(
				(a, b) =>
					filteredSubCategoryIDs.indexOf(a.subCategoryID) -
					filteredSubCategoryIDs.indexOf(b.subCategoryID)
			);

			return {
				matchCategoryOpeningHours: barDetails.matchCategoryOpeningHours,
				venueOpeningHours: Array.from(groupedData.values()),
				subCategoryOpeningHours: formattedSubCategoryGroupedData
			};
		} catch (error) {
			console.error(error);
			return 0;
		}
	},
	async updateVenueOpeningHours(req, res) {
		try {
			const {
				bar_id: barID,
				matchCategoryOpeningHours,
				venueOpeningHours: updatedOperatingHours
			} = req.body;
			const bar = await BarModel.findByPk(barID);
			const timezone = bar.timezone;
			const whereClause =
				bar.posStatus === '1'
					? { categoryID: '-1', isDeleted: 'No' }
					: { categoryID: { [Op.not]: '-1' }, isDeleted: 'No' };
			const subCategories = await SubCategoryModel.findAll({
				where: whereClause
			});
			const subCategoryIDs = subCategories.map((sc) => sc.id);
			for (const day of updatedOperatingHours) {
				const { weekDay: weekDayStr, isClosed, timeSlots } = day;
				const localWeekDay = parseInt(weekDayStr, 10);
				const existingRecords = await BarOpeningHoursModel.findAll({
					where: { barID, weekDay: localWeekDay }
				});
				const existingData = existingRecords.map((r) => ({
					openingHours: moment(r.openingHours, 'HH:mm:ss').format('HH:mm'),
					closingHours: moment(r.closingHours, 'HH:mm:ss').format('HH:mm'),
					isClosed: r.isClosed ? '1' : '0'
				}));
				const newData = timeSlots.map((s) => ({
					openingHours: s.openingHours,
					closingHours: s.closingHours,
					isClosed
				}));

				const arraysEqual = (arr1, arr2) =>
					arr1.length === arr2.length &&
					arr1.every((item1) =>
						arr2.some(
							(item2) =>
								item1.openingHours === item2.openingHours &&
								item1.closingHours === item2.closingHours &&
								item1.isClosed === item2.isClosed
						)
					);

				if (arraysEqual(existingData, newData)) continue;

				const existingIds = existingRecords.map((r) => r.id);
				if (existingIds.length) {
					if (
						bar.matchCategoryOpeningHours == 'Yes' &&
						matchCategoryOpeningHours == 'Yes'
					) {
						await Promise.all([
							BarOpeningHoursUTCModel.destroy({
								where: { barOpeningHoursID: existingIds }
							}),
							BarOpeningHoursModel.destroy({ where: { id: existingIds } })
						]);

						const existingSubCategoryHours =
							await BarSubCategoryOpeningHoursModel.findAll({
								where: {
									barID,
									weekDay: localWeekDay,
									subCategoryID: subCategoryIDs
								}
							});
						const existingSubCategoryHourIDs = existingSubCategoryHours.map(
							(h) => h.id
						);
						await Promise.all([
							BarSubCategoryWaitTimeModel.destroy({
								where: {
									barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
								}
							}),
							BarSubCategoryWaitTimeUTCModel.destroy({
								where: {
									barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
								}
							}),
							BarSubCategoryOpeningHoursModel.destroy({
								where: { id: existingSubCategoryHourIDs }
							}),
							BarSubCategoryOpeningHoursUTCModel.destroy({
								where: {
									barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
								}
							})
						]);
					} else {
						await Promise.all([
							BarOpeningHoursUTCModel.destroy({
								where: { barOpeningHoursID: existingIds }
							}),
							BarOpeningHoursModel.destroy({ where: { id: existingIds } })
						]);
					}
				}

				const newBarHours = await BarOpeningHoursModel.bulkCreate(
					timeSlots.map((s) => ({
						openingHours: s.openingHours,
						closingHours: s.closingHours,
						weekDay: localWeekDay,
						isClosed: isClosed === '1',
						barID
					})),
					{ returning: true }
				);

				const utcEntries = newBarHours.flatMap((r) =>
					convertOpeningHours(r, timezone, localWeekDay).map((e) => ({
						...e,
						barID,
						isClosed,
						barOpeningHoursID: r.id
					}))
				);
				if (utcEntries.length)
					await BarOpeningHoursUTCModel.bulkCreate(utcEntries);

				if (
					bar.matchCategoryOpeningHours == 'Yes' &&
					matchCategoryOpeningHours === 'Yes'
				) {
					const subCategoryEntries = newBarHours.flatMap((newBarHour) =>
						subCategories.map((sc) => ({
							openingHours: newBarHour.openingHours,
							closingHours: newBarHour.closingHours,
							weekDay: newBarHour.weekDay,
							subCategoryID: sc.id,
							isClosed: newBarHour.isClosed,
							barID
						}))
					);

					if (subCategoryEntries.length) {
						const newSubHours =
							await BarSubCategoryOpeningHoursModel.bulkCreate(
								subCategoryEntries,
								{ returning: true }
							);

						const utcSubEntries = newSubHours.flatMap((r) =>
							convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
								...e,
								barID,
								isClosed: r.isClosed,
								subCategoryID: r.subCategoryID,
								barSubCategoryOpeningHoursID: r.id
							}))
						);
						if (utcSubEntries.length)
							await BarSubCategoryOpeningHoursUTCModel.bulkCreate(
								utcSubEntries
							);

						const waitTimeEntries = newSubHours.flatMap((record) =>
							timeChunkArray(
								record.openingHours,
								record.closingHours,
								'60'
							).map(({ startTime, endTime }) => ({
								barID,
								subCategoryID: record.subCategoryID,
								barSubCategoryOpeningHoursID: record.id,
								weekDay: record.weekDay,
								waitTime: '00:10:00',
								startTime,
								endTime
							}))
						);
						if (waitTimeEntries.length) {
							const newWaitTimeEntries =
								await BarSubCategoryWaitTimeModel.bulkCreate(waitTimeEntries);
							const utcWaitTimeEntries = newWaitTimeEntries.flatMap(
								({
									id,
									startTime,
									endTime,
									waitTime,
									weekDay,
									isClosed,
									subCategoryID,
									barSubCategoryOpeningHoursID
								}) =>
									convertOpeningHours(
										{ openingHours: startTime, closingHours: endTime },
										timezone,
										weekDay
									).map((data) => ({
										...data,
										barSubCategoryWaitTimeID: id,
										startTime: data.openingHours,
										endTime: data.closingHours,
										waitTime,
										isClosed,
										subCategoryID,
										barSubCategoryOpeningHoursID,
										barID
									}))
							);
							if (utcWaitTimeEntries.length)
								await BarSubCategoryWaitTimeUTCModel.bulkCreate(
									utcWaitTimeEntries
								);
						}
					}
				}
			}

			if (
				matchCategoryOpeningHours === 'Yes' &&
				bar.matchCategoryOpeningHours !== 'Yes'
			) {
				await Promise.all([
					BarSubCategoryOpeningHoursModel.destroy({
						where: { barID, subCategoryID: subCategoryIDs }
					}),
					BarSubCategoryOpeningHoursUTCModel.destroy({
						where: { barID, subCategoryID: subCategoryIDs }
					}),
					BarSubCategoryWaitTimeModel.destroy({
						where: { barID, subCategoryID: subCategoryIDs }
					}),
					BarSubCategoryWaitTimeUTCModel.destroy({
						where: { barID, subCategoryID: subCategoryIDs }
					})
				]);

				const subCategoryEntries = updatedOperatingHours.flatMap((day) => {
					const localWeekDay = parseInt(day.weekDay, 10);
					return subCategories.flatMap((sc) =>
						day.timeSlots.map((s) => ({
							openingHours: s.openingHours,
							closingHours: s.closingHours,
							weekDay: localWeekDay,
							subCategoryID: sc.id,
							isClosed: day.isClosed === '1',
							barID
						}))
					);
				});

				if (subCategoryEntries.length) {
					const newSubHours = await BarSubCategoryOpeningHoursModel.bulkCreate(
						subCategoryEntries,
						{ returning: true }
					);
					const utcSubEntries = newSubHours.flatMap((r) =>
						convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
							...e,
							barID,
							isClosed: r.isClosed,
							subCategoryID: r.subCategoryID,
							barSubCategoryOpeningHoursID: r.id
						}))
					);
					if (utcSubEntries.length)
						await BarSubCategoryOpeningHoursUTCModel.bulkCreate(utcSubEntries);

					const waitTimeEntries = newSubHours.flatMap((record) =>
						timeChunkArray(record.openingHours, record.closingHours, '60').map(
							({ startTime, endTime }) => ({
								barID,
								subCategoryID: record.subCategoryID,
								barSubCategoryOpeningHoursID: record.id,
								weekDay: record.weekDay,
								waitTime: '00:10:00',
								startTime,
								endTime
							})
						)
					);
					if (waitTimeEntries.length) {
						const newWaitTimeEntries =
							await BarSubCategoryWaitTimeModel.bulkCreate(waitTimeEntries);
						const utcWaitTimeEntries = newWaitTimeEntries.flatMap(
							({
								id,
								startTime,
								endTime,
								waitTime,
								weekDay,
								isClosed,
								subCategoryID,
								barSubCategoryOpeningHoursID
							}) =>
								convertOpeningHours(
									{ openingHours: startTime, closingHours: endTime },
									timezone,
									weekDay
								).map((data) => ({
									...data,
									barSubCategoryWaitTimeID: id,
									startTime: data.openingHours,
									endTime: data.closingHours,
									waitTime,
									isClosed,
									subCategoryID,
									barSubCategoryOpeningHoursID,
									barID
								}))
						);
						if (utcWaitTimeEntries.length)
							await BarSubCategoryWaitTimeUTCModel.bulkCreate(
								utcWaitTimeEntries
							);
					}
				}
			}

			await BarModel.update(
				{ matchCategoryOpeningHours },
				{ where: { id: barID } }
			);
			return 1;
		} catch (error) {
			console.error(error);
			return 0;
		}
	},
	async updateVenueCategoryOpeningHours(req, res) {
		try {
			const {
				bar_id: barID,
				subCategoryID,
				subCategoryOpeningHours
			} = req.body;

			const bar = await BarModel.findByPk(barID);
			const timezone = bar.timezone;

			const venueHours = await BarOpeningHoursModel.findAll({
				where: { barID },
				attributes: ['weekDay', 'openingHours', 'closingHours'],
				raw: true
			});

			const venueHoursMap = venueHours.reduce(
				(acc, { weekDay, openingHours, closingHours }) => {
					if (!acc[weekDay]) acc[weekDay] = [];
					acc[weekDay].push({
						start: moment(openingHours, 'HH:mm:ss'),
						end: moment(closingHours, 'HH:mm:ss')
					});
					return acc;
				},
				{}
			);

			for (const { weekDay, timeSlots } of subCategoryOpeningHours) {
				const venueSlots = venueHoursMap[weekDay];

				const hasInvalidSlot = timeSlots.some((slot) => {
					const subCatStart = moment(slot.openingHours, 'HH:mm');
					const subCatEnd = moment(slot.closingHours, 'HH:mm');

					return !venueSlots.some(
						(venueSlot) =>
							subCatStart.isSameOrAfter(venueSlot.start) &&
							subCatEnd.isSameOrBefore(venueSlot.end)
					);
				});

				if (hasInvalidSlot) {
					return -1;
				}
			}

			for (const day of subCategoryOpeningHours) {
				const { weekDay: weekDayStr, isClosed, timeSlots } = day;
				const localWeekDay = parseInt(weekDayStr, 10);
				const existingRecords = await BarSubCategoryOpeningHoursModel.findAll({
					where: { barID, subCategoryID, weekDay: localWeekDay }
				});
				const existingData = existingRecords.map((record) => ({
					openingHours: moment(record.openingHours, 'HH:mm:ss').format('HH:mm'),
					closingHours: moment(record.closingHours, 'HH:mm:ss').format('HH:mm'),
					subCategoryID,
					isClosed: record.isClosed ? '1' : '0'
				}));
				const newData = timeSlots.map((timeSlot) => ({
					...timeSlot,
					subCategoryID,
					isClosed
				}));

				const arraysEqual = (arr1, arr2) =>
					arr1.length === arr2.length &&
					arr1.every((item1) =>
						arr2.some(
							(item2) =>
								item1.openingHours === item2.openingHours &&
								item1.closingHours === item2.closingHours &&
								item1.subCategoryID === item2.subCategoryID &&
								item1.isClosed === item2.isClosed
						)
					);

				if (arraysEqual(existingData, newData)) continue;

				const existingIds = existingRecords.map((record) => record.id);
				if (existingIds.length) {
					await Promise.all([
						BarSubCategoryOpeningHoursUTCModel.destroy({
							where: { barSubCategoryOpeningHoursID: existingIds }
						}),
						BarSubCategoryOpeningHoursModel.destroy({
							where: { id: existingIds }
						}),
						BarSubCategoryWaitTimeModel.destroy({
							where: { barSubCategoryOpeningHoursID: existingIds }
						}),
						BarSubCategoryWaitTimeUTCModel.destroy({
							where: { barSubCategoryOpeningHoursID: existingIds }
						})
					]);
				}

				const newCategoryHours =
					await BarSubCategoryOpeningHoursModel.bulkCreate(
						timeSlots.map(({ openingHours, closingHours }) => ({
							openingHours,
							closingHours,
							subCategoryID,
							weekDay: localWeekDay,
							isClosed: isClosed === '1',
							barID
						})),
						{ returning: true }
					);

				const utcEntries = newCategoryHours.flatMap((record) =>
					convertOpeningHours(record, timezone, localWeekDay).map((data) => ({
						...data,
						barID,
						isClosed,
						subCategoryID,
						barSubCategoryOpeningHoursID: record.id
					}))
				);
				if (utcEntries.length)
					await BarSubCategoryOpeningHoursUTCModel.bulkCreate(utcEntries);

				const waitTimeEntries = newCategoryHours.flatMap((record) => {
					return timeChunkArray(
						record.openingHours,
						record.closingHours,
						'60'
					).map(({ startTime, endTime }) => ({
						barID,
						subCategoryID,
						barSubCategoryOpeningHoursID: record.id,
						weekDay: record.weekDay,
						waitTime: '00:10:00',
						startTime,
						endTime
					}));
				});

				if (waitTimeEntries.length) {
					const newSubCategoryWaitTimeHours =
						await BarSubCategoryWaitTimeModel.bulkCreate(waitTimeEntries);

					const utcWaitTimeEntries = newSubCategoryWaitTimeHours.flatMap(
						({
							startTime,
							endTime,
							waitTime,
							weekDay,
							isClosed,
							barSubCategoryOpeningHoursID,
							id
						}) =>
							convertOpeningHours(
								{ openingHours: startTime, closingHours: endTime },
								timezone,
								weekDay
							).map((data) => ({
								...data,
								barSubCategoryWaitTimeID: id,
								startTime: data.openingHours,
								endTime: data.closingHours,
								waitTime,
								isClosed,
								subCategoryID,
								barSubCategoryOpeningHoursID,
								barID
							}))
					);
					if (utcWaitTimeEntries.length)
						await BarSubCategoryWaitTimeUTCModel.bulkCreate(utcWaitTimeEntries);
				}
			}

			return 1;
		} catch (error) {
			console.error(error);
			return 0;
		}
	},

	async convertExistingVenueOpeningHours(req, res) {
		try {
			const bars = await BarModel.findAll({
				where: { isDeleted: 'No' },
				include: [{ model: OperatingHoursModel }],
				order: [[OperatingHoursModel, 'weekDay', 'ASC']]
			});

			for (const bar of bars) {
				const whereClause =
					bar.posStatus === '1'
						? { categoryID: '-1', isDeleted: 'No' }
						: { categoryID: { [Op.not]: '-1' }, isDeleted: 'No' };

				const subCats = await SubCategoryModel.findAll({ where: whereClause });
				const subCatIDs = subCats.map((sc) => sc.id);

				for (const hour of bar.operating_hours) {
					const { weekDay, openingHours, closingHours, isClosed, barID } = hour;

					if (
						await BarOpeningHoursModel.findOne({
							where: { weekDay, openingHours, closingHours, isClosed, barID }
						})
					)
						continue;

					const newHours = await BarOpeningHoursModel.create({
						weekDay,
						openingHours,
						closingHours,
						isClosed,
						barID,
						timeZone: bar.timezone
					});
					const utcSlots = convertOpeningHours(
						newHours,
						bar.timezone,
						weekDay
					).map((s) => ({
						...s,
						barID,
						isClosed,
						barOpeningHoursID: newHours.id
					}));
					await BarOpeningHoursUTCModel.bulkCreate(utcSlots);

					for (const subID of subCatIDs) {
						const newSub = await BarSubCategoryOpeningHoursModel.create({
							barID,
							weekDay,
							openingHours,
							closingHours,
							isClosed,
							subCategoryID: subID,
							timeZone: bar.timezone
						});

						const utcSubSlots = convertOpeningHours(
							newSub,
							bar.timezone,
							weekDay
						).map((s) => ({
							...s,
							barID,
							isClosed,
							subCategoryID: subID,
							barSubCategoryOpeningHoursID: newSub.id
						}));

						await BarSubCategoryOpeningHoursUTCModel.bulkCreate(utcSubSlots);

						const waitTimeChunks = timeChunkArray(
							newSub.openingHours,
							newSub.closingHours,
							'60'
						).map((c) => ({
							barID,
							subCategoryID: subID,
							barSubCategoryOpeningHoursID: newSub.id,
							weekDay: newSub.weekDay,
							waitTime: '00:10:00',
							startTime: c.startTime,
							endTime: c.endTime
						}));

						if (waitTimeChunks.length) {
							const watiTimeEntries =
								await BarSubCategoryWaitTimeModel.bulkCreate(waitTimeChunks);

							const utcWaitTimeEntries = watiTimeEntries.flatMap(
								({
									startTime,
									endTime,
									waitTime,
									weekDay,
									isClosed,
									barSubCategoryOpeningHoursID,
									id
								}) =>
									convertOpeningHours(
										{ openingHours: startTime, closingHours: endTime },
										bar.timezone,
										weekDay
									).map((data) => ({
										...data,
										barSubCategoryWaitTimeID: id,
										startTime: data.openingHours,
										endTime: data.closingHours,
										waitTime,
										isClosed,
										subCategoryID: subID,
										barSubCategoryOpeningHoursID,
										barID
									}))
							);
							if (utcWaitTimeEntries.length)
								await BarSubCategoryWaitTimeUTCModel.bulkCreate(
									utcWaitTimeEntries
								);
						}
					}
				}
			}
			return 1;
		} catch (error) {
			console.error('Error :', error);
			return 0;
		}
	}
};
