// secretManager.js
const AWS = require('aws-sdk');
const constant = require('../config/constant');
const { retryWithJitter } = require('./retry');

AWS.config.update({
    region: process.env.S3_REGION || 'ap-southeast-2',
    credentials: {
        accessKeyId: process.env.S3_ACCESSKEY,
        secretAccessKey: process.env.S3_SECRETKEY
    }
});


async function getSecret() {
    // Configure AWS credentials

    const secretsManager = new AWS.SecretsManager();
    const data = await secretsManager.getSecretValue({ SecretId: constant.AWS_SECRET_ID }).promise();
    return JSON.parse(data.SecretString);
}

async function fetchSecretWithRetry() {
    return retryWithJitter(
        () => getSecret(),
        {
            retries: 5,
            baseDelay: 200,
            onRetry: (err, attempt, delay) => {
                console.warn(`🔁 Secret fetch retry #${attempt} in ${Math.round(delay)}ms:`, err.message);
            }
        }
    );
}

module.exports = { fetchSecretWithRetry };
