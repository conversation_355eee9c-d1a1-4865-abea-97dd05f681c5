'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class transaction_err_logs extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// transaction_err_logs.belongsTo(models.user, { foreignKey: 'userID' });
			// transaction_err_logs.belongsTo(models.bar, { foreignKey: 'barID' });
			// transaction_err_logs.belongsTo(models.orders, { foreignKey: 'orderID' });
		}
	}
	transaction_err_logs.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			orderID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'orders',
					key: 'id'
				}
			},
			amout: DataTypes.FLOAT,
			log: DataTypes.TEXT,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE
		},
		{
			sequelize,
			paranoid: true,
			freezeTableName: true,
			modelName: 'transaction_err_logs'
		}
	);
	return transaction_err_logs;
};
