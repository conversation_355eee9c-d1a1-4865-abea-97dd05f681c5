require('dotenv').config();
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const PickupLocationModel = require('../../../database/models').pickup_location;
const ProductModel = require('../../../database/models').product;
const BarModel = require('../../../database/models').bar;
const SubCategoryModel = require('../../../database/models').sub_category;
const PickupLocationSubCategoryModel =
	require('../../../database/models').pickup_location_sub_category;

async function ensureDefaultPickupLocation(barId) {
	let defaultPickupLocation = await PickupLocationModel.findOne({
		where: {
			barID: barId,
			isDeleted: 'No',
			isDefault: '1'
		},
		order: [['id', 'ASC']]
	});

	if (!defaultPickupLocation) {
		defaultPickupLocation = await PickupLocationModel.create({
			barID: barId,
			address: 'Collect at counter',
			isDefault: '1'
		});
	}

	return defaultPickupLocation;
}

module.exports = {
	async add(req, res) {
		try {
			const barId = req.body.bar_id;

			const pickupLocation = await PickupLocationModel.create({
				barID: barId,
				address: req.body.address,
				isDefault: '0'
			});

			return pickupLocation;
		} catch (err) {
			throw err;
		}
	},

	/* list */
	async list(req, res) {
		try {
			const barId = req.body.bar_id;

			const data = await PickupLocationModel.findAll({
				where: { barID: barId, isDeleted: 'No' },
				attributes: { exclude: ['isDeleted', 'createdAt', 'updatedAt'] },
				include: [
					{
						model: PickupLocationSubCategoryModel,
						attributes: ['subCategoryID'],
						include: [
							{
								model: ProductModel,
								attributes: [],
								where: {
									isDeleted: 'No',
									barID: barId
								},
								required: true
							}
						]
					}
				],
				order: [['isDefault', 'DESC'], 'id']
			});

			return data;
		} catch (err) {
			throw err;
		}
	},

	/* edit */
	async edit(req, res) {
		try {
			const barId = req.body.bar_id;
			const pickupLocationId = req.body.id;
			const subCategoryIds = req.body.sub_category_ids || [];
			const address = req.body.address;

			if (address) {
				await PickupLocationModel.update(
					{ address, updatedAt: new Date() },
					{ where: { id: pickupLocationId } }
				);
			}

			if (subCategoryIds.length > 0) {
				const existingLinks = await PickupLocationSubCategoryModel.findAll({
					where: { barID: barId, subCategoryID: { [Op.in]: subCategoryIds } }
				});

				const linkedSubCategories = existingLinks.reduce((acc, link) => {
					acc[link.subCategoryID] = link.pickupLocationID;
					return acc;
				}, {});

				for (const subCatId of subCategoryIds) {
					if (linkedSubCategories[subCatId] == pickupLocationId) {
						continue;
					}

					if (linkedSubCategories[subCatId]) {
						await PickupLocationSubCategoryModel.destroy({
							where: { barID: barId, subCategoryID: subCatId }
						});
					}

					await ProductModel.update(
						{ pickupLocationID: pickupLocationId },
						{
							where: {
								subCategoryID: subCatId,
								barID: barId,
								isDeleted: 'No'
							}
						}
					);

					await PickupLocationSubCategoryModel.create({
						barID: barId,
						subCategoryID: subCatId,
						pickupLocationID: pickupLocationId
					});
				}
			}
			return await PickupLocationModel.findByPk(pickupLocationId);
		} catch (err) {
			throw err;
		}
	},

	/* delete */
	async delete(req, res) {
		try {
			const barId = req.body.bar_id;
			const pickupLocationId = req.body.id;

			const pickupLocation = await PickupLocationModel.findOne({
				where: {
					id: pickupLocationId,
					barID: barId,
					isDeleted: 'No',
					isDefault: '0'
				}
			});

			if (!pickupLocation) {
				return 0;
			}

			const defaultPickupLocation = await ensureDefaultPickupLocation(barId);

			// Update subcategories to default pickup location
			await PickupLocationSubCategoryModel.update(
				{ pickupLocationID: defaultPickupLocation.id },
				{ where: { pickupLocationID: pickupLocationId } }
			);

			// Update products to default pickup location
			const linkedSubCategories = await PickupLocationSubCategoryModel.findAll({
				where: { pickupLocationID: defaultPickupLocation.id },
				attributes: ['subCategoryID']
			});

			const subCategoryIds = linkedSubCategories.map(
				(link) => link.subCategoryID
			);

			await ProductModel.update(
				{ pickupLocationID: defaultPickupLocation.id },
				{
					where: {
						subCategoryID: { [Op.in]: subCategoryIds },
						barID: barId,
						isDeleted: 'No'
					}
				}
			);

			// Mark the pickup location as deleted
			const data = await PickupLocationModel.update(
				{ isDeleted: 'Yes' },
				{ where: { id: pickupLocationId, barID: barId } }
			);

			return data;
		} catch (err) {
			throw err;
		}
	},

	/* create default pickup location for all venues */
	async createDefaultPickupLocationForAllVenues(req, res) {
		try {
			// Fetch all active venues
			const venues = await BarModel.findAll({
				where: { isDeleted: 'No' },
				attributes: ['id', 'posStatus']
			});

			if (!venues || venues.length === 0) {
				console.log('No venues found.');
				return 0;
			}

			for (const venue of venues) {
				const { id: barId, posStatus } = venue;

				// Ensure the default pickup location exists
				const defaultPickupLocation = await ensureDefaultPickupLocation(barId);

				// Determine the condition for fetching subcategories
				const whereCondition = {
					...(posStatus === 1
						? { categoryID: -1 }
						: { categoryID: { [Op.ne]: -1 } })
				};

				// Fetch subcategories and their IDs
				const allSubCategories = await SubCategoryModel.findAll({
					where: whereCondition,
					attributes: ['id'],
					raw: true // Optimize query performance by fetching plain objects
				});

				const subCategoryIds = allSubCategories.map((subCat) => subCat.id);

				// Fetch existing links for the bar
				const existingLinks = await PickupLocationSubCategoryModel.findAll({
					where: { barID: barId },
					attributes: ['subCategoryID'],
					raw: true
				});

				const existingSubCategoryIds = existingLinks.map(
					(link) => link.subCategoryID
				);

				// Find subcategories that need to be linked
				const newSubCategoryIds = subCategoryIds.filter(
					(subCatId) => !existingSubCategoryIds.includes(subCatId)
				);

				// Bulk create new links and update products in one go
				if (newSubCategoryIds.length > 0) {
					// Prepare new links
					const newLinks = newSubCategoryIds.map((subCatId) => ({
						barID: barId,
						subCategoryID: subCatId,
						pickupLocationID: defaultPickupLocation.id
					}));

					await PickupLocationSubCategoryModel.bulkCreate(newLinks);

					// Update products for the new subcategories
					await ProductModel.update(
						{ pickupLocationID: defaultPickupLocation.id },
						{
							where: {
								subCategoryID: newSubCategoryIds,
								barID: barId,
								isDeleted: 'No'
							}
						}
					);
				}
			}
			return 1;
		} catch (err) {
			console.error('Error creating default pickup locations:', err);
			throw err;
		}
	}
};
