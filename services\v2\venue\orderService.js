/*Messages,status code and services require*/
require('dotenv').config();

const order = require('../../../database/models').orders;
const orderItems = require('../../../database/models').order_items;
const orderItemExtras = require('../../../database/models').order_item_extras;
const orderItemVariants = require('../../../database/models').order_item_variants;
const orderTableNumber = require('../../../database/models').orderTableNumber;
const bar = require('../../../database/models').bar;
const coupons = require('../../../database/models').coupons;
const orderDiscount = require('../../../database/models').order_discount;
const orderProductVariantTypes =
	require('../../../database/models').order_product_variant_types;
const orderProductVariantSubTypes =
	require('../../../database/models').order_product_variant_sub_types;
const productVariantTypes =
	require('../../../database/models').product_variant_types;
const productVariantSubTypes =
	require('../../../database/models').product_variant_sub_types;
const user = require('../../../database/models').user;
const orderTax = require('../../../database/models').order_tax;
const orderRefundTax = require('../../../database/models').order_refund_tax;
const product = require('../../../database/models').product;
const productVariants = require('../../../database/models').product_variants;
const productExtras = require('../../../database/models').product_extras;
const pickupLocation = require('../../../database/models').pickup_location;
const constant = require('../../../config/constant');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const sequelize = require('sequelize');

module.exports = {
	/*  bar order history */
	async barOrderHistory(req, res) {
		try {
			const offset = (req.body.page - 1) * constant.LIMIT;
			const limit = constant.LIMIT;
			const barID = req.body.bar_id;
			const whereClauseOrder = [];
			const whereClauseOrderItems = [];
			const whereClauseRefundedItem = [];

			const orderAttributes = [
				'id',
				'orderNo',
				'subTotal',
				'transactionFee',
				'refundTransactionFee',
				'tax',
				'total',
				'orderDate',
				'orderStatus',
				'refundStatus',
				'promocode_id',
				'promocode_amount',
				'paymentStatus',
				'promocode_discount',
				'userID',
				'barID',
				'stripeFee',
				'orderServiceType',
				'isPosOrder',
				'posOrderFee',
				'isDocketOrder',
				'docketOrderFee',
				"totalDiscountedAmount",
				[
					Sequelize.literal(
						'(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
					),
					'totalOrderTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
					),
					'totalOrderRefundTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_items.newRefundAmount) from order_items WHERE order_items.orderID = `orders`.id)'
					),
					'refundAmount'
				],
				[
					Sequelize.literal(
						'round(total - round(stripeFee, 2) - transactionFee, 2)'
					),
					'net_revenue'
				],
				'createdAt'
			];

			whereClauseOrder.push({
				isDeleted: 'No',
				barID: barID
			});

			// if(getUserCategory.length > 0) {
			//   whereClauseProduct.push({
			//     subCategoryID : getUserCategory
			//   });
			// }

			if (req.body.startDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.gte]: req.body.startDate
					}
				});
			}

			if (req.body.endDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.lte]: req.body.endDate
					}
				});
			}

			if (req.body.search) {
				whereClauseOrder.push({
					orderNo: {
						[Op.like]: '%' + req.body.search + '%'
					}
				});
			}

			if (req.body.orderType && req.body.orderType != '') {
				if (req.body.orderType == 'current') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
						],
						isCanceled: 'No',
						paymentStatus: 'received'
					});
					whereClauseOrderItems.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
							//   orderStatus: 'Pickup'
							// }
						],
						isCanceled: 'No'
					});
				} else if (req.body.orderType == 'past') {
					whereClauseOrderItems.push({
						orderStatus: {
							[Op.notIn]: ['New', 'Preparing']
						}
					});
					whereClauseOrder.push({
						//isCanceled: 'No',
						paymentStatus: 'received'
					});
				} else if (req.body.orderType == 'refund') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								refundStatus: 'Refunded'
							},
							{
								refundStatus: 'PartialRefunded'
							},
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							)
						],
						paymentStatus: 'received'
					});
					// whereClauseRefundedItem.push({
					//   [Op.or]: [
					//     {
					//       isCanceled: 'Yes'
					//     },
					//     {
					//       refundedQuantity: {
					//         [Op.gt]: 0
					//       }
					//     },
					//     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
					//   ]
					// })
				} else if (req.body.orderType == 'cancel') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'Yes'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'Intoxicated') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'No'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							),
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'promo_code') {
                    console.log("heloo----------")
					whereClauseOrder.push({
						[Op.or]: [
							{
								[Op.and]: [
									{ promocode_id: { [Op.gt]: 0 } },
									{ promocode_amount: { [Op.gt]: 0 } }
								]
							},
							{ totalDiscountedAmount: { [Op.gt]: 0 } }
						]
					});
				}
			} else {
				whereClauseOrder.push({
					// [Op.or]: [
					//   {
					//     orderStatus: 'New'
					//   },
					//   {
					//     orderStatus: 'Preparing'
					//   },
					//   {
					//     orderStatus: 'Pickup'
					//   }
					// ],
					paymentStatus: 'received'
				});
				whereClauseOrderItems.push({
					// [Op.or]: [
					// 	{
					// 		orderStatus: 'New'
					// 	},
					// 	{
					// 		orderStatus: 'Preparing'
					// 	},
					// 	{
					// 		orderStatus: 'Pickup'
					// 	}
					// ]
				});
			}

			const orderHistoryList = await order.findAndCountAll({
				where: [...whereClauseOrder],
				attributes: [...orderAttributes, 'pickupCode'],
				include: [
					{
						required: true,
						attributes: [
							'id',
							'orderID',
							'productID',
							'price',
							'chargeAmount',
							'quantity',
							'specialRequest',
							'isCanceled',
							'refundAmount',
							'refundedQuantity',
							'waitTime',
							'orderStatus',
							'PreparingStartTime',
							'ReadyTime',
							'PickedupTime',
							"discountedAmount",
							[
								Sequelize.literal(
									`(ADDTIME(order_items.createdAt, waitTime)) `
								),
								'expectedTime'
							],
							[
								Sequelize.literal(
									`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
								),
								'pickupLocation'
							],
							[
								Sequelize.literal(
									`(select subCategoryID from product where product.id = order_items.productID ) `
								),
								'subCategoryID'
							]
						],
						model: orderItems,
						where: [...whereClauseRefundedItem, ...whereClauseOrderItems],
						include: [
							{
								// where: [ {categoryID: [1,2] }, ...whereClauseProduct ],
								attributes: [
									'id',
									'name',
									'categoryID',
									'subCategoryID',
									'description',
									'avatar',
									'posID',
									'pickupLocationID'
								],
								required: false,
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
								model: orderItemExtras,
								include: [
									{
										attributes: ['id', 'extraItem', 'posID'],
										model: productExtras
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
								model: orderItemVariants,
								include: [
									{
										attributes: ['id', 'variantType'],
										model: productVariants
									}
								]
							},
							{
								attributes: [
									['id', 'orderProductVariantTypeID'],
									'orderItemID'
								],
								where: Sequelize.where(
									Sequelize.col('`order_items`.`id`'),
									Sequelize.col(
										'`order_items->order_product_variant_types`.`orderItemID`'
									)
								),
								model: orderProductVariantTypes,
								required: false,
								include: [
									{
										attributes: [['id', 'productVariantTypeID'], 'label'],
										model: productVariantTypes,
										required: true,
										include: [
											{
												attributes: [
													['id', 'orderProductVariantSubTypeID'],
													'orderItemID'
												],
												// where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
												model: orderProductVariantSubTypes,
												include: [
													{
														attributes: [
															['id', 'productVariantSubTypeID'],
															['variantType', 'extraItem'],
															'price'
														],
														model: productVariantSubTypes
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						required: false,
						model: user,
						attributes: ['id', 'fullName', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: [
							'id',
							'code',
							'name',
							'description',
							'is_fixed',
							'discount_amount'
						]
					},
					{
						model: orderDiscount,
						attributes: [
							'id',
							'orderID',
							'discountID',
							'discountCode',
							'discountType',
							'type',
							'discountValue',
							'discountAmount',
							'createdAt'
						]
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				],
				order: [['createdAt', 'DESC']],
				distinct: true,
				duplicating: false,
				offset: offset,
				limit: limit
			});
			if (orderHistoryList.count > 0) {
				for (let i = 0; i < orderHistoryList.rows.length; i++) {
					const order = orderHistoryList.rows[i];
					let newNetRevenue = orderHistoryList.rows[i].dataValues.net_revenue;
					if (orderHistoryList.rows[i].dataValues.refundStatus != 'no') {
						newNetRevenue =
							newNetRevenue -
							orderHistoryList.rows[i].dataValues.totalOrderRefundTax -
							orderHistoryList.rows[i].dataValues.refundAmount;
					}
					if (orderHistoryList.rows[i].dataValues.isPosOrder == 1) {
						var posCommissionFee =
							(orderHistoryList.rows[i].dataValues.posOrderFee / 100) *
							(orderHistoryList.rows[i].dataValues.subTotal +
								orderHistoryList.rows[i].dataValues.totalOrderTax);
						newNetRevenue = newNetRevenue - posCommissionFee;
						orderHistoryList.rows[i].dataValues.posOrderFee =
							Number(posCommissionFee).toFixed(2);
					}
					if (orderHistoryList.rows[i].dataValues.isDocketOrder == 1) {
						var docketCommissionFee =
							(orderHistoryList.rows[i].dataValues.docketOrderFee / 100) *
							(orderHistoryList.rows[i].dataValues.subTotal +
								orderHistoryList.rows[i].dataValues.totalOrderTax);
						newNetRevenue = newNetRevenue - docketCommissionFee;
						orderHistoryList.rows[i].dataValues.docketOrderFee =
							Number(docketCommissionFee).toFixed(2);
					}
					if (
						orderHistoryList.rows[i].dataValues.refundTransactionFee == 'Yes'
					) {
						newNetRevenue =
							newNetRevenue -
							orderHistoryList.rows[i].dataValues.transactionFee;
					}
					orderHistoryList.rows[i].dataValues.net_revenue =
						newNetRevenue.toFixed(2);
					let totalRefundAmount = Number(
						orderHistoryList.rows[i].dataValues.refundAmount +
							orderHistoryList.rows[i].dataValues.totalOrderRefundTax
					);
					if (
						orderHistoryList.rows[i].dataValues.refundTransactionFee == 'Yes'
					) {
						totalRefundAmount =
							totalRefundAmount +
							orderHistoryList.rows[i].dataValues.transactionFee;
					}
					orderHistoryList.rows[i].dataValues.refundAmount = totalRefundAmount;
					for (let j = 0; j < order.order_items.length; j++) {
						const item = orderHistoryList.rows[i].order_items[j];
						for (let k = 0; k < item.order_product_variant_types.length; k++) {
							const variant =
								orderHistoryList.rows[i].order_items[j]
									.order_product_variant_types[k];
							let getProductSubVariant =
								await orderProductVariantSubTypes.findOne({
									attributes: [
										['id', 'orderProductVariantSubTypeID'],
										'orderItemID',
										[
											Sequelize.literal(
												'(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'extraItem'
										],
										[
											Sequelize.literal(
												'(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'price'
										],
										[
											Sequelize.literal(
												'(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'productVariantSubTypeID'
										]
									],
									where: {
										orderItemID: variant.orderItemID,
										productVariantTypeID:
											variant.product_variant_type.dataValues
												.productVariantTypeID
									}
								});

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID =
								getProductSubVariant.dataValues.productVariantSubTypeID;

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem =
								getProductSubVariant.dataValues.extraItem;

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price =
								getProductSubVariant.dataValues.price;
						}
					}
				}
				orderHistoryList.rows =
					orderHistoryList &&
					orderHistoryList.rows.map((order) => {
						let newOrderItems = groupByOrderItems(
							order.order_items,
							order.orderServiceType,
							order.refundStatus
						); // Group By order items
						// delete order.dataValues.order_items; // Delete old key
						order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
						return order;
					});
				return orderHistoryList;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async barOrderList(req, res) {
			try {
				const barID = req.body.bar_id;
				const dashboardOrder = {};
				let whereClauseOrder = {};
	
				let havingClauseOrder = [
					{
						totalCancelItem: {
							[Op.gt]: 0
						}
					}
				];
	
				const barDetails = await bar.findOne({
					attributes: [
						'id',
						'serviceType',
						'attachedPosConfig',
						'avatar',
						'docketStatus',
						'posStatus'
					],
					where: { id: barID }
				});
	
				if (barDetails.serviceType != 'BOTH') {
					whereClauseOrder = {
						orderServiceType: barDetails.serviceType
					};
				}
	
				const orderAttributes = [
					'id',
					'orderNo',
					// [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
					'subTotal',
					'transactionFee',
					'refundTransactionFee',
					'tax',
					'total',
					'orderDate',
					'orderStatus',
					'refundStatus',
					'promocode_id',
					'promocode_amount',
					'promocode_discount',
					'userID',
					'barID',
					'isPosOrder',
					'posOrderStatus',
					'orderServiceType',
					'createdAt',
					'isDocketOrder',
					'docketPrintingStatus',
					'pickupCode',
					"totalDiscountedAmount",
					[
						Sequelize.literal(
							'(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
						),
						'totalCancelItem'
					]
				];
				const itemAttributes = [
					'id',
					'orderID',
					'productID',
					'price',
					'quantity',
					'specialRequest',
					'isCanceled',
					'refundAmount',
					'chargeAmount',
					'discountedAmount',
					'newRefundAmount',
					'refundedQuantity',
					'waitTime',
					'orderStatus',
					'PreparingStartTime',
					'ReadyTime',
					'PickedupTime',
					"discountedAmount",
					[
						Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `),
						'expectedTime'
					],
					[
						Sequelize.literal(
							`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
						),
						'pickupLocation'
					],
					[
						Sequelize.literal(
							`(select subCategoryID from product where product.id = order_items.productID ) `
						),
						'subCategoryID'
					]
				];
				const productAttributes = [
					'id',
					'name',
					'categoryID',
					'description',
					'avatar',
					'subCategoryID'
				];
	
				let newOrder = await order.findAll({
					where: [
						{
							isDeleted: 'No',
							paymentStatus: 'received',
							barID: barID,
							isCanceled: 'No',
							[Op.or]: [
								{
									orderStatus: 'New'
								},
								{
									orderStatus: 'Preparing'
								}
							],
							[Op.not]: [
								{
									orderStatus: 'Intoxicated'
								}
							],
							...whereClauseOrder
						}
					],
					attributes: orderAttributes,
					include: [
						{
							required: true,
							attributes: itemAttributes,
							where: [
								{
									[Op.or]: [
										{
											orderStatus: 'New'
										},
										{
											orderStatus: 'Preparing'
										}
									],
									isCanceled: 'No'
								}
							],
							model: orderItems,
							include: [
								{
									attributes: productAttributes,
									model: product,
									include: [
										{
											attributes: ['id', 'description', 'address'],
											model: pickupLocation
										}
									]
								},
								{
									attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
									model: orderItemExtras,
									include: [
										{
											attributes: ['id', 'extraItem'],
											model: productExtras
										}
									]
								},
								{
									attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
									model: orderItemVariants,
									include: [
										{
											attributes: ['id', 'variantType'],
											model: productVariants
										}
									]
								},
								{
									attributes: [
										['id', 'orderProductVariantTypeID'],
										'orderItemID'
									],
									where: Sequelize.where(
										Sequelize.col('`order_items`.`id`'),
										Sequelize.col(
											'`order_items->order_product_variant_types`.`orderItemID`'
										)
									),
									model: orderProductVariantTypes,
									required: false,
									include: [
										{
											attributes: [['id', 'productVariantTypeID'], 'label'],
											model: productVariantTypes,
											required: true,
											include: [
												{
													attributes: [
														['id', 'orderProductVariantSubTypeID'],
														'orderItemID'
													],
													where: Sequelize.where(
														Sequelize.col(
															'`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'
														),
														Sequelize.col(
															'`order_items->order_product_variant_types`.`id`'
														)
													),
													model: orderProductVariantSubTypes,
													include: [
														{
															attributes: [
																['id', 'productVariantSubTypeID'],
																['variantType', 'extraItem'],
																'price'
															],
															model: productVariantSubTypes
														}
													]
												}
											]
										}
									]
								}
							]
						},
						{
							required: true,
							model: user,
							attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
						},
						{
							model: coupons,
							attributes: ['id', 'code', 'name', 'description']
						},
						{
							model: orderDiscount,
							attributes: [
								'id',
								'orderID',
								'discountID',
								'discountCode',
								'discountType',
								'type',
								'discountValue',
								'discountAmount',
								'createdAt',
							]
						},
						{
							model: orderTableNumber,
							attributes: ['tableCode']
						},
						{
							attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
							model: orderTax
						},
						{
							attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
							model: orderRefundTax
						}
					],
					order: [['createdAt', 'ASC']],
					distinct: true,
					duplicating: false
				});
	
				let pickupOrder = await order.findAll({
					where: [
						{
							orderStatus: {
								[Op.notIn]: ['Intoxicated', 'Pickedup']
							},
							isDeleted: 'No',
							barID: barID,
							isCanceled: 'No',
							paymentStatus: 'received',
							...whereClauseOrder
						}
					],
					having: havingClauseOrder,
					attributes: [...orderAttributes],
					include: [
						{
							required: true,
							attributes: itemAttributes,
							model: orderItems,
							where: {
								orderStatus: 'Pickup',
								isCanceled: 'No'
							},
							include: [
								{
									attributes: productAttributes,
									model: product,
									include: [
										{
											attributes: ['id', 'description', 'address'],
											model: pickupLocation
										}
									]
								},
								{
									attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
									model: orderItemExtras,
									include: [
										{
											attributes: ['id', 'extraItem'],
											model: productExtras
										}
									]
								},
								{
									attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
									model: orderItemVariants,
									include: [
										{
											attributes: ['id', 'variantType'],
											model: productVariants
										}
									]
								},
								{
									attributes: [
										['id', 'orderProductVariantTypeID'],
										'orderItemID'
									],
									where: Sequelize.where(
										Sequelize.col('`order_items`.`id`'),
										Sequelize.col(
											'`order_items->order_product_variant_types`.`orderItemID`'
										)
									),
									model: orderProductVariantTypes,
									required: false,
									include: [
										{
											attributes: [['id', 'productVariantTypeID'], 'label'],
											model: productVariantTypes,
											required: true,
											include: [
												{
													attributes: [
														['id', 'orderProductVariantSubTypeID'],
														'orderItemID'
													],
													where: Sequelize.where(
														Sequelize.col(
															'`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'
														),
														Sequelize.col(
															'`order_items->order_product_variant_types`.`id`'
														)
													),
													model: orderProductVariantSubTypes,
													include: [
														{
															attributes: [
																['id', 'productVariantSubTypeID'],
																['variantType', 'extraItem'],
																'price'
															],
															model: productVariantSubTypes
														}
													]
												}
											]
										}
									]
								}
							]
						},
						{
							required: true,
							model: user,
							attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
						},
						{
							model: coupons,
							attributes: ['id', 'code', 'name', 'description']
						},
						{
							model: orderDiscount,
							attributes: [
								'id',
								'orderID',
								'discountID',
								'discountCode',
								'discountType',
								'type',
								'discountValue',
								'discountAmount',
								'createdAt',
							]
						},
						{
							model: orderTableNumber,
							attributes: ['tableCode']
						},
						{
							attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
							model: orderTax
						},
						{
							attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
							model: orderRefundTax
						}
					],
					order: [['updatedAt', 'ASC']],
					distinct: true,
					duplicating: false
				});
	
				let newOrdersObj = newOrder.map((order) => {
					let newOrderItems = groupByOrderItems(
						order.order_items,
						order.orderServiceType,
						order.refundStatus
					); // Group By order items
					// delete order.dataValues.order_items; // Delete old key
					order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
					return order;
				});
	
				dashboardOrder.newOrder = newOrdersObj;
	
				let pickupOrderObj = pickupOrder.map((order) => {
					let newOrderItems = groupByOrderItems(
						order.order_items,
						order.orderServiceType,
						order.refundStatus
					); // Group By order items
					// delete order.dataValues.order_items; // Delete old key
					order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
					return order;
				});
	
				dashboardOrder.pickupOrder = pickupOrderObj;
	
				return dashboardOrder;
			} catch (err) {
				console.log('err', err);
				throw err;
			}
		},
};



function groupByOrderItems(
	orderItems,
	orderServiceType = 'PICKUP',
	refundStatus = 'no'
) {
	let order_items = JSON.parse(JSON.stringify(orderItems));
	// Group By Sub Category ID
	// let groupByArr = groupBy(order_items, 'subCategoryID');
	// let newOrderItemsArr = [];
	// Object.entries(groupByArr).forEach(([key, value]) => {
	let newValueItemsArr = [];
	if (orderServiceType === 'PICKUP') {
		// Group By Pickup Location
		let groupByPickupLocationArr = groupBy(order_items, 'pickupLocation');
		Object.entries(groupByPickupLocationArr).forEach(([key, value]) => {
			// Group By Wait Time
			let groupByWaitTimeArr = groupBy(value, 'waitTime');
			let newValueItemsArr1 = [];
			Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
				newValueItemsArr1.push({
					wait_time: value[0] && value[0].expectedTime,
					orderStatus: value[0].orderStatus,
					refundStatus: refundStatus,
					items: value
				});
			});

			let pickup_location = value[0] && value[0].pickupLocation;
			Object.entries(newValueItemsArr1).forEach(([key, value]) => {
				newValueItemsArr.push({ pickup_location: pickup_location, ...value });
			});
		});
	} else {
		// Group By Wait Time
		let groupByWaitTimeArr = groupBy(order_items, 'waitTime');
		Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
			newValueItemsArr.push({
				wait_time: value[0] && value[0].expectedTime,
				orderStatus: value[0].orderStatus,
				refundStatus: refundStatus,
				items: value
			});
		});
	}
	newValueItemsArr.sort(function (a, b) {
		return new Date(a.wait_time) - new Date(b.wait_time);
	});
	// let subCategoryID = value[0]?.subCategoryID;
	// Object.entries(newValueItemsArr).forEach(([key, value]) => {
	//   newOrderItemsArr.push({ subCategoryID: subCategoryID, ...value });
	// });
	// });

	return newValueItemsArr;
}

function groupBy(xs, key) {
	return xs.reduce(function (rv, x) {
		(rv[x[key]] = rv[x[key]] || []).push(x);
		return rv;
	}, {});
}
