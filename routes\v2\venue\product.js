var express = require('express');
var router = express.Router();

const Authorization =
	require('../../../middleware/venueAuth').venueAuthorization;
const IsVenueUserConnectedWithBar =
	require('../../../middleware/venueAuth').isVenueUserConnectedWithBar;

const ProductController = require('../../../controllers/v2/venue/productController');

router.post(
	'/getVenueSubCategories',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getVenueSubCategories
);

router.post(
	'/getSegmentTags',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getSegmentTags
);

router.post(
	'/globalPriceUpdate',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.globalPriceUpdate
);

module.exports = router;
