var express = require('express');
var router = express.Router();

/* require for Authentication */
const Authorization = require('../../middleware/advertiserAuth').advertiserAuthorization;
const MulterMiddleware = require('../../middleware/multer');

/* require for Controller */
const AuthController = require('../../controllers/advertiser/authController');

/* routes of admin panel */

/* Register API */
router.post(
	'/register',
	MulterMiddleware.singleProfilePic,
	AuthController.register
);
router.get("/getTimezones", AuthController.getTimezones);

/* login API */
router.post('/login', AuthController.login);
/* Verify MFA API */
router.post('/verify-mfa', AuthController.verifyMFA);

router.post('/send-verification-code', AuthController.sendVerificationCode);
router.post('/verify-email-otp', AuthController.verifyOTP);

router.post('/verify-password', Authorization, AuthController.verifyPassword);

/* Setup MFA API */
router.post('/setup-mfa', AuthController.setupMFA);

/* Forgot Password API */
router.post('/forgot-password', AuthController.forgotPassword);

/* Verify token */
router.post('/verify-token', AuthController.verifyToken);

/* Reset Password API */
router.post('/reset-password', AuthController.resetPassword);

/* logout  device */
router.post('/logout', Authorization, AuthController.logout);

/* Edit Profile API */
router.put(
	'/edit-profile',
	MulterMiddleware.singleProfilePic,
	Authorization,
	AuthController.updateProfile
);

/* get profile API */
router.get(
	'/get-profile-details',
	Authorization,
	AuthController.getProfileDetails
);

/* verify otp */
router.post('/verify-mfa-otp', Authorization, AuthController.verifyMFAOtp);

module.exports = router;
