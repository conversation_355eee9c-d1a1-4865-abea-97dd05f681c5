var orderItems = require("./order_items");
var productVariants = require("./product_variants");
const { Model } = require("sequelize");

module.exports = (sequelize, DataTypes) => {
    class order_item_variants extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            order_item_variants.belongsTo(models.product_variants, {
                foreignKey: "productVariantsID",
            });

            models.order_items.hasMany(order_item_variants, {
                foreignKey: "orderItemID",
            });
            order_item_variants.belongsTo(models.order_items, {
                foreignKey: "orderItemID",
            });
        }
    }
    order_item_variants.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            orderItemID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "orderItems",
                    key: "id",
                },
            },
            productVariantsID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "productVariants",
                    key: "id",
                },
            },
            price: DataTypes.FLOAT,
            createdAt: DataTypes.DATE,
            updatedAt: DataTypes.DATE,
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: "order_item_variants",
            timestamps: true,
        }
    );
    return order_item_variants;
};
