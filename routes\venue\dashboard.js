const express = require('express');
const router = express();
const dashboardController = require('../../controllers/venue/dashboardController');
const {
	venueAuthorization,
	isVenueUserConnectedWithBar
} = require('../../middleware/venueAuth');

router.post(
	'/get-venue-statistics',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.getVenueStatistics
);
router.post(
	'/get-venue-statistics-new',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.getVenueStatisticsNew
);
router.post(
	'/service-type-percentage',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.serviceTypeByPercentage
);
router.post(
	'/orders-per-Hours',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.ordersPerHours
);
router.post(
	'/service-type-revenue',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.serviceTypeByRevenue
);
router.post(
	'/customer-count-by-time',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.customerCountByTime
);
router.post(
	'/most-orderd-items',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.mostOrderdItems
);
router.post(
	'/total-revenue',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	dashboardController.totalRevenue
);

module.exports = router;
