'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const imageGet = require('../../middleware/multerAwsGet');
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
module.exports = (sequelize, DataTypes) => {
	class venue_user extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			venue_user.belongsToMany(models.bar, {
				through: models.venue_user_bar,
				foreignKey: 'user_id'
			});
			venue_user.belongsToMany(models.subscription, {
				through: models.venue_user_subscription,
				foreignKey: 'user_id'
			});
		}
	}
	venue_user.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			profile_image: {
				type: DataTypes.STRING(555),
				get() {
					if (
						this.getDataValue('profile_image') != '' &&
						this.getDataValue('profile_image') != null
					) {
						// const contentValue = imageGet(
						// 	constant.AWSS3VENUEUSERFOLDER + this.getDataValue('profile_image')
						// );
						// return contentValue;
						return (
							constant.AWSS3PRIVATEURL +
							constant.AWSS3VENUEUSERFOLDER +
							this.getDataValue('profile_image')
						);
					} else {
						return '';
					}
				},
				defaultValue: null
			},
			// manager_name: DataTypes.STRING(255),
			first_name: DataTypes.STRING(255),
			last_name: DataTypes.STRING(255),
			email: DataTypes.STRING(255),
			country_code: DataTypes.STRING(25),
			mobile: DataTypes.STRING(25),
			password: DataTypes.STRING(255),
			password_updated_at: DataTypes.DATE,
			latitude: DataTypes.STRING(100),
			longitude: DataTypes.STRING(100),
			pronouns: {
				type: DataTypes.STRING(555),
				defaultValue: null
			},
			mobile_verified: {
				type: DataTypes.ENUM('Yes', 'No'),
				defaultValue: 'Yes'
			},
			address: {
				type: DataTypes.STRING(555),
				defaultValue: null
			},
			badge: DataTypes.BIGINT,
			mfa_code: DataTypes.STRING(200),
			mfa_qr_code: {
				type: DataTypes.STRING(200),
				get() {
					if (
						this.getDataValue('mfa_qr_code') != '' &&
						this.getDataValue('mfa_qr_code') != null
					) {
						// const contentValue = imageGet(
						// 	constant.AWSS3VENUEQRCODEFOLDER + this.getDataValue('mfa_qr_code')
						// );
						// return contentValue;
						return (
							constant.AWSS3PRIVATEURL +
							constant.AWSS3VENUEQRCODEFOLDER +
							this.getDataValue('mfa_qr_code')
						);
					} else {
						return '';
					}
				}
			},
			notification: {
				type: DataTypes.ENUM('Yes', 'No'),
				defaultValue: 'Yes'
			},
			account_verified: {
				type: DataTypes.ENUM('New', 'Approved', 'Rejected'),
				defaultValue: 'New'
			},
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			otp_token: DataTypes.STRING(555)
		},
		{
			hooks: {
				beforeCreate: function (user, options) {
					if (user.password) {
						return bcrypt
							.hash(user.password, 10)
							.then((hash) => {
								user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				beforeUpdate: function (venue_user, options) {
					if (venue_user.password) {
						return bcrypt
							.hash(venue_user.password, 10)
							.then((hash) => {
								venue_user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				}
			},

			sequelize,
			paranoid: true,
			underscored: true,
			freezeTableName: true,
			modelName: 'venue_user',
			timestamps: true
		}
	);
	return venue_user;
};
