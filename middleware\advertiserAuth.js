/*
 * Summary:     auth middleware user file.
 * Author:      Openxcell
 */
const AdvertiserUserToken = require('../database/models').advertiser_user_accesstoken; //include venue user token model

const JWT = require('jsonwebtoken');
const constant = require('../config/constant');
const message = require('../config/cmsMessage');
const status = require('../config/status').status;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

exports.advertiserAuthorization = async (req, res, next) => {
	if (req.headers.authorization) {
		try {
			let jwtGetAdvertiserUserDetail = await JWT.verify(
				req.headers.authorization,
				constant.JWTTOKEN.secret,
				{
					algorithm: constant.JWTTOKEN.algo,
					expiresIn: constant.JWTTOKEN.expiresIn
				}
			);
			let getAdvertiserUserAuthDetails = await AdvertiserUserToken.findOne({
				where: {
					access_token: req.headers.authorization,
					user_id: jwtGetAdvertiserUserDetail.user_id
				}
			});

			if (getAdvertiserUserAuthDetails) {
				req.user_id = getAdvertiserUserAuthDetails.dataValues.user_id;
				req.email = jwtGetAdvertiserUserDetail.email;
				next();
			} else {
				let resData = {
					data: '',
					status: status.UNAUTHORIZEDUSER
				};
				return res.status(401).send(resData);
			}
		} catch (error) {
			console.log('TCL: exports.authenticationApi -> error', error);
			let resData = {
				data: '',
				status: status.UNAUTHORIZEDUSER
			};
			return res.status(401).send(resData);
		}
	} else {
		return response(
			res,
			status.UNAUTHORIZEDUSER,
			{},
			message.cmsMessage.TOKENREQUIRED,
			status.ERROR
		);
	}
};

