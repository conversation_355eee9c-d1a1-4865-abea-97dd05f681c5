"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class venue_user_subscription extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  venue_user_subscription.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      subscription_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },

      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
      },
    },
    {
      sequelize,
      timestamps: true,
      paranoid: true,
      underscored: true,
      modelName: "venue_user_subscription",
      freezeTableName: true,
    }
  );
  return venue_user_subscription;
};
