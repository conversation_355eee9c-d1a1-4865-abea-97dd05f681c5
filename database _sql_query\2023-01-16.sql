CREATE TABLE `venue_user` (
  `id` bigint NOT NULL,
  `manager_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `country_code` varchar(25) DEFAULT NULL,
  `mobile` varchar(25) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `password_updated_at` datetime DEFAULT NULL,
  `password_updatedAt` datetime DEFAULT NULL,
  `latitude` varchar(100) DEFAULT NULL,
  `longitude` varchar(100) DEFAULT NULL,
  `mobile_verified` enum('Yes','No') DEFAULT 'Yes',
  `badge` bigint DEFAULT NULL,
  `mfa_code` varchar(200) DEFAULT NULL,
  `mfa_qr_code` varchar(200) DEFAULT NULL,
  `notification` enum('Yes','No') DEFAULT 'Yes',
  `account_verified` enum('New','Approved','Rejected') DEFAULT 'New',
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `otp_token` varchar(555) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `profile_image` varchar(555) DEFAULT NULL,
  `address` varchar(555) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


ALTER TABLE `venue_user`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `venue_user`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;
COMMIT;

CREATE TABLE `venue_user_bar` (
  `id` bigint NOT NULL,
  `user_id` int NOT NULL,
  `bar_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE `venue_user_bar`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `venue_user_bar` CHANGE `id` `id` BIGINT(20) NOT NULL AUTO_INCREMENT;

CREATE TABLE `venue_user_accesstoken` (
  `id` int NOT NULL,
  `user_id` bigint NOT NULL,
  `device_token` varchar(255) DEFAULT NULL,
  `access_token` varchar(255) NOT NULL,
  `device_type` int DEFAULT NULL,
  `device_location` varchar(255) DEFAULT NULL,
  `device_name` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE `venue_user_accesstoken`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

  ALTER TABLE `venue_user_accesstoken`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

  ALTER TABLE `venue_user_accesstoken`
  ADD CONSTRAINT `venue_user_accesstoken_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `venue_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;


ALTER TABLE `product` ADD PRIMARY KEY(`id`);
ALTER TABLE `product` CHANGE `id` `id` INT NOT NULL AUTO_INCREMENT;
ALTER TABLE `product_variant_types` ADD PRIMARY KEY(`id`);
ALTER TABLE `product_variant_types` CHANGE `id` `id` BIGINT NOT NULL AUTO_INCREMENT;
ALTER TABLE `product_variants` ADD PRIMARY KEY(`id`);
ALTER TABLE `product_variants` CHANGE `id` `id` INT NOT NULL AUTO_INCREMENT;
ALTER TABLE `product_extras` ADD PRIMARY KEY(`id`);
ALTER TABLE `product_extras` CHANGE `id` `id` INT NOT NULL AUTO_INCREMENT;
ALTER TABLE `product_food_options` ADD PRIMARY KEY(`id`);
ALTER TABLE `product_food_options` CHANGE `id` `id` BIGINT NOT NULL AUTO_INCREMENT;
