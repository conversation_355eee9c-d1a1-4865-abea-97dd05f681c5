/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const moment = require('moment');
const speakeasy = require('speakeasy');
const VenueUserModel = require('../../database/models').venue_user;
const VenueUserTokenModel =
	require('../../database/models').venue_user_accesstoken;

module.exports = {
	/* Change Password */
	async changePassword(req, res) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id,
				deletedAt: null
			}
		});

		//user not present
		if (!findVenueUser) {
			return 0;
		}

		//compare old password
		if (!Bcryptjs.compareSync(req.body.old_password, findVenueUser.password)) {
			return 2; //old password is incorrect
		}

		const generatedSalt = Bcryptjs.genSaltSync(10);
		let encryptedPassword = await Bcryptjs.hash(
			req.body.new_password,
			generatedSalt
		);
		//update password
		return await VenueUserModel.update(
			{
				password: encryptedPassword,
				password_updated_at: new Date()
			},
			{
				where: {
					id: findVenueUser.id
				}
			}
		);
	},

	/* Change Password Date */
	async changePasswordDate(req, res) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id
			}
		});

		if (findVenueUser.dataValues.password_updated_at) {
			return moment(findVenueUser.dataValues.password_updated_at).fromNow();
		}
		return 0;
	},

	async getDevicelist(req, res) {
		try {
			let findVenueUser = await VenueUserModel.findOne({
				where: {
					id: req.user_id
				}
			});

			//user not found
			if (!findVenueUser) {
				return 0;
			}

			let tokenArray = await VenueUserTokenModel.findAll({
				where: {
					user_id: findVenueUser.id
				}
			});

			//added flag for current device
			let currentDevice = tokenArray.filter((value, index) => {
				if (value.access_token === req.headers.authorization) {
					delete tokenArray[index].dataValues.access_token;
					return value;
				}
			});

			let otherDevices = tokenArray.filter((value, index) => {
				if (value.access_token) {
					delete tokenArray[index].dataValues.access_token;
					return value;
				}
			});

			return { currentDevice: currentDevice, otherDevice: otherDevices };
		} catch (err) {
			throw err;
		}
	},
	/*LOGOUT  */
	async logoutDevice(req, res) {
		try {
			// delete token
			let token = await VenueUserTokenModel.destroy({
				where: {
					id: req.body.id
				}
			});
			//logout successfully
			if (token) {
				return 1;
			}
			return 0;
		} catch (err) {
			console.log('err');
			throw err;
		}
	},
	/*verify-otp  */
	async verifyOtp(req, res) {
		try {
			let findVenueUser = await VenueUserModel.findOne({
				where: {
					id: req.user_id,
					deleted_at: null,
					status: 'Active'
				}
			});

			if (findVenueUser) {
				let verified = speakeasy.totp.verify({
					secret: findVenueUser.dataValues.mfa_code,
					encoding: 'base32',
					token: req.body.otp,
					window: 1
				});

				//verified successfully
				if (verified) {
					return 1;
				}
			}

			return 0;
		} catch (err) {
			console.log('err');
			throw err;
		}
	}
};
