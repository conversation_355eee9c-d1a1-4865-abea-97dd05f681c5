const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const authService = require('../../services/advertiser/authService');
const authValidation = require('../../validations/advertiser/authValidation');

const validateBusinessRegisterid =
    require('../../helper/validatebusinessRegisterId').validateBusinessRegisterid;
module.exports = {
    /* Register */
    async register(req, res) {
        try {
            const valid = await authValidation.registerValidation(req);

            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }

            let userRegister = await authService.register(req, res);

            if (userRegister === 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILALEREADYEXIST,
                    status.ERROR
                );
            } else if (userRegister === 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.PROFILEIMAGEREQUIRED,
                    status.ERROR
                );
            } else if (userRegister === 2) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.WRONGMFAINSERTED,
                    status.ERROR
                );
            } else if (userRegister) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.ADVERTISERSUCCESS,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async getTimezones(req, res) {
        try {
            const timezones = await authService.getTimezones();

            if (timezones.length === 0) {
                return response(
                    res,
                    status.INTERNALSERVERERRORSTATUS,
                    [],
                    "No timezones found",
                    status.ERROR
                );
            }

            return response(
                res,
                status.SUCCESSSTATUS,
                timezones,
                message.TIMEZONEFETCHSUCCESSFULLY,
                status.SUCCESS
            );
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                error.message,
                status.ERROR
            );
        }
    },
    /* Login api */
    async login(req, res) {
        try {
            const valid = await authValidation.loginValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            let userLogin = await authService.login(req, res);

            if (userLogin == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.INVALIDCREDENTIAL,
                    status.ERROR
                );
            } else if (userLogin === 2) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.WRONGMFAINSERTED,
                    status.ERROR
                );
            } else if (userLogin === 3) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Your advertiser account is under review. We will send you an email once your account is approved.",
                    status.ERROR
                );
            } else if (userLogin === 4) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Sorry, you cannot login as your account has been rejected by admin.",
                    status.ERROR
                );
            } else if (userLogin) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    userLogin,
                    message.LOGINSUCCESS,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    async sendVerificationCode(req, res) {
        try {
            const valid = await authValidation.advertiserEmailValidation(req);

            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }

            let data = await authService.sendVerificationCode(req, res);
            if (data == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILALEREADYEXIST,
                    status.ERROR
                );
            } else if (data == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.INCORRECTPASSWORD,
                    status.ERROR
                );
            } else if (data == 2) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.VENUENOTFOUND,
                    status.ERROR
                );
            } else if (data == 3) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILSENTSUCCESSFULLY,
                    status.SUCCESS
                );
            } else if (data == 4) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CONNECTVENUENOTALLOWED,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async verifyOTP(req, res) {
        try {
            let data = await authService.verifyOTP(req, res);

            if (data == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.WRONGOTPINSERTED,
                    status.ERROR
                );
            } else if (data == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.OTPVERIFIED,
                    status.ERROR
                );
            } else if (data) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    data,
                    message.OTPVERIFIED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Verify MFA */
    async verifyMFA(req, res) {
        try {
            const valid = await authValidation.verifyMFAValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            var codeVerify = await authService.verifyMFA(req, res);

            if (codeVerify == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.INVALIDCREDENTIAL,
                    status.ERROR
                );
            } else if (codeVerify == 1) {
                //response on wrong code
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.WRONGMFAINSERTED,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    codeVerify,
                    '',
                    status.SUCCESS
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    /* Verify Password */
    async verifyPassword(req, res) {
        try {
            const valid = await authValidation.verifyPasswordValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            var verify = await authService.verifyPassword(req, res);

            if (verify == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.INCORRECTPASSWORD,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.PASSWORDVERIFY,
                    status.SUCCESS
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    /* setup MFA */
    async setupMFA(req, res) {
        try {
            const valid = await authValidation.setupMFA(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            var codeVerify = await authService.setupMFA(req, res);
            if (codeVerify) {
                //response of otp verify success
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.MFASENT,
                    status.SUCCESS
                );
            } else if (codeVerify == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILNOTFOUND,
                    status.ERROR
                );
            } else {
                //response on wrong code
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    /* Forgot Password */
    async forgotPassword(req, res) {
        try {
            const valid = await authValidation.forgotPasswordValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            let forgotPasswordRes = await authService.forgotPassword(req, res);
            if (forgotPasswordRes === 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILNOTFOUND,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILSENTSUCCESSFULLY,
                    status.SUCCESS
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    //verify token
    async verifyToken(req, res) {
        try {
            // validation
            const valid = await authValidation.verifyTokensValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            // service
            let isValid = await authService.verifyToken(req);
            if (isValid) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    { isValid },
                    message.TOKEN_VALID,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    { isValid },
                    message.TOKEN_EXPIRED,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    //reset password
    async resetPassword(req, res) {
        try {
            // validation
            const valid = await authValidation.resetPasswordValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            let resetPasswordRes = await authService.resetPasswsord(req, res);

            if (resetPasswordRes === 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.TOKEN_EXPIRED,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.PASSWORD_RESET,
                    status.SUCCESS
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    /* Edit Profile */
    async updateProfile(req, res) {
        try {
            const valid = await authValidation.editProfileValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }

            let updatedVenueUser = await authService.updateProfile(req, res);
            if (updatedVenueUser == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.USERNOTFOUND,
                    status.ERROR
                );
            } else if (updatedVenueUser == 2) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.EMAILALEREADYEXIST,
                    status.ERROR
                );
            } else if (updatedVenueUser) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    updatedVenueUser,
                    message.PROFILEUPDATED,
                    status.SUCCESS
                );
            } else {
                res, status.SUCCESSSTATUS, {}, message.SOMETHINGWENTWRONG, status.ERROR;
            }
        } catch (error) {
            console.log(error);
            // response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async getProfileDetails(req, res) {
        try {
            // service
            let details = await authService.getProfileDetails(req);
            if (details) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    details,
                    message.PROFILERETRIVED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.USERNOTFOUND,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async myVenueDetails(req, res) {
        try {
            // service
            let details = await authService.myVenueDetails(req);
            if (details) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    details,
                    message.MYVENUELISTFETCH,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.USERNOTFOUND,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* get devicelist */
    async logout(req, res) {
        try {
            let logOutRes = await authService.logout(req, res);

            if (logOutRes === 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.LOGOUTSUCCESS,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Verify MFA */
    async verifyMFAOtp(req, res) {
        try {
            const valid = await authValidation.verifyMFAOtpValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            var codeVerify = await authService.verifyMFAOtp(req, res);

            if (codeVerify == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.INVALIDCREDENTIAL,
                    status.ERROR
                );
            } else if (codeVerify == 1) {
                //response on wrong code
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.WRONGMFAINSERTED,
                    status.ERROR
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    codeVerify,
                    message.OTPVERIFIED,
                    status.SUCCESS
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Verify MFA */
    async saveSubscription(req, res) {
        try {
            const valid = await authValidation.saveSubscriptionValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    valid.error.message,
                    status.ERROR
                );
            }
            let data = await authService.saveSubscription(req, res);

            if (data == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.USERNOTFOUND,
                    status.ERROR
                );
            } else if (data) {
                //response on wrong code
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    data,
                    message.SUBSCRPTIONADD,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* delete venue details */
    async deleteVenueDetails(req, res) {
        try {
            let data = await authService.deleteVenue(req, res);

            if (data == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.VENUEDELETEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    }
};
