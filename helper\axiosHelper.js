const axios = require('axios');

const getData = async (url, headers) => {
	try {
		const getData = await axios.get(url, {
			headers: { ...headers, 'Content-Type': 'application/json' }
		});
		return getData;
	} catch (error) {
		console.log('error in axios ', error);
		return error;
	}
};

const postData = async (url, data, headers) => {
	try {
		const postData = await axios.post(url, data, {
			headers: { ...headers, 'Content-Type': 'application/json' }
		});
		return postData.data;
	} catch (error) {
		console.log('error in axios ', error);
		return error;
	}
};

const putData = async (url, data, headers) => {
	try {
		const postData = await axios.put(url, data, {
			headers: { ...headers, 'Content-Type': 'application/json' }
		});
		return postData.data;
	} catch (error) {
		console.log('error in axios ', error);
		return error;
	}
};

module.exports = {
	getData,
	postData,
	putData
};
