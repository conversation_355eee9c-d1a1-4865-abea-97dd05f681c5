const message = require('../../../config/cmsMessage.js').cmsMessage;
const status = require('../../../config/status.js').status;
const openingHoursService = require('../../../services/v2/venue/openingHoursService.js');
const openingHoursValidation = require('../../../validations/v2/venue/openingHoursValidation');

module.exports = {
	async convertExistingVenueOpeningHours(req, res) {
		try {
			let result = await openingHoursService.convertExistingVenueOpeningHours(
				req,
				res
			);
			if (result == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OPERATINGHOURSUPDATESUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getVenueOpeningHours(req, res) {
		try {
			const valid = await openingHoursValidation.list(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let result = await openingHoursService.getVenueOpeningHours(req, res);
			if (result) {
				return response(
					res,
					status.SUCCESSSTATUS,
					result,
					message.OPENINGHOURSFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async updateVenueOpeningHours(req, res) {
		try {
			const valid = await openingHoursValidation.update(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let result = await openingHoursService.updateVenueOpeningHours(req, res);
			if (result) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OPENINGHOURSUPDATESUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async updateVenueCategoryOpeningHours(req, res) {
		try {
			const valid = await openingHoursValidation.updateCategoryHours(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let result = await openingHoursService.updateVenueCategoryOpeningHours(
				req,
				res
			);
			if (result == -1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.HOURSLISTOUTSIDEOPENINGHOURS,
					status.ERROR
				);
			} else if (result) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CATEGORYHOURSUPDATESUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
