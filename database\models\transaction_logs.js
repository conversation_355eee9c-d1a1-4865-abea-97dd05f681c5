'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class transaction_logs extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// transaction_logs.belongsTo(models.user, { foreignKey: 'userID' });
			// transaction_logs.belongsTo(models.bar, { foreignKey: 'barID' });
			// transaction_logs.belongsTo(models.order, { foreignKey: 'orderID' });
		}
	}
	transaction_logs.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			orderID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'orders',
					key: 'id'
				}
			},
			amout: DataTypes.FLOAT,
			transaction_type: DataTypes.TEXT,
			transactionID: DataTypes.TEXT,
			transferID: DataTypes.TEXT,
			refundTransactionID: DataTypes.TEXT,
			reversalsTransactionID: DataTypes.TEXT,
			log: DataTypes.TEXT,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE
		},
		{
			sequelize,
			paranoid: true,
			freezeTableName: true,
			modelName: 'transaction_logs'
		}
	);
	return transaction_logs;
};
