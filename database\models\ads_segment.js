'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class ads_segment extends Model {
        static associate(models) {

            models.segment.hasMany(ads_segment, {
                foreignKey: 'segmentID'
            });
            ads_segment.belongsTo(models.segment, {
                foreignKey: 'segmentID'
            });

            models.ads.hasMany(ads_segment, {
                foreignKey: 'adsID'
            });
            ads_segment.belongsTo(models.ads, {
                foreignKey: 'adsID'
            });

            models.bar.hasMany(ads_segment, {
                foreignKey: 'barID'
            });
            ads_segment.belongsTo(models.bar, {
                foreignKey: 'barID'
            });
        }
    }

    ads_segment.init(
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true
            },
            segmentID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'segment',
                    key: 'id'
                }
            },
            adsID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'ads',
                    key: 'id'
                }
            },
            barID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'bar',
                    key: 'id'
                }
            }
        },
        {
            sequelize,
            modelName: 'ads_segment',
            timestamps: false,
            freezeTableName: true
        }
    );

    return ads_segment;
};