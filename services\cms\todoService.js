/*Messages,status code and services require*/
require("dotenv").config();
const Bcryptjs = require("bcryptjs"); /* For encryption and decryption */
const Admin = require("../../database/models").admin; //import modal always Capital
const todoModel = require("../../database/models").todo_table;
const constant = require("../../config/constant");
const Sequelize = require("sequelize");

module.exports = {
  /* Change Password */
  async add(req, res) {
    try {
      let data = await todoModel.create({
        priority: req.body.priority,
        task: req.body.task,
        status: req.body.status,
        due_date: req.body.due_date,
        assigned_to: req.body.assigned_to,
      });

      if (data) {
        return 1;
      }
      return 0;
    } catch (err) {
      console.log("err", err);
      throw err;
    }
  },

  // // delete todo
  // async deleteTodo(req, res) {
  //   try {
  //     let data = await todoModel.findOne({
  //       where: {
  //         id: req.body.id,
  //       },
  //     });
  //     if (!data) {
  //       return 0;
  //     }
  //     await todoModel.destroy({
  //       where: {
  //         id: req.body.id,
  //       },
  //     });

  //     return 1;
  //   } catch (err) {
  //     console.log("err", err);
  //     throw err;
  //   }
  // },
  //update todo
  async update(req, res) {
    try {
      let data = await todoModel.findOne({
        where: {
          id: req.body.id,
        },
      });
      if (!data) {
        return 0;
      }
      await todoModel.update(
        {
          priority: req.body.priority,
          task: req.body.task,
          status: req.body.status,
          due_date: req.body.due_date,
          assigned_to: req.body.assigned_to,
        },
        {
          where: {
            id: req.body.id,
          },
        }
      );

      return 1;
    } catch (err) {
      console.log("err", err);
      throw err;
    }
  },
  //update todo
  async updateStatus(req, res) {
    try {
      let data = await todoModel.findOne({
        where: {
          id: req.body.id,
        },
      });
      if (!data) {
        return 0;
      }
      //update to completed
      await todoModel.update(
        {
          status: "3",
        },
        {
          where: {
            id: req.body.id,
          },
        }
      );

      return 1;
    } catch (err) {
      console.log("err", err);
      throw err;
    }
  },
  //list todo
  async list(req) {
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;
    let order = req.body.order === undefined ? "DESC" : req.body.order;
    let sort_by = req.body.sortBy === undefined ? "id" : req.body.sortBy;
    let where = [];
    if (req.body.status) {
      where.push({
        status: req.body.status,
      });
    }
    //1 pending 2 Inprogress 3 completed 4 hold

    return await todoModel.findAndCountAll({
      where: where,
      include: [
        {
          model: Admin,
          attributes: ["first_name", "last_name", "profile_image", "email"],
        },
      ],
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      offset: offset,
      limit: limit,
      attributes: { exclude: ["createdAt", "updatedAt", "deletedAt"] },
    });
  },
};
