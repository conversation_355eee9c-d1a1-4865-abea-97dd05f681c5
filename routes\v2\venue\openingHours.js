var express = require('express');
var router = express.Router();

/* Controller */
const OpeningHoursController = require('../../../controllers/v2/venue/openingHoursController.js');
const {
	venueAuthorization,
	isVenueUserConnectedWithBar
} = require('../../../middleware/venueAuth.js');

router.get(
	'/convertExistingVenueOpeningHours',
	OpeningHoursController.convertExistingVenueOpeningHours
);

router.post(
	'/getVenueOpeningHours',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	OpeningHoursController.getVenueOpeningHours
);

router.post(
	'/updateVenueOpeningHours',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	OpeningHoursController.updateVenueOpeningHours
);

router.post(
	'/updateVenueCategoryOpeningHours',
	venueAuthorization,
	isVenueUserConnectedWithBar,
	OpeningHoursController.updateVenueCategoryOpeningHours
);

module.exports = router;
