var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/venueAuth").venueAuthorization;

/*require for pickuplocation  response*/
const PromoCodeController = require("../../controllers/venue/promocodeController");

/* promocode add API */
router.post("/add", Authorization, PromoCodeController.add);

/* promocode list API */
router.post("/list", Authorization, PromoCodeController.list);

/* pickuplocation edit API */
router.put("/edit", Authorization, PromoCodeController.edit);

/* promocode delete API */
router.delete("/delete", Authorization, PromoCodeController.delete);

module.exports = router;
