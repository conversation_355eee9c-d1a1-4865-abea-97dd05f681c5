/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const moment = require('moment');
const Admin = require('../../database/models').admin; //import modal always Capital
const AdminToken = require('../../database/models').admin_login_session;
const constant = require('../../config/constant');
const helper = require('../../helper/generalHelper');

module.exports = {
	/* Change Password */
	async changePassword(req, res) {
		let findAdmin = await Admin.findOne({
			where: {
				id: req.admin_id,
				deletedAt: null
			}
		});
		//email not present
		if (!findAdmin) {
			return 0;
		}
		if (!Bcryptjs.compareSync(req.body.old_password, findAdmin.password)) {
			return 2; //old password is incorrect
		}
		const generatedSalt = Bcryptjs.genSaltSync(10);
		let encryptedPassword = await Bcryptjs.hash(
			req.body.new_password,
			generatedSalt
		);
		//update password
		return await Admin.update(
			{
				password: encryptedPassword,
				password_updated_at: new Date()
			},
			{
				where: {
					id: findAdmin.id
				}
			}
		);
	},
	/* Change Password Date */
	async changePasswordDate(req, res) {
		let findAdmin = await AdminToken.findOne({
			include: [
				{
					model: Admin
				}
			],
			where: {
				access_token: req.headers.authorization
			}
		});
		if (findAdmin.admin.password_updated_at) {
			return moment(findAdmin.admin.password_updated_at).fromNow();
		}
		return 0;
	},
	/* Get Device List */
	async getDeviceList(req, res) {
		try {
			let findAdmin = await Admin.findOne({
				where: {
					id: req.admin_id
				}
			});
			if (!findAdmin) {
				return 0;
			}

			let Token = await AdminToken.findAll({
				attributes: [
					'id',
					'access_token',
					'device_type',
					'device_name',
					'device_location',
					'createdAt'
				],
				where: {
					admin_id: findAdmin.id
				},
				order: [['id', 'DESC']]
			});

			let currentDevice = Token.filter((value, index) => {
				if (value.access_token === req.headers.authorization) {
					delete Token[index].dataValues.access_token;
					return value;
				}
			});

			let otherDevices = Token.filter((value, index) => {
				if (value.access_token) {
					delete Token[index].dataValues.access_token;
					return value;
				}
			});

			return { currentDevice: currentDevice, otherDevice: otherDevices };
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	/* Get MFA Code */
	async getMFACode(req, res) {
		try {
			let findAdmin = await AdminToken.findOne({
				include: [
					{
						model: Admin
					}
				],
				where: {
					access_token: req.headers.authorization
				}
			});

			if (!findAdmin) {
				return 0;
			}

			let qrCode = await helper.s3GetImage(
				constant.AWSS3ADMINQRCODEFOLDER + findAdmin?.admin?.mfa_qr_code
			);

			return qrCode;
		} catch (err) {
			console.log('err');
			throw err;
		}
	},
	/* Logout Device */
	async logoutDevice(req, res) {
		try {
			// find user
			let findAdmin = await AdminToken.findOne({
				where: {
					access_token: req.headers.authorization
				}
			});
			// delete token
			let Token = await AdminToken.destroy({
				where: {
					id: req.body.id,
					admin_id: findAdmin.admin_id
				}
			});
			//token not present
			if (Token) {
				return 1;
			}

			return 0;
		} catch (err) {
			console.log('err');
			throw err;
		}
	}
};
