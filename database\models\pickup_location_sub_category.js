'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class pickup_location_sub_category extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// pos_conf.belongsTo(models.admin, {
			//   foreignKey: "assigned_to",
			//   targetKey: "id",
			//   onDelete: "CASCADE",
			// });
			pickup_location_sub_category.belongsTo(models.pickup_location, {
				foreignKey: 'pickupLocationId'
			});
			pickup_location_sub_category.hasMany(models.product, {
				foreignKey: 'subCategoryID', // Adjust if your foreign key has a different name
				sourceKey: 'subCategoryID'
			});
		}
	}
	pickup_location_sub_category.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			pickupLocationID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'pickup_location',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'pickup_location_sub_category',
			timestamps: true,
			freezeTableName: true
		}
	);
	return pickup_location_sub_category;
};
