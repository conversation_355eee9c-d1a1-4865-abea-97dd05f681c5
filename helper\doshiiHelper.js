const jwt = require('jsonwebtoken');
require('dotenv').config();
const moment = require('moment');
const { getData, postData, putData } = require('./axiosHelper');
const commonFunction = require('../common/commonFunction');
const constant = require('../config/constant');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const ProductModel = require('../database/models').product;
const PickUpLocationModel = require('../database/models').pickup_location;
const ProductVariantType = require('../database/models').product_variant_types;
const ItemActiveHoursModel = require('../database/models').itemActiveHours;
const ProductExtraModel = require('../database/models').product_extras;
const ProductVariantsModel = require('../database/models').product_variants;
const PickupLocationSubCategoryModel =
	require('../database/models').pickup_location_sub_category;
const TransactionLogsModel = require('../database/models').transaction_logs;
const TransactionErrLogsModel =
	require('../database/models').transaction_err_logs;
const BarSubCategoryOpeningHoursModel =
	require('../database/models').bar_sub_category_opening_hours;
const BarSubCategoryWaitTimeUTCModel =
	require('../database/models').bar_sub_category_wait_time_utc;
const BarOpeningHoursModel = require('../database/models').bar_opening_hours;
const ProductFoodOptionsModel =
	require('../database/models').product_food_options;
const BarSubCategoryOpeningHoursUTCModel =
	require('../database/models').bar_sub_category_opening_hours_utc;
const OrderModel = require('../database/models').orders;
const OrderItemsModel = require('../database/models').order_items;
const OrderTableNumberModel = require('../database/models').orderTableNumber;
const OrderTaxModel = require('../database/models').order_tax;
const OrderRefundTaxModel = require('../database/models').order_refund_tax;
const UserNotificationModel = require('../database/models').user_notification;
const OrderItemWaitTimeNotificationsModel =
	require('../database/models').order_item_wait_time_notifications;
const SurcountModel = require('../database/models').pos_surcounts;
const ProductVariantTypesModel =
	require('../database/models').product_variant_types;
const ProductVariantSubTypesModel =
	require('../database/models').product_variant_sub_types;
const BarSubCategoryWaitTimeModel =
	require('../database/models').bar_sub_category_wait_time;
const SubCategoryModel = require('../database/models/').sub_category;
const BarCategorySequenceModel =
	require('../database/models').bar_category_sequence;
const BarProductSequenceModel =
	require('../database/models').bar_product_sequence;
const BarModel = require('../database/models').bar;
const {
	convertOpeningHours,
	timeChunkArray
} = require('../common/commonFunction');
const s3UploadDoshiiFile = require('../middleware/multerAwsUploadDoshiiFile');

const getDoshiAuthToken = () => {
	const jwtSign = jwt.sign(
		{ clientId: process.env.DOSHI_APP_CLIENT_ID, timestamp: moment().unix() },
		process.env.DOSHI_APP_CLIENT_SECRET
	);
	if (jwtSign) {
		return jwtSign;
	} else {
		return null;
	}
};

async function createSubCategoryOpeningHours(barId, subcategoryId) {
	const bar = await BarModel.findOne({
		where: {
			id: barId,
			isDeleted: 'No'
		},
		include: [
			{ model: BarOpeningHoursModel },
			{
				model: PickUpLocationModel,
				where: {
					isDefault: '1',
					isDeleted: 'No'
				}
			}
		],
		order: [[BarOpeningHoursModel, 'weekDay', 'ASC']]
	});

	if (bar?.pickup_locations[0]?.id) {
		await PickupLocationSubCategoryModel.create({
			barID: barId,
			subCategoryID: subcategoryId,
			pickupLocationID: bar?.pickup_locations[0]?.id
		});
	} else {
		const defaultPickupLocation = await PickUpLocationModel.create({
			barID: barId,
			address: 'Collect at counter',
			isDefault: '1'
		});
		await PickupLocationSubCategoryModel.create({
			barID: barId,
			subCategoryID: subcategoryId,
			pickupLocationID: defaultPickupLocation?.id
		});
	}

	for (const hour of bar.bar_opening_hours) {
		const { weekDay, openingHours, closingHours, isClosed, barID } = hour;

		const subID = subcategoryId;

		const newSub = await BarSubCategoryOpeningHoursModel.create({
			barID,
			weekDay,
			openingHours,
			closingHours,
			isClosed,
			subCategoryID: subID,
			timeZone: bar.timezone
		});

		const timeChunks = timeChunkArray(
			newSub.openingHours,
			newSub.closingHours,
			'60'
		).map((c) => ({
			barID,
			subCategoryID: subID,
			barSubCategoryOpeningHoursID: newSub.id,
			weekDay: newSub.weekDay,
			waitTime: '00:10:00',
			startTime: c.startTime,
			endTime: c.endTime
		}));

		if (timeChunks.length)
			await BarSubCategoryWaitTimeModel.bulkCreate(timeChunks);

		const utcSubSlots = convertOpeningHours(newSub, bar.timezone, weekDay).map(
			(s) => ({
				...s,
				barID,
				isClosed,
				subCategoryID: subID,
				barSubCategoryOpeningHoursID: newSub.id
			})
		);

		await BarSubCategoryOpeningHoursUTCModel.bulkCreate(utcSubSlots);

		const utcWaitTimes = utcSubSlots.flatMap((s) =>
			timeChunkArray(s.openingHours, s.closingHours, '60').map((c) => ({
				barID,
				subCategoryID: subID,
				barSubCategoryOpeningHoursID: s.barSubCategoryOpeningHoursID,
				weekDay: s.weekDay,
				waitTime: '00:10:00',
				startTime: c.startTime,
				endTime: c.endTime
			}))
		);

		if (utcWaitTimes.length)
			await BarSubCategoryWaitTimeUTCModel.bulkCreate(utcWaitTimes);
	}
}

const isVenueIdValid = async (locationId) => {
	try {
		const token = getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'doshii-location-id': locationId
		};

		let isVenueValid = await getData(
			process.env.DOSHI_APP_BASE_URL + 'locations/' + locationId,
			headers
		);
		return isVenueValid.data;
	} catch (error) {
		console.log(error);
		return 0;
	}
};

const getDoshiMenuItems = async (locationId) => {
	try {
		const token = getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		let getMenu = await getData(
			process.env.DOSHI_APP_BASE_URL +
				'locations/' +
				locationId +
				'/menu?filtered',
			headers
		);
		return getMenu.data;
	} catch (error) {
		console.log('error in helper function ', error);
		return 0;
	}
};

const getDoshiiReferralLink = async (venueData, venueOwnerDetails) => {
	try {
		const token = getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip'
		};
		const reqBody = {
			merchantContactEmail: venueData.dataValues.email,
			merchantContactFirstName: venueOwnerDetails.dataValues.first_name,
			merchantContactLastName: venueOwnerDetails.dataValues.last_name,
			merchantPhone: venueOwnerDetails.dataValues.mobile,
			merchantVenueName: venueData.dataValues.restaurantName,
			partnerLocationId: '' + venueData.dataValues.id,
			referrerName: venueData.dataValues.manager,
			referrerEmail: venueData.dataValues.email,
			triggerMerchantEmail: true
		};

		let getReferral = await postData(
			process.env.DOSHI_APP_BASE_URL + 'referrals',
			reqBody,
			headers
		);
		return getReferral;
	} catch (error) {
		console.log('error in helper function ', error);
		return 0;
	}
};

const getDoshiMenuProductById = async (url, locationId) => {
	try {
		const token = await getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		let getMenu = await getData(url, headers);
		return getMenu.data;
	} catch (error) {
		console.log('error in helper function ', error);
		return 0;
	}
};

const updateMenuDoshiiWebhook = async (webhookRequestData) => {
	try {
		const { action, itemUri, locationId, posId, type } =
			webhookRequestData.data;
		let barDetails = await BarModel.findOne({
			where: {
				venueId: locationId
			}
		});
		if (!barDetails) {
			return 2; // unable to find the bar details, Try again!
		} else {
			const barId = barDetails.id;

			if (action === 'updated') {
				// global surcounts
				if (type === 'surcounts') {
					// const getSurcounts = await getDoshiMenuProductById(
					// 	itemUri,
					// 	locationId
					// );
					// const checkIfSurcountExists = await SurcountModel.findOne({
					// 	where: {
					// 		posID: getSurcounts.posId,
					// 		isDeleted: 'No'
					// 	}
					// });
					// if (!checkIfSurcountExists) {
					// 	await SurcountModel.create({
					// 		posID: getSurcounts.posId,
					// 		name: getSurcounts.name,
					// 		surcount_type: getSurcounts.type,
					// 		amount: getSurcounts.amount
					// 	});
					// } else {
					// 	await SurcountModel.update(
					// 		{
					// 			posID: getSurcounts.posId,
					// 			name: getSurcounts.name,
					// 			surcount_type: getSurcounts.type,
					// 			amount: getSurcounts.amount
					// 		},
					// 		{
					// 			where: {
					// 				posID: getSurcounts.posId
					// 			}
					// 		}
					// 	);
					// }
					return;
				}
				const getUpdatedMenuItem = await getDoshiMenuProductById(
					itemUri.replace(/\s/g, ''),
					locationId
				);
				const productData = await ProductModel.findOne({
					where: {
						posID: posId,
						barID: barId,
						isDeleted: 'No'
					}
				});
				if (!getUpdatedMenuItem) {
					return 6;
				}
				if (!productData) {
					//add product to the db with bar id
					const subCategoryIds = [];
					if (getUpdatedMenuItem.tags && getUpdatedMenuItem.tags.length > 0) {
						for (let tag of getUpdatedMenuItem.tags) {
							const isSubCategoryAvailable = await SubCategoryModel.findOne({
								where: {
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: locationId,
									isDeleted: 'No'
								}
							});
							if (!isSubCategoryAvailable) {
								const createSubCategoryId = await SubCategoryModel.create({
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: locationId
								});
								await createSubCategoryOpeningHours(
									barId,
									createSubCategoryId?.id
								);
								subCategoryIds.push(createSubCategoryId);
							} else {
								subCategoryIds.push(isSubCategoryAvailable.dataValues);
							}
						}
					}

					let productImage = '';
					if (getUpdatedMenuItem.imageUri) {
						productImage =
							getUpdatedMenuItem.imageUri.split('/')[
								getUpdatedMenuItem.imageUri.split('/').length - 1
							];
						await s3UploadDoshiiFile(
							getUpdatedMenuItem.imageUri,
							constant.AWSS3PRODUCTFOLDER + productImage
						);
					} else {
						productImage = null;
					}

					const productData = {
						barID: barId,
						categoryID: -1,
						subCategoryID: subCategoryIds[0].id || null, //subcategory model
						name: getUpdatedMenuItem.name, //name
						description: getUpdatedMenuItem.description,
						price: getUpdatedMenuItem.unitPrice / 100,
						posID: posId,
						avatar: productImage || '',
						serviceType: barDetails.dataValues.serviceType
						// status:
						// 	getUpdatedMenuItem.availability === 'available'
						// 		? 'Active'
						// 		: 'Inactive'
						//pickupLocationID: req.body.pickuplocation_id, //pickup location model
						//avatar: req.file?.originalname ? req.file.originalname : null,
					};

					if (getUpdatedMenuItem.availability) {
						productData.status =
							getUpdatedMenuItem.availability === 'available'
								? 'Active'
								: 'Inactive';
					} else {
						productData.status = 'Active';
					}
					const createProduct = await ProductModel.create(productData);
					if (createProduct) {
						if (subCategoryIds[0].id) {
							const pickupLocationData =
								await PickupLocationSubCategoryModel.findOne({
									where: {
										barID: barId,
										subCategoryID: subCategoryIds[0].id
									}
								});
							await ProductModel.update(
								{ pickupLocationID: pickupLocationData?.pickupLocationID },
								{
									where: {
										id: createProduct?.id
									}
								}
							);
						}

						let productVariants;
						if (getUpdatedMenuItem.options.length > 0) {
							for (let option of getUpdatedMenuItem.options) {
								if (option.variants.length > 0) {
									if (option.min == '0') {
										let variants = [];
										for (let variant of option.variants) {
											const variantData = {
												productID: createProduct.id,
												extraItem: variant.name,
												price: variant.price / 100,
												posID: variant.posId,
												productOptionposID: option.posId,
												productOptionName: option.name,
												status: 'Active'
											};
											variants.push(variantData);
										}
										productVariants = await ProductExtraModel.bulkCreate(
											variants
										);
									} else {
										// await ProductVariantTypesModel.destroy({
										// 	where: {
										// 		posID: option.posId
										// 	}
										// });
										// await ProductVariantSubTypesModel.destroy({
										// 	where: {
										// 		variantType_posID: option.posId
										// 	}
										// })
										const createOptions = await ProductVariantTypesModel.create(
											{
												productID: createProduct.id,
												label: option.name,
												status: 'Active',
												posID: option.posId,
												serviceType: barDetails.dataValues.serviceType
											}
										);
										if (createOptions) {
											if (option.variants.length > 0) {
												for (let variant of option.variants) {
													console.log('variant  ', variant);
													await ProductVariantSubTypesModel.create({
														productVariantTypeID: createOptions.id,
														variantType: variant.name,
														price: variant.price / 100,
														status: 'Active',
														posID: variant.posId,
														variantType_posID: createOptions.posID
													});
												}
											}
										}
									}
								}
							}
						}
						// if (getUpdatedMenuItem.surcounts.length > 0) {
						// 	getUpdatedMenuItem.surcounts.map(async (surcount) => {
						// 		const checkSurcountExists = await SurcountModel.findOne({
						// 			where: {
						// 				posID: surcount.posId,
						// 				isDeleted: 'No'
						// 			}
						// 		});
						// 		if (checkSurcountExists) {
						// 			await SurcountModel.update(
						// 				{
						// 					posID: surcount.posId,
						// 					name: surcount.name,
						// 					surcount_type: surcount.type,
						// 					amount: surcount.amount,
						// 					productID: createProduct.id
						// 				},
						// 				{
						// 					where: {
						// 						productID: createProduct.id
						// 					}
						// 				}
						// 			);
						// 		} else {
						// 			await SurcountModel.create({
						// 				posID: surcount.posId,
						// 				name: surcount.name,
						// 				surcount_type: surcount.type,
						// 				amount: surcount.amount,
						// 				productID: createProduct.id
						// 			});
						// 		}
						// 	});
						// }
						if (getUpdatedMenuItem.dietary?.length > 0) {
							let foodOptionsArr = [];
							getUpdatedMenuItem.dietary.map(async (dietaryItm) => {
								if (dietaryItm == 'gluten-free') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 2
									});
								} else if (dietaryItm == 'vegan') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 5
									});
								} else if (dietaryItm == 'vegetarian') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 6
									});
								}
							});
							await ProductFoodOptionsModel.bulkCreate(foodOptionsArr);
						}
						return {
							product: createProduct,
							code: 1
						};
					} else {
						return 6; // unable to create the product
					}
				} else {
					const subCategoryIds = [];
					if (getUpdatedMenuItem.tags && getUpdatedMenuItem.tags.length > 0) {
						for (let tag of getUpdatedMenuItem.tags) {
							const isSubCategoryAvailable = await SubCategoryModel.findOne({
								where: {
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: locationId,
									isDeleted: 'No'
								}
							});
							if (!isSubCategoryAvailable) {
								const createSubCategoryId = await SubCategoryModel.create({
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: locationId
								});
								await createSubCategoryOpeningHours(
									barId,
									createSubCategoryId?.id
								);
								subCategoryIds.push(createSubCategoryId);
							} else {
								subCategoryIds.push(isSubCategoryAvailable.dataValues);
							}
						}
					}
					// if (getUpdatedMenuItem.surcounts.length > 0) {
					// 	getUpdatedMenuItem.surcounts.map(async (surcount) => {
					// 		const checkSurcountExists = await SurcountModel.findOne({
					// 			where: {
					// 				posID: surcount.posId
					// 			}
					// 		});
					// 		if (checkSurcountExists) {
					// 			await SurcountModel.update(
					// 				{
					// 					posID: surcount.posId,
					// 					name: surcount.name,
					// 					surcount_type: surcount.type,
					// 					amount: surcount.amount,
					// 					productID: productData.id
					// 				},
					// 				{
					// 					where: {
					// 						productID: productData.id
					// 					}
					// 				}
					// 			);
					// 		} else {
					// 			await SurcountModel.create({
					// 				posID: surcount.posId,
					// 				name: surcount.name,
					// 				surcount_type: surcount.type,
					// 				amount: surcount.amount,
					// 				productID: productData.id
					// 			});
					// 		}
					// 	});
					// }
					await ProductFoodOptionsModel.destroy({
						where: {
							productID: productData.id
						}
					});
					if (getUpdatedMenuItem.dietary?.length > 0) {
						let foodOptionsArr = [];
						getUpdatedMenuItem.dietary.map(async (dietaryItm) => {
							if (dietaryItm == 'gluten-free') {
								foodOptionsArr.push({
									productID: productData.id,
									foodOptionID: 2
								});
							} else if (dietaryItm == 'vegan') {
								foodOptionsArr.push({
									productID: productData.id,
									foodOptionID: 5
								});
							} else if (dietaryItm == 'vegetarian') {
								foodOptionsArr.push({
									productID: productData.id,
									foodOptionID: 6
								});
							}
						});
						await ProductFoodOptionsModel.bulkCreate(foodOptionsArr);
					}
					let productImage = '';
					if (getUpdatedMenuItem.imageUri) {
						productImage =
							getUpdatedMenuItem.imageUri.split('/')[
								getUpdatedMenuItem.imageUri.split('/').length - 1
							];
						await s3UploadDoshiiFile(
							getUpdatedMenuItem.imageUri,
							constant.AWSS3PRODUCTFOLDER + productImage
						);
					} else {
						productImage = null;
					}
					const updatedProductData = {
						barID: barId,
						categoryID: -1,
						subCategoryID: subCategoryIds[0].id || null, //subcategory model
						name: getUpdatedMenuItem.name, //name
						description: getUpdatedMenuItem.description,
						price: getUpdatedMenuItem.unitPrice / 100,
						posID: posId,
						avatar: productImage || null
						// status:
						// 	getUpdatedMenuItem.availability === 'available'
						// 		? 'Active'
						// 		: 'Inactive'
					};
					if (getUpdatedMenuItem.availability) {
						updatedProductData.status =
							getUpdatedMenuItem.availability === 'available'
								? 'Active'
								: 'Inactive';
					} else {
						updatedProductData.status = 'Active';
					}
					if (subCategoryIds[0].id) {
						const pickupLocationData =
							await PickupLocationSubCategoryModel.findOne({
								where: {
									barID: barId,
									subCategoryID: subCategoryIds[0].id
								}
							});
						updatedProductData.pickupLocationID =
							pickupLocationData?.pickupLocationID;
					}
					const [updateProduct] = await ProductModel.update(
						updatedProductData,
						{
							where: {
								id: productData.id,
								posID: posId,
								isDeleted: 'No'
							}
						}
					);

					if (updateProduct !== -1) {
						// extras delete {soft Delete}
						await ProductExtraModel.update(
							{
								isDeleted: 'Yes'
							},
							{
								where: {
									productID: productData.id
								}
							}
						);
						// variants delete { soft delete}
						const getAllVariantTypes = await ProductVariantTypesModel.findAll({
							where: {
								productID: productData.id
							}
						});
						await ProductVariantTypesModel.update(
							{
								isDeleted: 'Yes'
							},
							{
								where: {
									productID: productData.id
								}
							}
						);

						// subtype delete { soft delete}
						if (getAllVariantTypes) {
							getAllVariantTypes.map(async (variantType) => {
								await ProductVariantSubTypesModel.update(
									{
										isDeleted: 'Yes'
									},
									{
										where: {
											productVariantTypeID: variantType.id
										}
									}
								);
							});
						}

						if (getUpdatedMenuItem.options.length > 0) {
							for (let option of getUpdatedMenuItem.options) {
								if (option.min == '0') {
									if (option.variants.length > 0) {
										for (let variant of option.variants) {
											const variantData = {
												productID: productData.id,
												extraItem: variant.name,
												price: variant.price / 100,
												posID: variant.posId,
												productOptionposID: option.posId,
												productOptionName: option.name,
												status: 'Active'
											};
											await ProductExtraModel.create(variantData);
										}
									}
								} else {
									const createOptions = await ProductVariantTypesModel.create({
										productID: productData.id,
										label: option.name,
										status: 'Active',
										posID: option.posId,
										serviceType: barDetails.dataValues.serviceType
									});
									if (createOptions) {
										if (option.variants.length > 0) {
											for (let variant of option.variants) {
												await ProductVariantSubTypesModel.create({
													productVariantTypeID: createOptions.id,
													variantType: variant.name,
													price: variant.price / 100,
													status: 'Active',
													posID: variant.posId,
													variantType_posID: createOptions.posID
												});
											}
										}
									}
								}
							}
						}
						return 5; // update the product
					}
					return 8; // unable to update the product
				}
			} else if (action === 'deleted') {
				if (type === 'surcounts') {
					// const findSurcounts = await SurcountModel.findOne({
					// 	where: {
					// 		posID: posId,
					// 		isDeleted: 'No'
					// 	}
					// });

					// if (findSurcounts) {
					// 	await SurcountModel.update(
					// 		{
					// 			isDeleted: 'Yes'
					// 		},
					// 		{
					// 			where: {
					// 				posID: posId
					// 			}
					// 		}
					// 	);
					// } else {
					// 	return;
					// }
					return;
				}
				const findProductToDelete = await ProductModel.findOne({
					where: {
						posID: posId,
						categoryID: -1,
						isDeleted: 'No'
					}
				});
				if (!findProductToDelete) {
					return 2; // no product found
				} else {
					const deleteProduct = await ProductModel.update(
						{
							isDeleted: 'Yes'
						},
						{
							where: {
								posID: posId
							}
						}
					);
					// await SurcountModel.update(
					// 	{
					// 		isDeleted: 'Yes'
					// 	},
					// 	{
					// 		where: {
					// 			productID: findProductToDelete.dataValues.id
					// 		}
					// 	}
					// );

					let productVariantType = await ProductVariantTypesModel.findAll({
						where: {
							productID: findProductToDelete.dataValues.id
						}
					});

					if (productVariantType.length > 0) {
						await ProductVariantTypesModel.update(
							{
								isDeleted: 'Yes'
							},
							{
								where: {
									productID: findProductToDelete.dataValues.id
								}
							}
						);

						productVariantType.map(async (variantType) => {
							await ProductVariantSubTypesModel.update(
								{
									isDeleted: 'Yes'
								},
								{
									where: {
										productVariantTypeID: variantType.id
									}
								}
							);
						});
					}

					await ProductExtraModel.update(
						{
							isDeleted: 'Yes'
						},
						{
							where: {
								productID: findProductToDelete.dataValues.id
							}
						}
					);
					return deleteProduct;
				}
			} else {
				console.log('Unhandled Event data', webhookRequestData);
			}
		}
	} catch (error) {
		console.log('error ', error);
		return 0;
	}
};

const updateCategoryDoshiiWebhook = async (webhookRequestData) => {
	try {
		const { locationId, action, category } = webhookRequestData.data;
		let barDetails = await BarModel.findOne({
			where: {
				venueId: locationId
			}
		});
		if (!barDetails) {
			return 2;
		} else {
			if (action === 'updated') {
				const checkIfCategoryExists = await SubCategoryModel.findOne({
					where: {
						name: category.category,
						categoryID: -1,
						isDeleted: 'No',
						posID: locationId
					}
				});
				if (!checkIfCategoryExists) {
					const createSubCategory = await SubCategoryModel.create({
						name: category.category,
						categoryID: -1,
						status: 'Active',
						posID: locationId
					});

					if (createSubCategory) {
						await createSubCategoryOpeningHours(
							barDetails?.id,
							createSubCategory?.id
						);
						await category.productIds.map(async (id) => {
							await ProductModel.update(
								{
									subCategoryID: createSubCategory.dataValues.id
								},
								{
									where: {
										posID: id
									}
								}
							);
						});
						return 1;
					} else {
						return 0;
					}
				} else {
					await category.productIds.map(async (id) => {
						await ProductModel.update(
							{
								subCategoryID: checkIfCategoryExists.dataValues.id
							},
							{
								where: {
									posID: id
								}
							}
						);
					});
					return 1;
				}
			} else if (action === 'deleted') {
				const checkIfCategoryExists = await SubCategoryModel.findOne({
					where: {
						name: category.category,
						categoryID: -1,
						isDeleted: 'No',
						posID: locationId
					}
				});
				if (checkIfCategoryExists) {
					await category.productIds.map(async (id) => {
						await ProductModel.update(
							{
								subCategoryID: 66
							},
							{
								where: {
									posID: id,
									isDeleted: 'No'
								}
							}
						);
					});
					const deleteCategory = await SubCategoryModel.update(
						{
							isDeleted: 'Yes'
						},
						{
							where: {
								name: category.category,
								posID: category.id,
								categoryID: -1,
								posID: locationId
							}
						}
					);
					if (deleteCategory) {
						return 1;
					} else {
						return 0;
					}
				}
			} else {
				console.log('unhandled event ', webhookRequestData);
				return;
			}
		}
	} catch (error) {
		console.log('error ', error);
		return error;
	}
};

const setCategoryAndProductSequence = async (locationId, barId) => {
	try {
		const getMenuFromDoshii = await getDoshiMenuItems(locationId);
		const barDetails = await BarModel.findOne({
			where: {
				id: barId
			}
		});
		if (barDetails) {
			// await SubCategoryModel.update(
			// 	{
			// 		isDeleted: 'Yes'
			// 	},
			// 	{ where: { posID: locationId } }
			// );
			await BarCategorySequenceModel.destroy({
				where: { barId: barId }
			});

			await BarProductSequenceModel.destroy({
				where: {
					barId,
					categoryId: -1
				}
			});

			if (getMenuFromDoshii && getMenuFromDoshii?.categories?.length > 0) {
				let menuCategories = getMenuFromDoshii.categories;
				menuCategories.sort(function (a, b) {
					return a.displayOrder - b.displayOrder;
				});
				for (const category of menuCategories) {
					const findCategory = await SubCategoryModel.findOne({
						where: {
							name: category.category,
							categoryID: -1,
							posID: locationId,
							isDeleted: 'No'
						}
					});
					if (findCategory) {
						await BarCategorySequenceModel.create({
							barId: barId,
							subCategorySequence: category.displayOrder,
							subCategoryId: findCategory.dataValues.id
						});
						const pickupLocationData =
							await PickupLocationSubCategoryModel.findOne({
								where: {
									barID: barId,
									subCategoryID: findCategory.dataValues.id
								}
							});

						await category.productIds.map(async (id) => {
							await ProductModel.update(
								{
									pickupLocationID: pickupLocationData?.pickupLocationID,
									subCategoryID: findCategory.dataValues.id
								},
								{
									where: {
										posID: id
									}
								}
							);
						});

						if (category?.productIds?.length > 0) {
							let productIds = category?.productIds.reverse();
							await productIds?.map(async (product, index) => {
								let productData = await ProductModel.findOne({
									where: { posID: product, barID: barId, isDeleted: 'No' }
								});
								if (productData) {
									await BarProductSequenceModel.create({
										barId,
										productSequence: index + 1,
										subCategoryId: findCategory.dataValues.id,
										productId: productData.id,
										categoryId: -1
									});
								}
							});
						}
					} else {
						const createSubCategory = await SubCategoryModel.create({
							name: category.category,
							categoryID: -1,
							status: 'Active',
							posID: locationId
						});
						if (createSubCategory) {
							await createSubCategoryOpeningHours(barId, createSubCategory?.id);
							await BarCategorySequenceModel.create({
								barId: barId,
								subCategorySequence: category.displayOrder,
								subCategoryId: createSubCategory.dataValues.id
							});
							const pickupLocationData =
								await PickupLocationSubCategoryModel.findOne({
									where: {
										barID: barId,
										subCategoryID: createSubCategory.dataValues.id
									}
								});
							await category.productIds.map(async (id) => {
								await ProductModel.update(
									{
										pickupLocationID: pickupLocationData?.pickupLocationID,
										subCategoryID: createSubCategory.dataValues.id
									},
									{
										where: {
											posID: id
										}
									}
								);
							});

							if (category?.productIds?.length > 0) {
								let productIds = category?.productIds.reverse();
								await productIds?.map(async (product, index) => {
									let productData = await ProductModel.findOne({
										where: { posID: product, barID: barId, isDeleted: 'No' }
									});
									if (productData) {
										await BarProductSequenceModel.create({
											barId,
											productSequence: index + 1,
											subCategoryId: createSubCategory.dataValues.id,
											productId: productData.id,
											categoryId: -1
										});
									}
								});
							}
						}
					}
				}
				return '1';
			} else {
				console.log(
					'No categories in POS for venue ' + locationId + ' - ' + barId
				);
			}
		}
	} catch (error) {
		console.log('error ', error);
		return error;
	}
};

const updateOrderFromDoshiiWebhook = async (webhookRequestData) => {
	try {
		const { locationId, uri, id, version, externalOrderRef } =
			webhookRequestData;
		let barDetails = await BarModel.findOne({
			where: {
				venueId: locationId,
				posStatus: '1'
			}
		});
		if (!barDetails) {
			return 0;
		}
		const getDoshiOrderDetails = await getDoshiMenuProductById(uri, locationId);
		const findOrder = await OrderModel.findOne({
			where: {
				posOrderId: id
			}
		});
		if (findOrder) {
			if (webhookRequestData.status === 'accepted') {
				await OrderModel.update(
					{
						posOrderStatus: 'Accepted',
						posOrderVersion: version
					},
					{
						where: {
							posOrderId: id
						}
					}
				);
			} else if (webhookRequestData.status === 'rejected') {
				await OrderModel.update(
					{
						posOrderStatus: 'Rejected',
						posOrderVersion: version
					},
					{
						where: {
							posOrderId: id
						}
					}
				);
				if (findOrder.posCheckInId) {
					var checkInPayload = {
						status: 'cancelled',
						type: 'table'
					};
					await updateCheckInDoshii(
						checkInPayload,
						barDetails.dataValues.venueId,
						findOrder.posCheckInId
					);
				}
			} else if (webhookRequestData.status === 'complete') {
				await OrderModel.update(
					{
						posOrderStatus: 'Completed',
						posOrderVersion: version
					},
					{
						where: {
							posOrderId: id
						}
					}
				);
				return;
			} else if (webhookRequestData.status === 'venue_cancelled') {
				await OrderModel.update(
					{
						posOrderStatus: 'Venue Cancelled',
						posOrderVersion: version
					},
					{
						where: {
							posOrderId: id
						}
					}
				);
				if (findOrder.posCheckInId) {
					var checkInPayload = {
						status: 'cancelled',
						type: 'table'
					};
					await updateCheckInDoshii(
						checkInPayload,
						barDetails.dataValues.venueId,
						findOrder.posCheckInId
					);
				}
				await orderCancel(id);
				return;
			} else {
				await OrderModel.update(
					{
						posOrderStatus: 'Other',
						posOrderVersion: version
					},
					{
						where: {
							posOrderId: id
						}
					}
				);
				return;
			}
		} else {
			console.log('no product found !!');
			return;
		}
	} catch (error) {
		console.log('error ', error);
		return error;
	}
};

const createOrderInDoshii = async (payload, locationId, orderId) => {
	try {
		const token = await getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		const result = await postData(
			process.env.DOSHI_APP_BASE_URL + `orders`,
			payload,
			headers
		);
		if (!result) {
			return 0;
		} else {
			await OrderModel.update(
				{
					posOrderId: result.id,
					posOrderStatus: result.status
				},
				{
					where: {
						id: orderId
					}
				}
			);
			return result;
		}
	} catch (error) {
		console.log('error ', error);
		throw error;
	}
};

const updateOrderInDoshii = async (payload, locationId, orderId) => {
	try {
		const token = await getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		const result = await putData(
			process.env.DOSHI_APP_BASE_URL + `orders/` + orderId,
			payload,
			headers
		);
		if (!result) {
			return 0;
		} else {
			let orderStatus = 'Other';
			if (result.status == 'cancelled') {
				orderStatus = 'Cancelled';
			}
			await OrderModel.update(
				{
					posOrderStatus: orderStatus
				},
				{
					where: {
						posOrderId: orderId
					}
				}
			);
			return result;
		}
	} catch (error) {
		throw error;
	}
};

const updateTransactionInDoshii = async (
	payload,
	locationId,
	transactionId
) => {
	try {
		const token = await getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		const result = await putData(
			process.env.DOSHI_APP_BASE_URL + `transactions/` + transactionId,
			payload,
			headers
		);
		if (!result) {
			return 0;
		} else {
			return result;
		}
	} catch (error) {
		throw error;
	}
};

const updateCheckInDoshii = async (payload, locationId, orderId) => {
	try {
		const token = await getDoshiAuthToken();
		let headers = {
			Authorization: 'Bearer ' + token,
			Accept: 'application/json',
			'Accept-encoding': 'gzip',
			'doshii-location-id': locationId
		};

		const result = await putData(
			process.env.DOSHI_APP_BASE_URL + `checkins/` + orderId,
			payload,
			headers
		);
		if (!result) {
			return 0;
		} else {
			let orderStatus = 'Other';
			if (result.status == 'cancelled') {
				orderStatus = 'Cancelled';
			}
			await OrderModel.update(
				{
					posOrderStatus: orderStatus
				},
				{
					where: {
						posOrderId: orderId
					}
				}
			);
			return result;
		}
	} catch (error) {
		throw error;
	}
};

const getOrderDetailsById = async (orderId) => {
	try {
		const getOrderDetails = await OrderItemsModel.findOne({
			where: {
				orderID: orderId
			}
		});
		if (getOrderDetails) {
			const getProductId = await ProductModel.findOne({
				where: {
					id: getOrderDetails.dataValues.productID,
					isDeleted: 'No'
				}
			});
		}
	} catch (error) {
		console.log('error ', error);
		return;
	}
};

const orderCancel = async (posOrderId) => {
	try {
		const orderDetails = await OrderModel.findOne({
			where: {
				posOrderId: posOrderId
			},
			include: [
				{
					attributes: [
						'id',
						'orderID',
						'productID',
						'price',
						'chargeAmount',
						'quantity',
						'refundedQuantity',
						'refundAmount'
					],
					model: OrderItemsModel
				},
				{
					attributes: [
						'id',
						'orderID',
						'name',
						'percentage',
						'taxID',
						'amount'
					],
					model: OrderTaxModel
				},
				{
					attributes: [
						'id',
						'orderID',
						'name',
						'percentage',
						'taxID',
						'amount'
					],
					model: OrderRefundTaxModel
				}
			]
		});
		if (orderDetails) {
			const barID = orderDetails.barID;
			if (orderDetails.order_items.length > 0) {
				orderDetails.order_items.map(async (orderItem) => {
					let isCanceled = 'Yes';

					let itemRefundedAmount = orderItem.chargeAmount * orderItem.quantity;

					await OrderItemsModel.update(
						{
							isCanceled: isCanceled,
							refundAmount: itemRefundedAmount,
							refundedQuantity: orderItem.quantity,
							updatedAt: new Date()
						},
						{
							returning: true,
							where: {
								id: orderItem.id
							}
						}
					);

					// Delete wait time notification
					await OrderItemWaitTimeNotificationsModel.destroy({
						where: {
							orderItemID: orderItem.id
						}
					});
				});
				let orderTaxData = [];
				if (orderDetails.order_taxes) {
					orderDetails.order_taxes.map((tax) => {
						totalRefundAmount = totalRefundAmount + tax.amount;
						orderTaxData.push({
							barID: orderDetails.barID,
							orderID: orderDetails.id,
							name: tax.name,
							percentage: tax.percentage,
							taxID: tax.id,
							amount: tax.amount
						});
					});
				}
				if (orderTaxData.length) {
					await OrderRefundTaxModel.bulkCreate(orderTaxData);
				}

				// var totalRefundAmount =
				// 	orderDetails.total - orderDetails.transactionFee;
				// totalRefundAmount = Number(totalRefundAmount);

				var totalRefundAmount = Number(orderDetails.total);

				const barData = await BarModel.findOne({
					attributes: [
						'id',
						'restaurantName',
						'managerName',
						'email',
						'countryCode',
						'mobile',
						'stripeID',
						'venueId',
						'attachedPosConfig',
						'posStatus'
					],
					where: {
						id: barID
					}
				});
				await OrderModel.update(
					{
						refundStatus: 'Refunded',
						isCanceled: 'Yes'
					},
					{
						where: {
							id: orderDetails.id,
							barID: barID
						}
					}
				);
				await stripe.refunds.create(
					{
						charge: orderDetails.transactionID,
						amount: Math.round(totalRefundAmount * 100),
						// reverse_transfer: true,
						refund_application_fee: false
					},
					{
						stripeAccount: barData.stripeID
					},
					async function (stripeErr, refundsData) {
						if (stripeErr == null) {
							if (refundsData.status == 'succeeded') {
								await TransactionLogsModel.create({
									orderID: orderDetails.id,
									amout: totalRefundAmount,
									transaction_type: 'cancel_order',
									refundTransactionID: refundsData.id,
									reversalsTransactionID: refundsData.transfer_reversal,
									log: JSON.stringify(refundsData),
									userID: orderDetails.userID,
									barID: barID,
									createdAt: new Date()
								});
							} else {
								await TransactionErrLogsModel.create({
									orderID: orderDetails.id,
									amout: totalRefundAmount,
									transaction_type: 'cancel_order_err',
									log: JSON.stringify(refundsData),
									userID: orderDetails.userID,
									barID: barID,
									createdAt: new Date()
								});
							}
						} else {
							await TransactionErrLogsModel.create({
								orderID: orderDetails.id,
								amout: totalRefundAmount,
								transaction_type: 'cancel_order_err',
								log: JSON.stringify(stripeErr),
								userID: orderDetails.userID,
								barID: barID,
								createdAt: new Date()
							});
						}
					}
				);
				// Table code separated for new change
				let table_code = await OrderTableNumberModel.findOne({
					attributes: ['tableCode'],
					where: { orderID: orderDetails.id },
					order: [['id', 'DESC']]
				});
				const tableCode = table_code && table_code.tableCode;

				var message =
					orderDetails.orderServiceType === 'PICKUP'
						? `Your order ${orderDetails.pickupCode} has been refunded.`
						: `Your order for table #${tableCode} has been refunded.`;

				var notification_type = 'orderRefund';

				if (message != '') {
					await UserNotificationModel.create({
						barID: barID,
						notification_type: notification_type,
						userID: orderDetails.userID,
						dataID: orderDetails.id,
						message: message,
						createdAt: new Date()
					});
					await commonFunction.orderStatusNotificationToUser(
						orderDetails.userID,
						orderDetails.id,
						notification_type,
						message,
						'Order Refund'
					);
				}
				return 1;
			} else {
				return 0;
			}
		} else {
			return 0;
		}
	} catch (error) {
		console.log('error ', error);
		return;
	}
};

const getDoshiiFilteredAppMenu = async (venueId, barId) => {
	const getMenuFromDoshii = await getDoshiMenuItems(venueId);
	const barDetails = await BarModel.findOne({
		where: {
			id: barId
		}
	});
	if (getMenuFromDoshii) {
		const productArray = getMenuFromDoshii.products;
		const categoryArray = getMenuFromDoshii.categories;

		if (categoryArray.length !== 0) {
			for (let category of categoryArray) {
				const checkIfCategoryExists = await SubCategoryModel.findOne({
					where: {
						name: category.category,
						categoryID: -1,
						isDeleted: 'No',
						posID: venueId
					}
				});
				if (!checkIfCategoryExists) {
					const createSubCategory = await SubCategoryModel.create({
						name: category.category,
						categoryID: -1,
						status: 'Active',
						posID: venueId
					});

					if (createSubCategory) {
						await createSubCategoryOpeningHours(barId, createSubCategory?.id);
						const pickupLocationData =
							await PickupLocationSubCategoryModel.findOne({
								where: {
									barID: barId,
									subCategoryID: createSubCategory.dataValues.id
								}
							});
						await category.productIds.map(async (id) => {
							await ProductModel.update(
								{
									pickupLocationID: pickupLocationData?.pickupLocationID,
									subCategoryID: createSubCategory.dataValues.id
								},
								{
									where: {
										posID: id
									}
								}
							);
						});
					}
				}
			}
		}

		if (productArray.length > 0) {
			for (let product of productArray) {
				const isProductAvailable = await ProductModel.findOne({
					where: {
						posID: product.posId,
						barID: barId,
						isDeleted: 'No'
					}
				});
				if (!isProductAvailable) {
					const subCategoryIds = [];
					if (product.tags && product.tags.length > 0) {
						for (let tag of product.tags) {
							const isSubCategoryAvailable = await SubCategoryModel.findOne({
								where: {
									categoryID: -1,
									name: tag,
									isDeleted: 'No',
									status: 'Active',
									posID: venueId
								}
							});
							if (!isSubCategoryAvailable) {
								const createSubCategoryId = await SubCategoryModel.create({
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: venueId
								});
								await createSubCategoryOpeningHours(
									barId,
									createSubCategoryId?.id
								);
								subCategoryIds.push(createSubCategoryId);
							} else {
								subCategoryIds.push(isSubCategoryAvailable.dataValues);
							}
						}
					}

					let productImage = '';
					if (product.imageUri) {
						productImage =
							product.imageUri.split('/')[
								product.imageUri.split('/').length - 1
							];
						await s3UploadDoshiiFile(
							product.imageUri,
							constant.AWSS3PRODUCTFOLDER + productImage
						);
					} else {
						productImage = null;
					}

					const productData = {
						barID: barId,
						categoryID: -1,
						subCategoryID: subCategoryIds[0].id || null, //subcategory model
						name: product.name, //name
						description: product.description,
						price: product.unitPrice / 100,
						posID: product.posId,
						avatar: productImage || '',
						serviceType: barDetails.dataValues.serviceType,
						status: product.availability
							? product.availability === 'available'
								? 'Active'
								: 'Inactive'
							: 'Active'
						//pickupLocationID: req.body.pickuplocation_id, //pickup location model
						//avatar: req.file?.originalname ? req.file.originalname : null,
					};
					const createProduct = await ProductModel.create(productData);
					if (createProduct) {
						if (subCategoryIds[0].id) {
							const pickupLocationData =
								await PickupLocationSubCategoryModel.findOne({
									where: {
										barID: barId,
										subCategoryID: subCategoryIds[0].id
									}
								});
							await ProductModel.update(
								{ pickupLocationID: pickupLocationData?.pickupLocationID },
								{
									where: {
										id: createProduct?.id
									}
								}
							);
						}
						let productVariants;
						if (product.options.length > 0) {
							for (let option of product.options) {
								if (option.variants.length > 0) {
									if (option.min == '0') {
										let variants = [];
										for (let variant of option.variants) {
											const variantData = {
												productID: createProduct.id,
												extraItem: variant.name,
												price: variant.price / 100,
												posID: variant.posId,
												productOptionposID: option.posId,
												productOptionName: option.name,
												status: 'Active'
											};
											variants.push(variantData);
										}
										productVariants = await ProductExtraModel.bulkCreate(
											variants
										);
									} else {
										const createOptions = await ProductVariantTypesModel.create(
											{
												productID: createProduct.id,
												label: option.name,
												status: 'Active',
												posID: option.posId,
												serviceType: barDetails.dataValues.serviceType
											}
										);
										if (createOptions) {
											if (option.variants.length > 0) {
												for (let variant of option.variants) {
													await ProductVariantSubTypesModel.create({
														productVariantTypeID: createOptions.id,
														variantType: variant.name,
														price: variant.price / 100,
														status: 'Active',
														posID: variant.posId,
														variantType_posID: createOptions.posID
													});
												}
											}
										}
									}
								}
							}
						}
						// if (product.surcounts.length > 0) {
						// 	product.surcounts.map(async (surcount) => {
						// 		const checkSurcountExists = await SurcountModel.findOne({
						// 			where: {
						// 				posID: surcount.posId,
						// 				isDeleted: 'No'
						// 			}
						// 		});
						// 		if (checkSurcountExists) {
						// 			await SurcountModel.update(
						// 				{
						// 					posID: surcount.posId,
						// 					name: surcount.name,
						// 					surcount_type: surcount.type,
						// 					amount: surcount.amount,
						// 					productID: createProduct.id
						// 				},
						// 				{
						// 					where: {
						// 						productID: createProduct.id
						// 					}
						// 				}
						// 			);
						// 		} else {
						// 			await SurcountModel.create({
						// 				posID: surcount.posId,
						// 				name: surcount.name,
						// 				surcount_type: surcount.type,
						// 				amount: surcount.amount,
						// 				productID: createProduct.id
						// 			});
						// 		}
						// 	});
						// }
						if (product.dietary?.length > 0) {
							let foodOptionsArr = [];
							product.dietary.map(async (dietaryItm) => {
								if (dietaryItm == 'gluten-free') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 2
									});
								} else if (dietaryItm == 'vegan') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 5
									});
								} else if (dietaryItm == 'vegetarian') {
									foodOptionsArr.push({
										productID: createProduct.id,
										foodOptionID: 6
									});
								}
							});
							await ProductFoodOptionsModel.bulkCreate(foodOptionsArr);
						}
					}
				} else {
					const subCategoryIds = [];
					if (product.tags && product.tags.length > 0) {
						for (let tag of product.tags) {
							const isSubCategoryAvailable = await SubCategoryModel.findOne({
								where: {
									categoryID: -1,
									name: tag,
									isDeleted: 'No',
									status: 'Active',
									posID: venueId
								}
							});
							if (!isSubCategoryAvailable) {
								const createSubCategoryId = await SubCategoryModel.create({
									categoryID: -1,
									name: tag,
									status: 'Active',
									posID: venueId
								});
								await createSubCategoryOpeningHours(
									barId,
									createSubCategoryId?.id
								);
								subCategoryIds.push(createSubCategoryId);
							} else {
								subCategoryIds.push(isSubCategoryAvailable.dataValues);
							}
						}
					}
					// if (product.surcounts.length > 0) {
					// 	product.surcounts.map(async (surcount) => {
					// 		const checkSurcountExists = await SurcountModel.findOne({
					// 			where: {
					// 				posID: surcount.posId
					// 			}
					// 		});
					// 		if (checkSurcountExists) {
					// 			await SurcountModel.update(
					// 				{
					// 					posID: surcount.posId,
					// 					name: surcount.name,
					// 					surcount_type: surcount.type,
					// 					amount: surcount.amount,
					// 					productID: isProductAvailable.dataValues.id
					// 				},
					// 				{
					// 					where: {
					// 						productID: isProductAvailable.dataValues.id
					// 					}
					// 				}
					// 			);
					// 		} else {
					// 			await SurcountModel.create({New products na add pr product ma and required options ma ser
					// 				posID: surcount.posId,
					// 				name: surcount.name,
					// 				surcount_type: surcount.type,
					// 				amount: surcount.amount,
					// 				productID: isProductAvailable.dataValues.id
					// 			});
					// 		}
					// 	});
					// }

					let productImage = '';
					if (product.imageUri) {
						productImage =
							product.imageUri.split('/')[
								product.imageUri.split('/').length - 1
							];
						await s3UploadDoshiiFile(
							product.imageUri,
							constant.AWSS3PRODUCTFOLDER + productImage
						);
					} else {
						productImage = null;
					}

					const updatedProductData = {
						barID: barId,
						categoryID: -1,
						subCategoryID: subCategoryIds[0].id || null, //subcategory model
						name: product.name, //name
						description: product.description,
						price: product.unitPrice / 100,
						posID: product.posId,
						avatar: productImage || '',
						status: product.availability
							? product.availability === 'available'
								? 'Active'
								: 'Inactive'
							: 'Active'
					};
					if (subCategoryIds[0].id) {
						const pickupLocationData =
							await PickupLocationSubCategoryModel.findOne({
								where: {
									barID: barId,
									subCategoryID: subCategoryIds[0].id
								}
							});
						updatedProductData.pickupLocationID =
							pickupLocationData?.pickupLocationID;
					}
					const [updateProduct] = await ProductModel.update(
						updatedProductData,
						{
							where: {
								id: isProductAvailable.dataValues.id,
								posID: product.posId,
								isDeleted: 'No'
							}
						}
					);

					if (updateProduct !== -1) {
						await ProductExtraModel.update(
							{
								isDeleted: 'Yes'
							},
							{
								where: {
									productID: isProductAvailable.dataValues.id
								}
							}
						);

						const getAllVariantTypes = await ProductVariantTypesModel.findAll({
							where: {
								productID: isProductAvailable.dataValues.id
							}
						});
						await ProductVariantTypesModel.update(
							{
								isDeleted: 'Yes'
							},
							{
								where: {
									productID: isProductAvailable.dataValues.id
								}
							}
						);

						// subtype delete { soft delete}
						if (getAllVariantTypes) {
							getAllVariantTypes.map(async (variantType) => {
								await ProductVariantSubTypesModel.update(
									{
										isDeleted: 'Yes'
									},
									{
										where: {
											productVariantTypeID: variantType.id
										}
									}
								);
							});
						}

						if (product.options.length > 0) {
							for (let option of product.options) {
								if (option.min == '0') {
									if (option.variants.length > 0) {
										for (let variant of option.variants) {
											const variantData = {
												productID: isProductAvailable.dataValues.id,
												extraItem: variant.name,
												price: variant.price / 100,
												posID: variant.posId,
												productOptionposID: option.posId,
												productOptionName: option.name,
												status: 'Active'
											};
											await ProductExtraModel.create(variantData);
										}
									}
								} else {
									const createOptions = await ProductVariantTypesModel.create({
										productID: isProductAvailable.dataValues.id,
										label: option.name,
										status: 'Active',
										posID: option.posId,
										serviceType: barDetails.dataValues.serviceType
									});
									if (createOptions) {
										if (option.variants.length > 0) {
											for (let variant of option.variants) {
												await ProductVariantSubTypesModel.create({
													productVariantTypeID: createOptions.id,
													variantType: variant.name,
													price: variant.price / 100,
													status: 'Active',
													posID: variant.posId,
													variantType_posID: createOptions.posID
												});
											}
										}
									}
								}
							}
						}
						await ProductFoodOptionsModel.destroy({
							where: {
								productID: isProductAvailable.dataValues.id
							}
						});
						if (product.dietary?.length > 0) {
							let foodOptionsArr = [];
							product.dietary.map(async (dietaryItm) => {
								if (dietaryItm == 'gluten-free') {
									foodOptionsArr.push({
										productID: isProductAvailable.dataValues.id,
										foodOptionID: 2
									});
								} else if (dietaryItm == 'vegan') {
									foodOptionsArr.push({
										productID: isProductAvailable.dataValues.id,
										foodOptionID: 5
									});
								} else if (dietaryItm == 'vegetarian') {
									foodOptionsArr.push({
										productID: isProductAvailable.dataValues.id,
										foodOptionID: 6
									});
								}
							});
							await ProductFoodOptionsModel.bulkCreate(foodOptionsArr);
						}
					}
				}
			}
		}
		return 1;
	} else {
		return 0; // unable to fetch the menu from doshii, Try again
	}
};

module.exports = {
	getDoshiAuthToken,
	isVenueIdValid,
	getDoshiMenuItems,
	getDoshiiReferralLink,
	getDoshiMenuProductById,
	updateMenuDoshiiWebhook,
	updateCategoryDoshiiWebhook,
	updateOrderFromDoshiiWebhook,
	createOrderInDoshii,
	updateOrderInDoshii,
	updateTransactionInDoshii,
	updateCheckInDoshii,
	getOrderDetailsById,
	orderCancel,
	getDoshiiFilteredAppMenu,
	setCategoryAndProductSequence
};
