// server.js
require("dotenv").config()
const http = require('http');

const express = require('express');
const logger = require('morgan');
const cors = require('cors');
const actuator = require('express-actuator');
const bodyParser = require('body-parser');

const bootstrap = require('./bootstrap');

async function startServer() {
    try {
        await bootstrap();

        const port = process.env.PORT || 3000;
        const app = express();
        const server = http.createServer(app);

        app.use(actuator({ infoGitMode: 'full' }));

        // view engine setup
        app.use(logger('dev'));
        app.use(cors());

        app.use(bodyParser.json({ limit: '10mb', extended: true }));
        app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

        require("./database/models");
        // require('./config/cronJob');
        require('./helper/response');
        require('./routes')(app);

        server.listen(port, () => console.log(`🚀 Server running on port ${port}`));
    }
    catch (err) {
        console.error('❌ Startup failed:', err);
        process.exit(1);
    }
}

startServer();
