const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class order_items extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			order_items.belongsTo(models.product, { foreignKey: 'productID' });

			models.orders.hasMany(order_items, { foreignKey: 'orderID' });
			order_items.belongsTo(models.orders, { foreignKey: 'orderID' });
		}
	}
	order_items.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			orderID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'orders',
					key: 'id'
				}
			},
			productID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'product',
					key: 'id'
				}
			},
			orderStatus: DataTypes.ENUM(
				'New',
				'Preparing',
				'Pickup',
				'Pickedup',
				'NotPickedup',
				'Intoxicated'
			),
			PreparingStartTime: DataTypes.DATE,
			ReadyTime: DataTypes.DATE,
			PickedupTime: DataTypes.DATE,
			price: DataTypes.FLOAT,
			chargeAmount: DataTypes.FLOAT,
			discountedAmount: DataTypes.FLOAT,
			quantity: DataTypes.INTEGER,
			specialRequest: DataTypes.TEXT,
			isCanceled: DataTypes.ENUM('Yes', 'No'),
			refundedQuantity: DataTypes.INTEGER,
			refundAmount: DataTypes.FLOAT,
			newRefundAmount: DataTypes.FLOAT,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No')
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'order_items',
			timestamps: true
		}
	);
	return order_items;
};
