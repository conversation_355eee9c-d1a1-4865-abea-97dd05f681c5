'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class discount_users extends Model {
		static associate(models) {
			models.discount.hasMany(discount_users, {
				foreignKey: 'discountID'
			});
			discount_users.belongsTo(models.discount, {
				foreignKey: 'discountID'
			});

			models.user.hasMany(discount_users, {
				foreignKey: 'userID'
			});
			discount_users.belongsTo(models.user, {
				foreignKey: 'userID'
			});

			models.bar.hasMany(discount_users, {
				foreignKey: 'barID'
			});
			discount_users.belongsTo(models.bar, {
				foreignKey: 'barID'
			});
		}
	}
	discount_users.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			discountID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'discount',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			}
		},
		{
			sequelize,
			modelName: 'discount_users',
			timestamps: false,
			freezeTableName: true
		}
	);
	return discount_users;
};
