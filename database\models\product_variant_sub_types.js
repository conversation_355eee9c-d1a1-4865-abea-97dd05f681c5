"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class product_variant_sub_types extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

    }
  }
  product_variant_sub_types.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true
      },
      productVariantTypeID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "product_variant_types",
          key: "id"
        }
      },
      variantType: DataTypes.STRING,
      posID: DataTypes.STRING,
      variantType_posID: DataTypes.STRING,
      price: DataTypes.FLOAT,
      status: DataTypes.ENUM('Active', 'Inactive'),
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
      isDeleted: DataTypes.ENUM('Yes', 'No'),
      extraSequence: DataTypes.INTEGER
    },
    {
      sequelize,
      modelName: "product_variant_sub_types",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return product_variant_sub_types;
};
