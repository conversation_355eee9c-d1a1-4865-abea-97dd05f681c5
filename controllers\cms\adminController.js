const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;
const adminService = require("../../services/cms/adminService");
const authValidation = require("../../validations/cms/authValidation");

module.exports = {
  /* Sign-In */
  async signIn(req, res) {
    try {
      const valid = await authValidation.loginValidation(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }
      let adminLogin = await adminService.signIn(req, res);
      if (adminLogin == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.INVALIDCREDENTIAL,
          status.ERROR
        );
      } else if (adminLogin == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.USERINACTIVE,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          adminLogin,
          message.LOGINSUCCESS,
          status.SUCCESS
        );
      }
    } catch (error) {
      console.log(error);
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Verify MFA */
  async verifyMFA(req, res) {
    try {
      const valid = await authValidation.verifyMFAValidation(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }
      var codeVerify = await adminService.verifyMFA(req, res);
      if (codeVerify) {
        //response of otp verify success
        return response(
          res,
          status.SUCCESSSTATUS,
          codeVerify,
          message.OTPVERIFIED,
          status.SUCCESS
        );
      } else {
        //response on wrong code
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.WRONGMFAINSERTED,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Verify MFA OTP */
  async verifyMFAOtp(req, res) {
    try {
      const valid = await authValidation.verifyMFAOtpValidation(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }
      var codeVerify = await adminService.verifyMFAOtp(req, res);
      if (codeVerify) {
        //response of otp verify success
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.OTPVERIFIED,
          status.SUCCESS
        );
      } else {
        //response on wrong code
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.WRONGMFAINSERTED,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* setup MFA */
  async setupMFA(req, res) {
    try {
      const valid = await authValidation.setupMFA(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }
      var codeVerify = await adminService.setupMFA(req, res);
      if (codeVerify) {
        //response of otp verify success
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.MFASENT,
          status.SUCCESS
        );
      } else if (codeVerify == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.EMAILNOTFOUND,
          status.SUCCESS
        );
      } else {
        //response on wrong code
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Sign-Out */
  async signOut(req, res) {
    try {
      await adminService.signOut(req, res);
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        message.LOGOUTSUCCESS,
        status.SUCCESS
      );
    } catch (error) {
      console.log(error);
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Forgot Password */
  async forgotPassword(req, res) {
    try {
      const valid = await authValidation.forgotPasswordValidation(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }
      let forgot_password_res = await adminService.forgotPassword(req, res);
      if (forgot_password_res === 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.EMAILNOTFOUND,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.EMAILSENTSUCCESSFULLY,
          status.SUCCESS
        );
      }
    } catch (error) {
      console.log(error);
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  //verify token
  async verifyToken(req, res) {
    try {
      // validation

      const valid = await authValidation.verifyTokensValidation(req);
      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      // service
      let isValid = await adminService.verifyToken(req);
      if (isValid) {
        return response(
          res,
          status.SUCCESSSTATUS,
          { isValid },
          message.TOKENVALID,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          { isValid },
          message.TOKENEXPIRED,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  //reset password
  async resetPassword(req, res) {
    try {
      // validation
      const valid = await authValidation.resetPasswordValidation(req);
      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      let resetPasswordRes = await adminService.resetPasswsord(req, res);

      if (resetPasswordRes === 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TOKENEXPIRED,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.PASSWORD_RESET,
          status.SUCCESS
        );
      }
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  /* Edit Profile */
  async updateProfile(req, res) {
    try {
      const valid = await authValidation.editProfileValidation(req);
      if (valid.error) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          valid.error.message,
          status.ERROR
        );
      }

      let updatedAdmin = await adminService.updateProfile(req, res);

      if (updatedAdmin) {
        return response(
          res,
          status.SUCCESSSTATUS,
          updatedAdmin,
          message.ADMINUPDATED,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.USERNOTFOUND,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      // response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  async getProfileDetails(req, res) {
    try {
      // service
      let details = await adminService.getProfileDetails(req);
      if (details) {
        return response(
          res,
          status.SUCCESSSTATUS,
          details,
          message.PROFILERETRIVED,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.USERNOTFOUND,
          status.ERROR
        );
      }
    } catch (error) {
      console.log(error);
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

};
