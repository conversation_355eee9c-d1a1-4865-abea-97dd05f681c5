// server.js
const express = require('express');
const logger = require('morgan');
const cors = require('cors');
const actuator = require('express-actuator');
const http = require('http');
const body_parser = require('body-parser');
const { response } = require('./helper/response');
const bootstrap = require('./bootstrap');
// const app = require('./app');
const routers = require('./routes');

async function startServer() {
    try {
        await bootstrap();
        const port = process.env.PORT || 3000;
        const app = express();
        const server = http.createServer(app);

        app.use(actuator(
            {
                infoGitMode: 'full'
            }
        ));

        // view engine setup
        app.use(logger('dev'));
        app.use(cors());

        app.use(body_parser.json({ limit: '10mb', extended: true }));
        app.use(body_parser.urlencoded({ limit: '10mb', extended: true }));
        routers(app);

        server.listen(port, () => console.log(`🚀 Server running on port ${port}`));
    } catch (err) {
        console.error('❌ Startup failed:', err);
        process.exit(1);
    }
}

startServer();
