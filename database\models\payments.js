'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class payments extends Model {
        static associate(models) {
            // Define association here
            payments.belongsTo(models.ads, {
                foreignKey: 'ad_id'
            });
        }
    }

    payments.init(
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true
            },
            ad_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            amount: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: false
            },
            currency: {
                type: DataTypes.STRING(3),
                allowNull: false,
                defaultValue: 'usd'
            },
            stripe_payment_id: {
                type: DataTypes.STRING(255),
                allowNull: false
            },
            stripe_customer_id: {
                type: DataTypes.STRING(255),
                allowNull: true
            },
            stripe_card_id: {
                type: DataTypes.STRING(255),
                allowNull: true
            },
            payment_method_type: {
                type: DataTypes.ENUM('card', 'bank_account', 'other'),
                allowNull: true
            },
            card_last4: {
                type: DataTypes.STRING(4),
                allowNull: true
            },
            card_brand: {
                type: DataTypes.STRING(50),
                allowNull: true
            },
            payment_status: {
                type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
                allowNull: false,
                defaultValue: 'pending'
            },
            created_by_id: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            created_by_type: {
                type: DataTypes.ENUM('advertiser', 'venue', 'admin'),
                allowNull: false
            }
        },
        {
            sequelize,
            modelName: 'payments',
            tableName: 'payments',
            timestamps: true,
            underscored: true,
            createdAt: 'created_at',
            updatedAt: 'updated_at'
        }
    );

    return payments;
};
