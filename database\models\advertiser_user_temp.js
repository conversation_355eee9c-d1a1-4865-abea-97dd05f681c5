'use strict';

const constant = require('../../config/constant');
const imageGet = require('../../middleware/multerAwsGet');
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
module.exports = (sequelize, DataTypes) => {
	class advertiser_user_temp extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			advertiser_user_temp.belongsToMany(models.bar, {
				through: models.venue_user_bar,
				foreignKey: 'user_id'
			});
			advertiser_user_temp.belongsToMany(models.subscription, {
				through: models.venue_user_subscription,
				foreignKey: 'user_id'
			});
		}
	}
	advertiser_user_temp.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			contact_name: DataTypes.STRING(255),
			email: DataTypes.STRING(255),			
			connect_email_otp: {
				type: DataTypes.INTEGER
			},
			mfa_code: DataTypes.STRING(200),
			mfa_qr_code: {
				type: DataTypes.STRING(200)				
			},
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
		},
		{
			hooks: {
				beforeCreate: function (user, options) {
					if (user.password) {
						return bcrypt
							.hash(user.password, 10)
							.then((hash) => {
								user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				beforeUpdate: function (advertiser_user_temp, options) {
					if (advertiser_user_temp.password) {
						return bcrypt
							.hash(advertiser_user_temp.password, 10)
							.then((hash) => {
								advertiser_user_temp.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				}
			},

			sequelize,
			paranoid: true,
			underscored: true,
			freezeTableName: true, 
			modelName: 'advertiser_user_temp',
			timestamps: true
		}
	);
	return advertiser_user_temp;
};
