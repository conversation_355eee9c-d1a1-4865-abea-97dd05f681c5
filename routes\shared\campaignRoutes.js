// routes/shared/campaignRoutes.js
const express = require('express');
const advertiserAuth = require('../../middleware/advertiserAuth').advertiserAuthorization;
const venueAuth = require("../../middleware/venueAuth").venueAuthorization;
const CampaignController = require('../../controllers/advertiser/campaignController');

const createCampaignRoutes = (userType) => {
    const router = express.Router();
        console.log(userType,"userType")

    // Choose auth middleware based on user type
    const auth = userType === 'venue' ? venueAuth : advertiserAuth;
    console.log(auth,"auth")
    // Routes
    router.get('/test', (req, res) => res.json({ message: `${userType} campaign working` }));
    
    router.post('/add-campaign', auth, CampaignController.addCampaign);
    router.post('/campaign-list', auth, CampaignController.getCampaignList);
    router.post('/all-campaign-list', CampaignController.getAllCampaignList);

    return router;
};

module.exports = createCampaignRoutes;