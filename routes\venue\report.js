const express = require('express');
const router = express();
const multer = require('multer');
const reportController = require('../../controllers/venue/reportController');
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueUserConnectedWithBar =
    require("../../middleware/venueAuth").isVenueUserConnectedWithBar;

var upload = multer({});

//item summary report
router.post(
    '/itemsummaryreport',
    venueAuthorization,
    IsVenueUserConnectedWithBar,
    reportController.itemsummaryreport
);
//download item summary report
router.post(
    '/downloaditemsummaryreport',
    venueAuthorization,
    IsVenueUserConnectedWithBar,
    reportController.downloaditemsummaryreport
);

router.post("/getCategory", venueAuthorization,IsVenueUserConnectedWithBar, reportController.ListCategory);






module.exports = router;