//CHAEK  PRODUCT LIST API,MIDDEWARE IN ADD ACTIVE ITEM API

var express = require('express');
var router = express.Router();

/* require for Authentication */
const Authorization = require('../../middleware/venueAuth').venueAuthorization;
const IsOperatingHoursValid =
	require('../../middleware/venueAuth').isOperatingHoursValid;

const IsVenueUserConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;
const MulterMiddleware = require('../../middleware/multer');

/*require for product */
const ProductController = require('../../controllers/venue/productController');
const { isVenueUserConnectedWithBar } = require('../../middleware/venueAuth');

/*add item API */
router.post(
	'/add',
	MulterMiddleware.singleProductPic,
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.addProduct
);

/*delete item API */
router.delete(
	'/delete',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.deleteProduct
);

/*change status */
router.put(
	'/change-status',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.chageStatusProduct
);

/* list item Dietary Requirements */
router.post(
	'/foodOptionList',
	Authorization,
	ProductController.getFoodOptionList
);

/*edit item API */
router.post(
	'/edit',
	MulterMiddleware.singleProductPic,
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.editProduct
);

/* list item */
router.post(
	'/getProductList',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getProductList
);

router.post(
	'/getProductListV2',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getProductListV2
);

/* delete product varient */
router.put(
	'/deleteProductVariant',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.deleteProductVariant
);

/* delete product extra */
router.put(
	'/deleteProductExtras',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.deleteProductExtras
);
/* get single product */
router.post(
	'/getSingleProduct',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getSingleProduct
);

/*get item active hours */
router.post(
	'/getItemActiveHours',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.getItemActiveHours
);

/*add-edit active hours */
router.put(
	'/addUpdateItemActiveHours',
	IsOperatingHoursValid,
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.addUpdateItemActiveHours
);

/*delete item active hours */
router.delete(
	'/deleteActiveHours',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.deleteItemActiveHours
);
//product sequence  /** 8. Ability to rearrange menu products under subheadings */
router.post(
	'/productSequence',
	Authorization,
	IsVenueUserConnectedWithBar,
	ProductController.productSequence
);

router.post(
	'/pickuplocation-list',
	Authorization,
	isVenueUserConnectedWithBar,
	ProductController.pickLocationList
);

module.exports = router;
