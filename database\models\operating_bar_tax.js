"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class operating_bar_tax extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      operating_bar_tax.belongsTo(models.tax, { foreignKey: "taxID" });
      models.tax.hasMany(operating_bar_tax, { foreignKey: "taxID" });
      // define association here
    }
  }
  operating_bar_tax.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      taxID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "tax",
          key: "id",
        },
      },
      percentage: DataTypes.FLOAT,
      openingHours: DataTypes.TIME,
      closingHours: DataTypes.TIME,
      weekDay: DataTypes.SMALLINT,
      isClosed: DataTypes.BOOLEAN,
      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },
    {
      sequelize,
      modelName: "operating_bar_tax",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return operating_bar_tax;
};
