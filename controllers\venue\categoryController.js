const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const categoryService = require('../../services/venue/categoryService');
const categoryValidation = require('../../validations/venue/categoryValidation');

module.exports = {
	/* Change Password */
	async getCategory(req, res) {
		try {
			let Categorydata = await categoryService.getCategory();

			if (Categorydata) {
				return response(
					res,
					status.SUCCESSSTATUS,
					Categorydata,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getSubCategory(req, res) {
		try {
			const valid = await categoryValidation.getSubCategoryValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let Categorydata = await categoryService.getSubCategory(req, res);

			if (Categorydata) {
				return response(
					res,
					status.SUCCESSSTATUS,
					Categorydata,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getCategoryList(req, res) {
		try {
			const valid = await categoryValidation.getCategoryListValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let Categorydata = await categoryService.getCategoryList(req, res);

			if (Categorydata) {
				return response(
					res,
					status.SUCCESSSTATUS,
					Categorydata,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async barSubCategorySequence(req, res) {
		try {
			const valid = await categoryValidation.barSubCategorySequenceValidation(
				req
			);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await categoryService.barSubCategorySequence(req, res);

			if (data == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SEQUENCEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INCORRECTSEQUNCE,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getSubCategoryList(req, res) {
		try {
			const valid = await categoryValidation.getSubCategoryListValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await categoryService.getSubCategoryList(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getSubCategoryForManageMenu(req, res) {
		try {
			const valid = await categoryValidation.getSubCategoryForManageMenuValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await categoryService.getSubCategoryForManageMenu(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		}
		catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
