'use strict';
const { Model } = require('sequelize');
const constant = require('../../config/constant');
module.exports = (sequelize, DataTypes) => {
	class couponssubcatids extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			couponssubcatids.belongsTo(models.coupon, { foreignKey: 'couponsID' });
			models.coupon.hasMany(couponssubcatids, { foreignKey: 'couponsID' });
			couponssubcatids.belongsTo(models.sub_category, {
				foreignKey: 'subCategoryID'
			});
			models.sub_category.hasMany(couponssubcatids, {
				foreignKey: 'subCategoryID'
			});
		}
	}
	couponssubcatids.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			couponsID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			}
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'couponsSubCatIds',
			timestamps: false
		}
	);
	return couponssubcatids;
};
