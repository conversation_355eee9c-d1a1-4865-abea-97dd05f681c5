const Joi = require("joi");

module.exports = {
  async loginValidation(req) {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        "any.required": "Email is required",
        "string.email": "Email is not valid",
        "string.empty": "Email is required",
      }),
      password: Joi.string().required().messages({
        "any.required": "Password is required",
        "string.empty": "Password is required",
      }),
    });
    return schema.validate(req.body);
  },
  async verifyMFAValidation(req) {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        "any.required": "Email is required",
        "string.email": "Email is not valid",
        "string.empty": "Email is required",
      }),
      admin_id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.base": "id is required",
      }),
      code: Joi.number().required().messages({
        "any.required": "id is required",
        "number.base": "id is required",
      }),
      device_name: Joi.string().allow(null, "").messages({
        "string.base": "Device name must be string",
      }),
      device_location: Joi.string().allow(null, "").messages({
        "string.base": "Device location must be string",
      }),
      device_type: Joi.number().allow(null, "").messages({
        "number.base": "Device type must be number",
      }),
      device_token: Joi.string().allow(null, "").messages({
        "string.base": "Device token must be string",
      }),
    });
    return schema.validate(req.body);
  },
  async verifyMFAOtpValidation(req) {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        "any.required": "Email is required",
        "string.email": "Email is not valid",
        "string.empty": "Email is required",
      }),
      admin_id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.base": "id is required",
      }),
      code: Joi.number().required().messages({
        "any.required": "id is required",
        "number.base": "id is required",
      }),
    });
    return schema.validate(req.body);
  },
  async setupMFA(req) {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        "any.required": "Email is required",
        "string.email": "Email is not valid",
        "string.empty": "Email is required",
      }),
      admin_id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.base": "id is required",
      }),
    });
    return schema.validate(req.body);
  },

  async forgotPasswordValidation(req) {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        "any.required": "Email is required",
        "string.email": "Email is not valid",
        "string.empty": "Email is required",
      }),
    });
    return schema.validate(req.body);
  },
  async verifyTokensValidation(req) {
    const schema = Joi.object({
      token: Joi.string().required().messages({
        "any.required": "Token is required",
        "string.empty": "Token is required",
      }),
    });
    return schema.validate(req.body);
  },
  async verifyOtpValidation(req) {
    const schema = Joi.object({
      email: Joi.string().required().messages({
        "any.required": "Email is required",
        "string.empty": "Email is required",
      }),
      otp_token: Joi.string().required().messages({
        "any.required": "otp token is required",
        "string.empty": "otp token is required",
      }),
      otp: Joi.number().required().messages({
        "any.required": "otp is required",
        "number.base": "otp is required",
      }),
    });
    return schema.validate(req.body);
  },
  async resetPasswordValidation(req) {
    const schema = Joi.object({
      password: Joi.string().required().messages({
        "any.required": "Password is required",
        "string.empty": "Password is required",
      }),
      otp_token: Joi.string().required().messages({
        "any.required": "otp token is required",
        "string.empty": "otp token is required",
      }),
    });
    return schema.validate(req.body);
  },
  async editProfileValidation(req) {
    const schema = Joi.object({
      first_name: Joi.string().optional().allow(""),
      last_name: Joi.string().optional().allow(""),
      image: Joi.string().optional().allow(""),
    });
    return schema.validate(req.body);
  },
};
