/*Messages,status code and services require*/
require('dotenv').config();
const Sequelize = require('sequelize');
const OrderModel = require('../../database/models').orders;
const BarModel = require('../../database/models').bar;
const UserModel = require('../../database/models').user;
const UserConsentVenueModel =
	require('../../database/models').user_consent_venue;
const SegmentUserVenueModel =
	require('../../database/models').segment_user_venue;
const SegmentModel = require('../../database/models').segment;
const { Parser } = require('json2csv');
const Op = Sequelize.Op;
const moment = require('moment');

module.exports = {
	async getCustomerList(req) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			if (!barDetails) {
				return 0;
			}
			const isDiscountPage = req.body.isDiscount || false;
			const page = req.body.page;
			const perPage = 50;
			const sortBy = req.body?.sortBy;
			let whereClause = [{ barID: barId }];
			let orderByClause = [];

			if (isDiscountPage) {
				orderByClause = [['user', 'fullname', 'asc']];
			} else {
				switch (sortBy) {
					case 'newToOld':
						orderByClause = [[Sequelize.literal('convertedOrderDate DESC')]];
						break;
					case 'oldToNew':
						orderByClause = [[Sequelize.literal('convertedOrderDate ASC')]];
						break;
					case 'highestAvgOrderTotal':
						orderByClause = [[Sequelize.literal('avgOrderTotal DESC')]];
						break;
					case 'lowestAvgOrderTotal':
						orderByClause = [[Sequelize.literal('avgOrderTotal ASC')]];
						break;
					case 'highestOrderTotalCount':
						orderByClause = [[Sequelize.literal('orderCount DESC')]];
						break;
					case 'lowestOrderTotalCount':
						orderByClause = [[Sequelize.literal('orderCount ASC')]];
						break;
					case 'highestTotalOrderSpent':
						orderByClause = [[Sequelize.literal('totalOrderSpent DESC')]];
						break;
					case 'lowestTotalOrderSpent':
						orderByClause = [[Sequelize.literal('totalOrderSpent ASC')]];
						break;
					case 'highestSpent':
						orderByClause = [[Sequelize.literal('hightestSpent DESC')]];
						break;
					case 'lowestSpent':
						orderByClause = [[Sequelize.literal('hightestSpent ASC')]];
						break;
					default:
						orderByClause = [];
						break;
				}
			}

			if (req.body?.search && req.body?.search.trim() !== '') {
				const searchKeyword = req.body.search.trim();

				whereClause.push({
					[Sequelize.Op.or]: [
						{
							'$user.fullName$': {
								[Sequelize.Op.like]: `%${searchKeyword}%` // Match full names partially
							}
						},
						{
							[Sequelize.Op.and]: [
								{
									'$user.fullName$': {
										[Sequelize.Op.or]: [null, ''] // Match users with no names
									}
								},
								Sequelize.where(Sequelize.literal(`'Anonymous User'`), {
									[Sequelize.Op.like]: `%${searchKeyword}%` // Match "Anonymous User" partially
								})
							]
						}
					]
				});
			}

			const queryOptions = {
				attributes: [
					'userID',
					[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
					[Sequelize.fn('AVG', Sequelize.col('total')), 'avgOrderTotal'],
					[Sequelize.fn('SUM', Sequelize.col('total')), 'totalOrderSpent'],
					[Sequelize.fn('MAX', Sequelize.col('total')), 'hightestSpent']
				],
				group: ['userID'],
				where: whereClause,
				include: [
					{
						model: UserModel,
						attributes: [
							'fullName',
							'email',
							'countryCode',
							'mobile',
							'birthday',
							[
								Sequelize.literal(
									`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = ${barId} and user_consent_venue.userID = user.id limit 1) >= 1 THEN 1 ELSE 0 END`
								),
								'userSubscription'
							],
							[
								Sequelize.literal(
									`(select createdAt from user_consent_venue where user_consent_venue.barID = ${barId} and user_consent_venue.userID = user.id limit 1)`
								),
								'userSubscriptionDate'
							]
						],
						required: isDiscountPage
					}
				],
				order: orderByClause
			};

			if (!isDiscountPage) {
				queryOptions.offset = (page - 1) * perPage;
				queryOptions.limit = perPage;
			}

			const aggregatedCounts = await OrderModel.findAll(queryOptions);

			const users = aggregatedCounts.map(async (count) => {
				const userName = count?.user?.dataValues?.fullName || 'Anonymous User';
				const email = count?.user?.dataValues?.email || ' - ';
				const mobileNumber = count?.user?.dataValues?.mobile || ' - ';
				const birthday = count?.user?.dataValues?.birthday || ' - ';
				return {
					userID: count.userID,
					fullName: userName,
					email: email,
					countryCode: count?.user?.dataValues?.countryCode,
					mobile: mobileNumber,
					birthday: birthday,
					userSubscription: count?.user?.dataValues?.userSubscription,
					userSubscriptionDate: count?.user?.dataValues?.userSubscriptionDate,
					orderCount: Number(count?.dataValues?.orderCount.toFixed(2)),
					avgOrderTotal: Number(count?.dataValues?.avgOrderTotal.toFixed(2)),
					totalOrderSpent: Number(
						count?.dataValues?.totalOrderSpent.toFixed(2)
					),
					hightestSpent: Number(count?.dataValues?.hightestSpent.toFixed(2))
				};
			});

			const orderDetails = await Promise.all(users);

			const totalCount = await OrderModel.count({
				distinct: true,
				col: 'userID',
				where: whereClause,
				include: [
					{
						model: UserModel,
						required: isDiscountPage
					}
				]
			});

			return { list: orderDetails, total: totalCount };
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getCustomerDetails(req) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			//Invalid venue id
			if (!barDetails) {
				return 0;
			}
			const userId = req.body?.user_id;

			let userOrderDetails = await OrderModel.findOne({
				attributes: [
					'userID',
					[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
					[Sequelize.fn('SUM', Sequelize.col('total')), 'totalOrderSpent'],
					[Sequelize.fn('AVG', Sequelize.col('total')), 'avgOrderTotal'],
					[Sequelize.fn('MAX', Sequelize.col('total')), 'hightestSpent'],
					[
						Sequelize.literal(
							'(select DATE_FORMAT(convertedOrderDate, "%M %Y") AS firstOrderDate from orders WHERE barID = ' +
								barId +
								' AND userID = ' +
								userId +
								' order by convertedOrderDate asc limit 1)'
						),
						'customerSince'
					]
				],
				include: [
					{
						model: UserModel,
						required: false,
						attributes: [
							'fullName',
							'email',
							'countryCode',
							'mobile',
							[
								Sequelize.literal(`CASE
										WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 18 AND 27 THEN "18 - 27 (Gen Z)"
										WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 28 AND 43 THEN "28 - 43 (Millennial)"
										WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 44 AND 59 THEN "44 - 59 (Gen X)"
										WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 60 AND 78 THEN "60 - 78 (Baby Boomer)"
										WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 79 AND 150 THEN "79 - 99+ (Silent Generation+)"
										ELSE " - "
									END`),
								'age_range'
							],
							'birthday'
						]
					}
				],
				where: {
					userID: userId,
					barID: barId
				}
			});

			return {
				...userOrderDetails.dataValues,
				totalOrderSpent: Number(
					userOrderDetails.dataValues.totalOrderSpent.toFixed(2)
				),
				avgOrderTotal: Number(
					userOrderDetails.dataValues.avgOrderTotal.toFixed(2)
				),
				hightestSpent: Number(
					userOrderDetails.dataValues.hightestSpent.toFixed(2)
				)
			};
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async getOrderList(req) {
		try {
			let barId = req.body.bar_id;
			let userId = req.body.user_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			if (!barDetails) {
				return 0;
			}
			const page = req.body.page;
			const perPage = 20;
			const sortBy = req.body?.sortBy;
			let whereClause = [{ barID: barId, userID: userId }];
			let orderByClause = [];
			switch (sortBy) {
				case 'newToOld':
					orderByClause = [[Sequelize.literal('createdAt DESC')]];
					break;
				case 'oldToNew':
					orderByClause = [[Sequelize.literal('createdAt ASC')]];
					break;
				case 'highestSpent':
					orderByClause = [[Sequelize.literal('total DESC')]];
					break;
				case 'lowestSpent':
					orderByClause = [[Sequelize.literal('total ASC')]];
					break;
				default:
					orderByClause = [];
					break;
			}
			if (req.body?.search && req.body?.search != '') {
				whereClause.push({
					orderNo: {
						[Sequelize.Op.like]: `%${req.body?.search}%`
					}
				});
			}

			const orderAttributes = [
				'id',
				'orderNo',
				'total',
				'orderDate',
				'orderStatus',
				'refundStatus',
				'paymentStatus',
				'userID',
				'barID',
				'createdAt'
			];

			const orderListCount = await OrderModel.count({
				where: whereClause,
				order: orderByClause
			});

			const orderListRows = await OrderModel.findAll({
				attributes: orderAttributes,
				where: whereClause,
				offset: (page - 1) * perPage,
				limit: perPage,
				order: orderByClause
			});

			let orderList = { count: orderListCount, rows: orderListRows };

			if (orderList.count > 0) {
				return orderList;
			} else {
				return { count: 0, rows: [] };
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async getSegmentList(req) {
		try {
			let barId = req.body.bar_id;
			let userId = req.body.user_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			if (!barDetails) {
				return 0;
			}
			const page = req.body.page;
			const perPage = 20;
			let whereClause = [{ barID: barId, userID: userId }];
			if (req.body?.search && req.body?.search != '') {
				whereClause.push({
					'$segment.name$': {
						[Sequelize.Op.like]: `%${req.body?.search}%`
					}
				});
			}

			const segmentList = await SegmentUserVenueModel.findAndCountAll({
				attributes: [
					'id',
					[Sequelize.col('segment.name'), 'name'],
					'convertedDateTime',
					'createdAt'
				],
				include: [
					{
						model: SegmentModel,
						required: true,
						attributes: []
					}
				],
				order: [['createdAt', 'desc']],
				where: whereClause,
				offset: (page - 1) * perPage,
				limit: perPage
			});

			return segmentList;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async downloadCustomerList(req) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			if (!barDetails) {
				return 0;
			}
			const page = req.body.page;
			const perPage = 50;
			const sortBy = req.body?.sortBy;
			let whereClause = [{ barID: barId }];
			let orderByClause = [];
			switch (sortBy) {
				case 'newToOld':
					orderByClause = [
						[Sequelize.literal('"User->Orders.createdAt" DESC')]
					];
					break;
				case 'oldToNew':
					orderByClause = [[Sequelize.literal('"User->Orders.createdAt" ASC')]];
					break;
				case 'highestAvgOrderTotal':
					orderByClause = [[Sequelize.literal('avgOrderTotal DESC')]];
					break;
				case 'lowestAvgOrderTotal':
					orderByClause = [[Sequelize.literal('avgOrderTotal ASC')]];
					break;
				case 'highestTotalOrderSpent':
					orderByClause = [[Sequelize.literal('totalOrderSpent DESC')]];
					break;
				case 'lowestTotalOrderSpent':
					orderByClause = [[Sequelize.literal('totalOrderSpent ASC')]];
					break;
				case 'highestSpent':
					orderByClause = [[Sequelize.literal('hightestSpent DESC')]];
					break;
				case 'lowestSpent':
					orderByClause = [[Sequelize.literal('hightestSpent ASC')]];
					break;
				default:
					orderByClause = [];
					break;
			}
			let orderAttribute = [
				'userID',
				[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
				[Sequelize.fn('AVG', Sequelize.col('total')), 'avgOrderTotal'],
				[Sequelize.fn('SUM', Sequelize.col('total')), 'totalOrderSpent'],
				[Sequelize.fn('MAX', Sequelize.col('total')), 'hightestSpent'],
				[
					Sequelize.literal(
						`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = ${barId} and user_consent_venue.userID = orders.userID limit 1) >= 1 THEN 'Subscribed' ELSE 'Unsubscribed' END`
					),
					'userSubscription'
				],
				[
					Sequelize.literal(
						`(select createdAt from user_consent_venue where user_consent_venue.barID = ${barId} and user_consent_venue.userID = orders.userID limit 1)`
					),
					'userSubscriptionDate'
				],
				[
					Sequelize.fn(
						'DATE_FORMAT',
						Sequelize.fn('MIN', Sequelize.col('convertedOrderDate')),
						'%Y-%m-%d'
					),
					'firstOrderDate'
				]
			];
			let userInclude = [
				{
					model: UserModel,
					attributes: [
						'fullName',
						'email',
						'countryCode',
						'birthday',
						'mobile'
						// [
						// 	Sequelize.literal(`CASE
						// 		WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 18 AND 27 THEN "18 - 27 (Gen Z)"
						// 		WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 28 AND 43 THEN "28 - 43 (Millennial)"
						// 		WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 44 AND 59 THEN "44 - 59 (Gen X)"
						// 		WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 60 AND 78 THEN "60 - 78 (Baby Boomer)"
						// 		ELSE "79 - 99+ (Silent Generation+)"
						// 	END`),
						// 	'age_range'
						// ]
					],
					required: false
				}
			];
			let aggregatedCounts = [];

			if (req.body.type == 2) {
				if (req.body?.search && req.body?.search != '') {
					whereClause.push({
						'$user.fullName$': {
							[Sequelize.Op.like]: `%${req.body?.search}%`
						}
					});
				}
				aggregatedCounts = await OrderModel.findAll({
					attributes: [...orderAttribute],
					group: ['userID'],
					where: whereClause,
					include: userInclude,
					offset: (page - 1) * perPage,
					limit: perPage,
					order: orderByClause
				});
			} else if (req.body.type == 3) {
				const customerIds = req.body?.customerIds;
				whereClause.push({
					userID: { [Op.in]: customerIds }
				});
				aggregatedCounts = await OrderModel.findAll({
					attributes: orderAttribute,
					group: ['userID'],
					where: whereClause,
					include: userInclude,
					order: orderByClause
				});
			} else {
				let havingClause = {};
				if (req.body?.userTypeFilter == '1') {
					havingClause = Sequelize.literal(
						`(SELECT COUNT(*) FROM user_consent_venue WHERE user_consent_venue.barID = ${barId} AND user_consent_venue.userID = orders.userID limit 1) >= 1`
					);
				}

				const subQuery = `(SELECT DATE_FORMAT(convertedOrderDate, '%Y-%m-%d') AS firstOrderDate FROM orders WHERE barID = ${barId} AND userID = orders.userID ORDER BY convertedOrderDate ASC LIMIT 1)`;

				whereClause.push({
					[Op.and]: Sequelize.literal(
						`${subQuery} BETWEEN '${req.body?.startDate}' AND '${req.body?.endDate}'`
					)
				});

				aggregatedCounts = await OrderModel.findAll({
					attributes: orderAttribute,
					group: ['userID'],
					where: whereClause,
					having: havingClause,
					include: userInclude,
					order: orderByClause
				});
			}
			const users = aggregatedCounts.map(async (count) => {
				const userName = count?.user?.dataValues?.fullName || 'Anonymous User';
				const email = count?.user?.dataValues?.email || ' - ';
				const mobileNumber = count?.user?.dataValues?.mobile || ' - ';
				const birthday = count?.user?.dataValues?.birthday
					? moment(count?.user?.dataValues?.birthday).format('DD/MM/YYYY')
					: ' - ';
				const age_range =
					count?.user?.dataValues?.age_range || '79 - 99+ (Silent Generation+)';
				return {
					userID: count.userID,
					fullName: userName,
					email: email,
					countryCode: count?.user?.dataValues?.countryCode || '',
					mobile: mobileNumber,
					birthday: birthday,
					age_range: age_range,
					firstOrderDate: moment(count?.dataValues?.firstOrderDate).format(
						'DD/MM/YYYY'
					),
					orderCount: Number(count?.dataValues?.orderCount.toFixed(2)),
					avgOrderTotal: Number(count?.dataValues?.avgOrderTotal.toFixed(2)),
					totalOrderSpent: Number(
						count?.dataValues?.totalOrderSpent.toFixed(2)
					),
					hightestSpent: Number(count?.dataValues?.hightestSpent.toFixed(2)),
					userSubscription: count?.dataValues?.userSubscription,
					userSubscriptionDate: count?.dataValues?.userSubscriptionDate
						? moment(count?.dataValues?.userSubscriptionDate).format(
								'DD/MM/YYYY'
						  )
						: ' - '
				};
			});

			const userDetails = await Promise.all(users);

			const csvFormattedData = userDetails.map((user) => {
				return {
					'First Order Date': user.firstOrderDate,
					'Full Name': user.fullName,
					Email: user.email,
					Mobile: user.countryCode + ' ' + user.mobile,
					Birthday: user.birthday,
					// 'Age Range': user.age_range,
					'Highest Spend': '$' + user.hightestSpent,
					'Average Order Spend': '$' + user.avgOrderTotal,
					'Total Order spent': '$' + user.totalOrderSpent,
					'Order Total': user.orderCount,
					'Subscription Statue': user.userSubscription,
					'Subscription Date': user.userSubscriptionDate
				};
			});
			const fields = [
				'First Order Date',
				'Full Name',
				'Email',
				'Mobile',
				'Birthday',
				// 'Age Range',
				'Highest Spend',
				'Average Order Spend',
				'Total Order spent',
				'Order Total',
				'Subscription Statue',
				'Subscription Date'
			];
			const json2csvParser = new Parser({
				fields,
				defaultValue: 'NA',
				includeEmptyRows: true
			});

			return json2csvParser.parse(csvFormattedData);
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async cron(req) {
		try {
			const allUserDataVenueWise = await OrderModel.findAll({
				attributes: [
					'userID',
					'barID',
					[Sequelize.fn('SUM', Sequelize.col('total')), 'totalSpent']
				],
				include: [
					{
						model: UserModel,
						attributes: [],
						required: true
					}
				],
				group: ['userID', 'barID'],
				order: [[Sequelize.fn('SUM', Sequelize.col('total')), 'DESC']]
			});

			const venueIds = Array.from(
				new Set(allUserDataVenueWise.map((record) => record.barID))
			);

			const userSpendsByVenue = allUserDataVenueWise.reduce((acc, record) => {
				if (!acc[record.barID]) {
					acc[record.barID] = [];
				}
				acc[record.barID].push({
					userID: record.userID,
					totalSpent: record.dataValues.totalSpent
				});
				return acc;
			}, {});

			const topSpendersByVenue = {};

			venueIds.forEach((venueId) => {
				const topSpenders = userSpendsByVenue[venueId].map(
					(user) => user.userID
				);

				topSpendersByVenue[venueId] = {
					top5: topSpenders.slice(0, 5),
					top10: topSpenders.slice(0, 10),
					top20: topSpenders.slice(0, 20),
					top50: topSpenders.slice(0, 50),
					top100: topSpenders.slice(0, 100)
				};
			});

			const allUserData = await OrderModel.findAll({
				attributes: [
					'userID',
					'barID',
					[Sequelize.fn('SUM', Sequelize.col('total')), 'totalSpent'],
					[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '06:00:00' AND '08:59:59' THEN 1 ELSE 0 END`
							)
						),
						'earlyMorning'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '09:00:00' AND '10:59:59' THEN 1 ELSE 0 END`
							)
						),
						'midMorning'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '11:00:00' AND '13:59:59' THEN 1 ELSE 0 END`
							)
						),
						'lunch'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '14:00:00' AND '16:59:59' THEN 1 ELSE 0 END`
							)
						),
						'afternoon'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '17:00:00' AND '20:59:59' THEN 1 ELSE 0 END`
							)
						),
						'evening'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN TIME(convertedOrderDateTime) BETWEEN '21:00:00' AND '23:59:59' THEN 1 ELSE 0 END`
							)
						),
						'lateNight'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN orderServiceType = 'PICKUP' THEN 1 ELSE 0 END`
							)
						),
						'hasPickup'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN orderServiceType = 'TABLE' THEN 1 ELSE 0 END`
							)
						),
						'hasTableService'
					],
					[
						Sequelize.literal(
							`CASE WHEN MAX(CASE WHEN orderServiceType = 'PICKUP' THEN 1 ELSE 0 END) = 1 AND MAX(CASE WHEN orderServiceType = 'TABLE' THEN 1 ELSE 0 END) = 1 THEN 1 ELSE 0 END`
						),
						'hasBoth'
					],
					[
						Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top5.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
						'isTop5Customer'
					],
					[
						Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top10.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
						'isTop10Customer'
					],
					[
						Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top20.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
						'isTop20Customer'
					],
					[
						Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top50.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
						'isTop50Customer'
					],
					[
						Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top100.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
						'isTop100Customer'
					],
					[
						Sequelize.literal(`CASE WHEN SUM(total) < 50 THEN 1 ELSE 0 END`),
						'croissantClub'
					],
					[
						Sequelize.literal(
							`CASE WHEN SUM(total) >= 50 AND SUM(total) < 200 THEN 1 ELSE 0 END`
						),
						'caramelClub'
					],
					[
						Sequelize.literal(`CASE WHEN SUM(total) > 200 THEN 1 ELSE 0 END`),
						'caviarClub'
					],
					[
						Sequelize.literal(
							`CASE WHEN DATE_FORMAT(user.birthday, '%m-%d') = DATE_FORMAT(CURDATE(), '%m-%d') THEN 1 ELSE 0 END`
						),
						'birthdayToday'
					],
					[
						Sequelize.literal(
							`CASE WHEN DATE_FORMAT(user.birthday, '%m-%d') BETWEEN DATE_FORMAT(CURDATE() - INTERVAL (WEEKDAY(CURDATE())) DAY, '%m-%d') AND DATE_FORMAT(CURDATE() + INTERVAL (6 - WEEKDAY(CURDATE())) DAY, '%m-%d') THEN 1 ELSE 0 END`
						),
						'birthdayThisWeek'
					],
					[
						Sequelize.literal(
							`CASE WHEN DATE_FORMAT(user.birthday, '%m') = DATE_FORMAT(CURDATE(), '%m') THEN 1 ELSE 0 END`
						),
						'birthdayThisMonth'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN MONTH(convertedOrderDateTime) IN (12, 1, 2) THEN 1 ELSE 0 END`
							)
						),
						'orderedInSummer'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN MONTH(convertedOrderDateTime) IN (3, 4, 5) THEN 1 ELSE 0 END`
							)
						),
						'orderedInAutumn'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN MONTH(convertedOrderDateTime) IN (6, 7, 8) THEN 1 ELSE 0 END`
							)
						),
						'orderedInWinter'
					],
					[
						Sequelize.fn(
							'MAX',
							Sequelize.literal(
								`CASE WHEN MONTH(convertedOrderDateTime) IN (9, 10, 11) THEN 1 ELSE 0 END`
							)
						),
						'orderedInSpring'
					],
					[
						Sequelize.literal(
							`CASE WHEN MAX(convertedOrderDateTime) >= CURDATE() - INTERVAL 30 DAY THEN 1 ELSE 0 END`
						),
						'newCustomer'
					],
					[
						Sequelize.literal(
							`CASE WHEN MAX(convertedOrderDateTime) >= CURDATE() - INTERVAL 90 DAY AND MAX(convertedOrderDateTime) < CURDATE() - INTERVAL 31 DAY THEN 1 ELSE 0 END`
						),
						'frequentCustomer'
					],
					[
						Sequelize.literal(
							`CASE WHEN MAX(convertedOrderDateTime) < CURDATE() - INTERVAL 90 DAY THEN 1 ELSE 0 END`
						),
						'inactiveCustomer'
					],
					[
						Sequelize.literal(
							`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = orders.barID and user_consent_venue.userID = user.id limit 1) >= 1 THEN 1 ELSE 0 END`
						),
						'hasConsent'
					]
				],
				group: ['userID', 'barID'],
				include: [
					{
						model: UserModel,
						attributes: [
							'fullName',
							'email',
							'countryCode',
							'mobile',
							'birthday',
							[
								Sequelize.literal(
									`CASE WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 18 AND 27 THEN "18 - 27 (Gen Z)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 28 AND 43 THEN "28 - 43 (Millennial)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 44 AND 59 THEN "44 - 59 (Gen X)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 60 AND 78 THEN "60 - 78 (Baby Boomer)" ELSE "79 - 99+ (Silent Generation+)" END`
								),
								'age_range'
							]
						],
						required: true
					}
				],
				orderBy: ['userID']
			});

			let createSegment = [];
			let removeSegment = [];
			let removeUser = 0;
			let skipUser = 0;
			let count = 0;

			// Mapping of age ranges to segment IDs
			const segmentIdMap = {
				'18 - 27 (Gen Z)': '7',
				'28 - 43 (Millennial)': '8',
				'44 - 59 (Gen X)': '9',
				'60 - 78 (Baby Boomer)': '10',
				'79 - 99+ (Silent Generation+)': '11'
			};

			let todayDateTime = moment().tz('Australia/Perth');

			// Fetch all user segments in one go
			const userSegmentLists = await SegmentUserVenueModel.findAll({
				attributes: [
					'userID',
					'barID',
					[
						Sequelize.fn('GROUP_CONCAT', Sequelize.col('segmentID')),
						'segmentIDs'
					]
				],
				group: ['userID', 'barID'],
				raw: true
			});

			const userSegmentsMap = new Map(
				userSegmentLists.map((segment) => [
					`${segment.userID}-${segment.barID}`,
					new Set(segment.segmentIDs.split(','))
				])
			);

			const userSegmentPromises = allUserData.map(async (entity) => {
				const key = `${entity.userID}-${entity.barID}`;
				const userSegmentSet = userSegmentsMap.get(key) || new Set();

				if (!entity.user) {
					if (userSegmentSet.size) {
						removeUser++;
						await SegmentUserVenueModel.destroy({
							where: { userID: entity.userID, barID: entity.barID }
						});
					} else {
						skipUser++;
					}
					return; // Exit early for users without a valid entity
				}

				count++;

				const addSegment = (id) => {
					if (!userSegmentSet.has(id)) {
						createSegment.push({
							userID: entity.userID,
							barID: entity.barID,
							segmentID: id,
							convertedDateTime: todayDateTime
						});
					}
				};

				const conditions = [
					{ id: '1', condition: true }, // All customers
					{ id: '3', condition: entity.dataValues.orderCount == 1 }, // Customers who have purchased once
					{
						id: '4',
						condition:
							entity.dataValues.orderCount > 1 &&
							entity.dataValues.orderCount < 10
					}, // Customers who have purchased more than once
					{ id: '5', condition: entity.dataValues.orderCount >= 10 }, // Customers who have purchased more than 10 times
					{
						id: segmentIdMap[entity.user.dataValues.age_range],
						condition: true
					}, // Customer Age Range
					{ id: '13', condition: entity.dataValues.earlyMorning == '1' }, // Customers who purchase early morning (6am - 9am)
					{ id: '14', condition: entity.dataValues.midMorning == '1' }, // Customers who purchase mid morning (9am - 11am)
					{ id: '15', condition: entity.dataValues.lunch == '1' }, // Customers who purchase at lunch (11pm - 2pm)
					{ id: '16', condition: entity.dataValues.afternoon == '1' }, // Customers who purchase in the afternoon (2pm - 5pm)
					{ id: '17', condition: entity.dataValues.evening == '1' }, // Customers who purchase in the evening (5pm - 9pm)
					{ id: '18', condition: entity.dataValues.lateNight == '1' }, // Customers who purchase late night (9pm - 11:59pm)
					{ id: '24', condition: entity.dataValues.hasPickup == '1' }, // Customers who place pick up/collect orders
					{ id: '25', condition: entity.dataValues.hasTableService == '1' }, // Customers who place table service orders
					{ id: '26', condition: entity.dataValues.hasBoth == '1' }, // Customers who place both pickup/collect & table service orders
					{ id: '31', condition: entity.dataValues.isTop5Customer == '1' }, // Top 5 customers with the highest individual purchase
					{ id: '32', condition: entity.dataValues.isTop10Customer == '1' }, // Top 10 customers with the highest individual purchase
					{ id: '33', condition: entity.dataValues.isTop20Customer == '1' }, // Top 20 customers with the highest individual purchase
					{ id: '34', condition: entity.dataValues.isTop50Customer == '1' }, // Top 50 customers with the highest individual purchase
					{ id: '35', condition: entity.dataValues.isTop100Customer == '1' }, // Top 100 customers with the highest individual purchase
					{ id: '37', condition: entity.dataValues.croissantClub == '1' }, // Croissant Club customers (spent $0 - $49.99)
					{ id: '38', condition: entity.dataValues.caramelClub == '1' }, // Creme Caramel Club customers (spent $50- $199.99)
					{ id: '39', condition: entity.dataValues.caviarClub == '1' }, // Caviar Club customers (spent $200+)
					{ id: '41', condition: entity.dataValues.birthdayToday == '1' }, // Customers who have birthdays today
					{ id: '42', condition: entity.dataValues.birthdayThisWeek == '1' }, // Customers who have birthdays this week
					{ id: '43', condition: entity.dataValues.birthdayThisMonth == '1' }, // Customers who have birthdays this month
					{ id: '45', condition: entity.dataValues.orderedInSummer == '1' }, // Customers who order in Summer (Dec - Feb)
					{ id: '46', condition: entity.dataValues.orderedInAutumn == '1' }, // Customers who order in Autumn (March - May)
					{ id: '47', condition: entity.dataValues.orderedInWinter == '1' }, // Customers who order in Winter (June - Aug)
					{ id: '48', condition: entity.dataValues.orderedInSpring == '1' }, // Customers who order in Spring (Sept - Nov)
					{ id: '50', condition: entity.dataValues.newCustomer == '1' }, // New customers (ordered within the last 30 days)
					{ id: '51', condition: entity.dataValues.frequentCustomer == '1' }, // Frequent customers (ordered within the last 90 day...
					{ id: '52', condition: entity.dataValues.inactiveCustomer == '1' }, // Inactive customers (not ordered for over 90 days)
					{ id: '54', condition: entity.dataValues.hasConsent == '1' }, // Subscribed customers
					{ id: '55', condition: entity.dataValues.hasConsent == '0' } // Unsubscribed customers
				];

				conditions.forEach(({ id, condition }) => {
					if (id && condition) {
						addSegment(id);
					} else if (userSegmentSet.has(id)) {
						removeSegment.push({
							userID: entity.userID,
							barID: entity.barID,
							segmentID: id
						});
					}
				});

				// // Prepare for removal of age range segments
				Object.values(segmentIdMap).forEach((segmentId) => {
					if (
						userSegmentSet.has(segmentId) &&
						segmentIdMap[entity.user.dataValues.age_range] != segmentId
					) {
						removeSegment.push({
							userID: entity.userID,
							barID: entity.barID,
							segmentID: segmentId
						});
					}
				});
			});

			await Promise.all(userSegmentPromises);

			// Bulk insert and delete operations
			if (createSegment.length) {
				await SegmentUserVenueModel.bulkCreate(createSegment);
			}

			if (removeSegment.length) {
				await SegmentUserVenueModel.destroy({
					where: {
						[Sequelize.Op.or]: removeSegment.map((segment) => ({
							userID: segment.userID,
							barID: segment.barID,
							segmentID: segment.segmentID
						}))
					}
				});
			}

			return {
				createSegment: createSegment,
				removeSegment: removeSegment,
				count: allUserData.length
				// userData: allUserData,
			};
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
};
