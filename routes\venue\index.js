/*
 * Summary:     index.js file for handling all routes, request and response for admin panel - (CMS related actions).
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require('express');
var router = express.Router();

/* require for Authentication */
const AuthRouter = require('./auth');

/*require for security routes */
const SecurityRouter = require('./security');

/*require for product Router */
const ProductRouter = require('./product');

/*require for bar Router */
const BarRouter = require('./bar');

/*require for role permission */
const RolePermissionRouter = require('./rolespermission');

/*require for report */
const ReportRouter = require('./report');

/*require for dashboardRouter */
const DashboardRouter = require('./dashboard');

/*require for dashboardRouter */
const FeatureRouter = require('./feature');

/*require for picklocation Router */
const PickupLocationRouter = require('./pickuplocation');

/*require for category Router */
const CategoryRouter = require('./category');

/*require for promocode Router */
const PromoCodeRouter = require('./promocode');

/*require for Tax Router */
const TaxRouter = require('./tax');

/*require for order Router */
const OrderRouter = require('./order');

/*require for page Router */
const PageRouter = require('./page');

/*require for customer Router */
const CustomerRouter = require('./customer');

/*require for segment Router */
const SegmentRouter = require('./segment');

/*require for ads Router */
const AdsRouter = require('./venueads');

const CampaignRouter = require('./venuecampaign');
const AdsReportsRouter = require('./venueadsreports');

/* Routes of auth*/
router.use('/auth', AuthRouter);

/* Routes of security*/
router.use('/security', SecurityRouter);

/*Routes of product */
router.use('/product', ProductRouter);

/*Routes of  bar */
router.use('/bar', BarRouter);

/*routes of roles in permission */
router.use('/roles-permission', RolePermissionRouter);

/*routes of roles in report */
router.use('/report', ReportRouter);

/*routes of report */
router.use('/dashboard', DashboardRouter);

/*routes of feature */
router.use('/feature', FeatureRouter);

/*Routes of pickuplocation */
router.use('/pickup-location', PickupLocationRouter);

/*Routes of category */
router.use('/category', CategoryRouter);

/*Routes of Promocode */
router.use('/promocode', PromoCodeRouter);

/*Routes of tax */
router.use('/tax', TaxRouter);

/*Routes of order */
router.use('/order', OrderRouter);

/*Routes of page */
router.use('/page', PageRouter);

/*Routes of customer */
router.use('/customer', CustomerRouter);

/*Routes of segment */
router.use('/segment', SegmentRouter);

/*Routes of campaign */
router.use('/campaign', CampaignRouter);
router.use('/reports', AdsReportsRouter);

/*Routes of ads */
router.use('/ads/', AdsRouter);

module.exports = router;
