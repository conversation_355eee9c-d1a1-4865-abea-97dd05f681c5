var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/venueAuth").venueAuthorization;

/*require for security */
const SecurityController = require("../../controllers/venue/securityController");

/* Security API */
router.post(
  "/change-password",
  Authorization,
  SecurityController.changePassword
);

router.get(
  "/change-password-date",
  Authorization,
  SecurityController.changePasswordDate
);

router.get("/device-list", Authorization, SecurityController.getDevicelist);

router.post("/logout-device", Authorization, SecurityController.logoutDevice);

router.post("/verify-otp", Authorization, SecurityController.verifyOtp);

module.exports = router;
