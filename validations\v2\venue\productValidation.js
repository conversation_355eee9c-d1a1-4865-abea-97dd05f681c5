const Joi = require('joi');

module.exports = {
	//list product validation
	async getVenueCategoriesValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			})
		});
		return schema.validate(req.body);
	},

	async getSegmentTagsValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			})
		});
		return schema.validate(req.body);
	},

	async globalPriceUpdateValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			}),
			sign: Joi.string().valid('+', '-').required().messages({
				'any.required': 'Sign field is required.',
				'string.empty': 'Sign field cannot be empty.',
				'any.only': 'Sign field must be either "+" or "-".'
			}),
			unit: Joi.string().valid('$', '%').required().messages({
				'any.required': 'Unit field is required.',
				'string.empty': 'Unit field cannot be empty.',
				'any.only': 'Unit field must be either "$" or "%".'
			}),
			amount: Joi.number().positive().precision(2).required().messages({
				'any.required': 'Amount field is required.',
				'number.base': 'Amount field must be a number.',
				'number.positive': 'Amount field must be a positive number.',
				'number.precision': 'Amount field must have up to 2 decimal places.'
			})
		});
		return schema.validate(req.body);
	}
};
