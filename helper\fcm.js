var fcm = require("fcm-notification");
var key = require("./event.json");
var FCM = new fcm(key);
const Notification = require("../database/models").notification;

class FCMService {
  async sendNotifications(
    body,
    tokens,
    title = null,
    type = 0
  ) {
    // let badges = await Notification.count({
    //   where: {
    //     receiver_id: receiver_id,
    //     is_read: false,
    //   },
    // });

    // let click_action = "FLUTTER_NOTIFICATION_CLICK";
    const apns = {
      payload: {
        aps: {
          sound: "default",
          type: type.toString()
        },
      },
    };

    var message = {
      notification: { body: body, title: title ? title : "Charge Nutrition" },
      data: {
        // click_action: click_action,
        data: JSON.stringify(body),
        type: type.toString()
      },
      apns,
    };
    console.log("FCMService -> message", message, tokens);

    return FCM.sendToMultipleToken(message, tokens, function (err, response) {
      if (err) {
        console.log("1", err);
        return err;
      } else {
        console.log("2", response);
        return response;
      }
    });
  }

  async createNotification(
    body,
    sender_id,
    user_type,
    //receiver_id,
    data
  ) {
    var message = {
      notification: { body: body },
      data: {
        notification_type: notification_type,
        data: JSON.stringify(data),
      },
    };

    let notificationData = {
      notification_text: JSON.stringify(message.notification.body),
      admin_id: sender_id,
      user_type: user_type,
    }

    await Notification.create(notificationData);
  }
}

module.exports = FCMService;