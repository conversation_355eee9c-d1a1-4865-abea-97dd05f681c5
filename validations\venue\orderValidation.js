const Joi = require('joi').extend(require('@joi/date'));
module.exports = {
	async barOrderHistoryValidation(req) {
		const schema = Joi.object({
			page: Joi.number().messages({
				'number.base': 'page must be number'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id is required'
			}),
			// categoryID: Joi.string()
			//     .regex(/^[0-9]+(,[0-9]+)*$/)
			//     .messages({
			//         'any.required': 'Category id is required',
			//         'string.pattern.base':
			//             'Category id must be comma seperated numbers'
			//     })
			//     .allow(''),
			orderType: Joi.string()
				.messages({
					'any.required': 'Order type is required',
					'string.base': 'Order type must be a string'
				})
				.allow(''),
			// filter_type: Joi.string()
			//     .regex(/^[0-9]+(,[0-9]+)*$/)
			//     .messages({
			//         'any.required': 'filter type is required',
			//         'string.pattern.base':
			//             'filter type must be comma seperated numbers'
			//     })
			//     .allow(''),
			startDate: Joi.date().optional({}).allow(''),
			endDate: Joi.date().optional({}).allow(''),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow('')
		});
		return schema.validate(req.body);
	},
	async barOrderListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id is required'
			})
		});
		return schema.validate(req.body);
	},
	async orderViewValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id is required'
			}),
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'number.empty': 'id is required'
			})
		});
		return schema.validate(req.body);
	},
	async orderCancelValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id should be number'
			}),
			id: Joi.number().required().messages({
				'any.required': 'order id is required',
				'number.empty': 'order id should be number'
			}),
			// refundTransactionFee: Joi.string().valid('Yes').allow(''),
			order_items: Joi.array()
				.allow('')
				.items(
					Joi.object({
						id: Joi.number().required().messages({
							'any.required': 'order id is required',
							'number.empty': 'order should be number'
						}),
						quantity: Joi.number().required().messages({
							'any.required': 'quantity is required',
							'number.empty': 'quantity should be number'
						})
					})
				),
			tax_data: Joi.array()
				.allow('')
				.items(
					Joi.object({
						tax: Joi.number().required().messages({
							'any.required': 'tax id is required',
							'number.empty': 'tax id should be number'
						}),
						amount: Joi.number().required().messages({
							'any.required': 'amount is required',
							'number.empty': 'amount is required'
						})
					})
				)
		});
		return schema.validate(req.body);
	},
	async updateOrderItemStatusValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id should be number'
			}),
			id: Joi.string().required().messages({
				'any.required': 'order items id is required'
			}),
			orderStatus: Joi.string()
				.valid('Preparing', 'Pickup', 'Pickedup', 'NotPickedup')
				.required()
				.messages({
					'any.required': 'order items status is required'
				})
		});
		return schema.validate(req.body);
	},
	async orderIntoxicatedValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id should be number'
			}),
			id: Joi.string().required().messages({
				'any.required': 'order items id is required'
			})
		});
		return schema.validate(req.body);
	},
	async updateOrderItemWaitTimeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id must be number'
			}),
			id: Joi.required().messages({
				'any.required': 'id is required'
			}),
			waitTime: Joi.required().messages({
				'any.required': 'waitTime is required'
			})
		});
		return schema.validate(req.body);
	},
	async downloadOrderHistoryValidation(req) {
		const schema = Joi.object({
			page: Joi.number().messages({
				'number.base': 'page must be number'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id is required'
			}),
			categoryID: Joi.string()
				.regex(/^[0-9]+(,[0-9]+)*$/)
				.messages({
					'any.required': 'Category id is required',
					'string.pattern.base': 'Category id must be comma seperated numbers'
				})
				.allow(''),
			orderType: Joi.string()
				.messages({
					'any.required': 'Order type is required',
					'string.base': 'Order type must be a string'
				})
				.allow(''),
			filter_type: Joi.string()
				.regex(/^[0-9]+(,[0-9]+)*$/)
				.messages({
					'any.required': 'filter type is required',
					'string.pattern.base': 'filter type must be comma seperated numbers'
				})
				.allow(''),
			startDate: Joi.date().optional({}).allow(''),
			endDate: Joi.date().optional({}).allow(''),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow('')
		});
		return schema.validate(req.body);
	},
	async readyForPickupAlertValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required',
				'number.empty': 'bar id is required'
			}),
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'number.empty': 'id is required'
			})
		});
		return schema.validate(req.body);
	}
};
