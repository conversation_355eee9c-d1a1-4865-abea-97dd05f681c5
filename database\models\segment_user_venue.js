'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class segment_user_venue extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			segment_user_venue.belongsTo(models.bar, { foreignKey: 'barID' });
			segment_user_venue.belongsTo(models.user, { foreignKey: 'userID' });
			segment_user_venue.belongsTo(models.segment, { foreignKey: 'segmentID' });
		}
	}
	segment_user_venue.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			segmentID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'segment',
					key: 'id'
				}
			},
			convertedDateTime: DataTypes.DATE,
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: true }
		},
		{
			sequelize,
			modelName: 'segment_user_venue',
			timestamps: true,
			freezeTableName: true
		}
	);
	return segment_user_venue;
};
