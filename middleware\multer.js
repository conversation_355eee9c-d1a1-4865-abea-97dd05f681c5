/*
 * Summary:     Multer is middleware for upload image
 * Author:      Openxcell
 */
const multer = require("multer");
const path = require("path");
const os = require("os");
const fs = require("fs");
const tmpdir = os.tmpdir();
const randomStringHelper = require("../helper/generalHelper");

var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tmpdir);
  },
  filename: function (req, file, cb) {
    const imageName =
      randomStringHelper.generateRandomString(5) +
      "_" +
      Date.now() +
      path.extname(file.originalname);

    const filepath = path.join(tmpdir, imageName);
    file.originalname = imageName;
    fs.mkdtemp(filepath, (err, folder) => {
      if (err) throw err;
      cb(null, imageName);
    });
  },
});

exports.singleProfilePic = multer({
  storage: storage,
}).single("image");

exports.singleBarPic = multer({
  storage: storage,
}).single("bar_image");

exports.singleProductPic = multer({
  storage: storage,
}).single("product_image");

exports.singleAdPic = multer({
    storage: storage,
  }).single("media_url");
  
exports.multiProfilePic = multer({
  storage: storage,
}).any();
