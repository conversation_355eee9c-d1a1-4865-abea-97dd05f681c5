/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const moment = require('moment');
const speakeasy = require('speakeasy');
const CouponModel = require('../../database/models').coupon;
const TaxModel = require('../../database/models').tax;
const OperatingBarTaxModel = require('../../database/models').operating_bar_tax;
const couponsSubCatIdsModel = require('../../database/models').couponssubcatids;
const SubCategoryModel = require('../../database/models').sub_category;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = {
	/* add */
	async add(req, res) {
		try {
			const barID = req.body.bar_id;
			const requestOperatingTax = req.body.operating_bar_tax;
			// if (requestOperatingTax.length !== 7) {
			//   return res.status(200)
			//     .send({
			//       success: 0,
			//       message: "You'll need to fill all the week day's data to proceed.",
			//       data: {}
			//     })
			// }
			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
			// var productIds = JSON.parse(req.body.productID)
			// var arrTaxProductIds = []
			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
			let result = await TaxModel.findOne({
				where: { barID: barID, name: req.body.name },
				attributes: ['id', 'name', 'percentage', 'createdAt', 'updatedAt']
				// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
				// include: [{
				//   model: taxProductIds,
				//   where: { productID: { [Op.in]: productIds } },
				// }],
				// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
			});

			if (result) {
				return 0;
			}

			let response = await TaxModel.create({
				barID: barID,
				name: req.body.name,
				percentage: req.body.percentage,
				status: req.body.status
			});

			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
			// var ids = []
			// for (const ele of productIds) {
			//   ids = {
			//     taxID: response.dataValues.id,
			//     productID: ele
			//   }
			//   arrTaxProductIds.push(ids)
			// }
			// taxProductIds.bulkCreate(arrTaxProductIds)
			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
			if (requestOperatingTax.length == 7) {
				requestOperatingTax.map(async (opHours) => {
					await OperatingBarTaxModel.create({
						weekDay: opHours.week_day,
						isClosed: opHours.is_closed,
						taxID: response.dataValues.id,
						percentage: req.body.percentage
					});
				});
			}

			return 1;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},
	/* list */
	async list(req, res) {
		try {
			const barID = req.body.bar_id;

			let result = await TaxModel.findAll({
				where: { barID: barID },
				include: [
					{
						model: OperatingBarTaxModel,
						required: false
					}
				],
				// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
				// include: [{
				//   model: taxProductIds,
				//   include: [{
				//     model: productModel,
				//     attributes: ['id', 'name'],
				//   }]
				// }],
				// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
				order: ['name']
			});

			if (result) {
				return result;
			} else {
				return 0;
			}
			//   .then(async (result) => {
			//       if (result != 0) {
			//         res.status(200).send({
			//           success: 1,
			//           message: "All taxes retrieved successfully.",
			//           data: result,
			//         });
			//       } else {
			//         res.status(200).send({
			//           success: 0,
			//           message: "No taxes found for the Venue.",
			//           data: {},
			//         });
			//       }
			//     })
			//     .catch(function (err) {
			//       console.log(err);
			//     });
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},
	/* edit */
	async edit(req, res) {
		try {
			const barID = req.body.bar_id;
			let taxId = req.body.id;
			const requestOperatingTax = req.body.operating_bar_tax;

			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
			// var productIds = JSON.parse(req.body.productID)
			// var arrTaxProductIds = []
			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.

			let result = await TaxModel.findOne({
				where: { barID: barID, id: taxId },
				attributes: ['id', 'name', 'percentage']
			});

			if (!result) {
				return 0;
			}
			const taxRecords = await TaxModel.findAll({
				where: {
					barID: barID,
					id: { [Op.not]: taxId },
					name: req.body.name
				}
			});

			if (taxRecords.length) {
				return 1;
			}

			await TaxModel.update(
				{
					barID: barID,
					name: req.body.name,
					percentage: req.body.percentage,
					status: req.body.status
				},
				{
					where: { id: taxId }
				}
			);

			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
			// taxProductIds.destroy({ where: { taxID: req.body.id } })
			// var ids = []
			// for (const ele of productIds) {
			//   ids = {
			//     taxID: req.body.id,
			//     productID: ele
			//   }
			//   arrTaxProductIds.push(ids)
			// }
			// taxProductIds.bulkCreate(arrTaxProductIds)
			// -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts ends.
			if (requestOperatingTax.length == 7) {
				await OperatingBarTaxModel.destroy({
					where: {
						taxID: req.body.id
					}
				});
				requestOperatingTax.map(async (opHours) => {
					await OperatingBarTaxModel.create({
						isClosed: opHours.is_closed,
						weekDay: opHours.week_day,
						taxID: req.body.id,
						percentage: req.body.percentage
					});
				});
			}

			return 2;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},
	/* delete */
	async delete(req, res) {
		let taxId = req.body.id;
		let barID = req.body.bar_id;
		try {
			let data = await TaxModel.destroy({ where: { id: taxId, barID: barID } });

			return data;
			// taxProductIds.destroy({ where: { taxID: taxId } }) -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly.
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	}
};
