/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const moment = require('moment');
const speakeasy = require('speakeasy');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const constant = require('../../config/constant');
const VenueUserModel = require('../../database/models').venue_user;
const PickupLocationModel = require('../../database/models').pickup_location;
const ProductModel = require('../../database/models').product;
// const PickupLocationSubCategoryModel =
// 	require('../../database/models').pickup_location_sub_category;

module.exports = {
	/* add */
	async add(req, res) {
		try {
			let barId = req.body.bar_id;
			// if (req.body?.sub_category_ids.length >= 1) {
			// 	for (const subCatId of req.body.sub_category_ids) {
			// 		let checkPickupLocationSubCategory =
			// 			await PickupLocationSubCategoryModel.findOne({
			// 				where: {
			// 					barID: barId,
			// 					subCategoryID: subCatId
			// 				}
			// 			});
			// 		if (checkPickupLocationSubCategory) {
			// 			return 2;
			// 		}
			// 	}
			// }

			let data = await PickupLocationModel.create({
				barID: barId,
				address: req.body.address
			});

			// for (const subCatId of req.body?.sub_category_ids) {
			// 	await PickupLocationSubCategoryModel.create({
			// 		barID: barId,
			// 		subCategoryID: subCatId,
			// 		pickupLocationID: data.id
			// 	});
			// }
			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* list */
	async list(req, res) {
		try {
			let barId = req.body.bar_id;
			let data = await PickupLocationModel.findAll({
				where: {
					barID: barId,
					isDeleted: 'No'
				},
				attributes: { exclude: ['isDeleted', 'createdAt', 'updatedAt'] }
				// include: [
				// 	{
				// 		model: PickupLocationSubCategoryModel
				// 	}
				// ]
			});

			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* edit */
	async edit(req, res) {
		try {
			let barId = req.body.bar_id;
			// if (req.body?.sub_category_ids.length >= 1) {
			// 	for (const subCatId of req.body.sub_category_ids) {
			// 		let checkPickupLocationSubCategory =
			// 			await PickupLocationSubCategoryModel.findOne({
			// 				where: {
			// 					barID: barId,
			// 					subCategoryID: subCatId,
			// 					pickupLocationID: {
			// 						[Op.ne]: req.body.id
			// 					}
			// 				}
			// 			});
			// 		if (checkPickupLocationSubCategory) {
			// 			return 2;
			// 		}
			// 	}
			// }

			// await PickupLocationSubCategoryModel.destroy({
			// 	where: {
			// 		pickupLocationID: req.body.id
			// 	}
			// });

			let data = await PickupLocationModel.update(
				{
					barID: barId,
					address: req.body.address,
					updatedAt: new Date()
				},
				{
					where: {
						id: req.body.id
					}
				}
			);

			// for (const subCatId of req.body?.sub_category_ids) {
			// 	await PickupLocationSubCategoryModel.create({
			// 		barID: barId,
			// 		subCategoryID: subCatId,
			// 		pickupLocationID: req.body.id
			// 	});
			// }

			// if (req.body?.updateCategoryProducts) {
			// 	for (const subCatId of req.body?.sub_category_ids) {
			// 		await ProductModel.update(
			// 			{
			// 				pickupLocationID: req.body.id
			// 			},
			// 			{
			// 				where: {
			// 					categoryID: subCatId,
			// 					isDeleted: 'No'
			// 				}
			// 			}
			// 		);
			// 	}
			// }

			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* delete */
	async delete(req, res) {
		try {
			let barId = req.body.bar_id;
			let data = await PickupLocationModel.update(
				{
					isDeleted: 'Yes'
				},
				{
					where: {
						id: req.body.id,
						barID: barId
					}
				}
			);

			// await PickupLocationSubCategoryModel.destroy({
			// 	where: {
			// 		pickupLocationID: req.body.id
			// 	}
			// });

			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	}
};
