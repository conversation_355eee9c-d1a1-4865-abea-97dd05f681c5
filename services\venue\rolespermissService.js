/*Messages,status code and services require*/
require("dotenv").config();

const Sequelize = require("sequelize");
const constant = require("../../config/constant");

const Role = require("../../database/models").venue_user_roles; //import modal always Capital
const Permission = require("../../database/models").venue_user_permission; //import modal always Capital
const RolePermission =
  require("../../database/models").venue_user_role_permission; //import modal always Capital
const Op = Sequelize.Op;

module.exports = {
  /* List all roles with permissions */
  async listPermission(req, res) {
    let permissions = await Permission.findAll({
      attributes: ["id", "permission"],
    });
    return permissions;
  },

  /* List all roles with permissions */
  async listRolePermission(req, res) {
    const limit =
      req.body.per_page === undefined ? constant.LIMIT : req.body.per_page;
    const offset = (req.body.page - 1) * limit;
    let order = req.body.order === undefined ? "ASC" : req.body.order;
    let sort_by =
      req.body.sort_by === undefined ? "created_at" : req.body.sort_by;
    // let search = req.body.search === undefined ? "" : req.body.search;

    let role_permission = await Role.findAll({
      // where: { admin_permission: { [Op.like]: `%${search}%` } },
      offset: offset,
      limit: limit,
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      include: {
        model: RolePermission,
        attributes: [
          "id",
          "venue_user_permission_id",
          "add",
          "edit",
          "delete",
          "view",
          "status",
        ],
        include: {
          model: Permission,
        },
      },
    });
    return role_permission;
  },

  /* Add roles with permissions */
  async addRolePermission(req) {
    try {
      let roleObj = {
        role: req.body.role,
      };
      let add_role = await Role.create(roleObj);

      let permissionsObj = req.body.permissions.map((permission) => ({
        ...permission,
        venue_user_role_id: add_role.id,
      }));
      let add_role_permission = await RolePermission.bulkCreate(
        permissionsObj,
        { returning: true }
      );
      return { role: add_role, permissions: add_role_permission };
    } catch (error) {
      return error;
    }
  },

  /* update roles with permissions */
  async updateRolePermission(req) {
    try {
      let roleObj = {
        role: req.body.role,
      };
      await Role.update(roleObj, {
        where: { id: req.body.id, deleted_at: null },
      });
      await Promise.all(
        await req.body.permissions?.map(async (permission, index) => {
          let role_permission_exists = await RolePermission.findOne({
            where: {
              venue_user_permission_id: permission.venue_user_permission_id,
              venue_user_role_id: req.body.id,
            },
          });
          if (role_permission_exists) {
            await RolePermission.update(permission, {
              where: {
                venue_user_permission_id: permission.venue_user_permission_id,
                venue_user_role_id: req.body.id,
                deleted_at: null,
              },
            });
          } else {
            let permissionObj = {
              ...permission,
              venue_user_role_id: req.body.id,
            };
            await RolePermission.create(permissionObj);
          }
        })
      );
      return 1;
    } catch (error) {
      return error;
    }
  },

  /* delete roles with permissions */
  async deleteRolePermission(req) {
    let role = await Role.findOne({
      where: { id: req.body.role_id, deleted_at: null },
    });
    if (role) {
      // Delete Role
      await Role.destroy({
        where: {
          id: req.body.role_id,
        },
      });
      // Delete Role Permission
      await RolePermission.destroy({
        where: {
          venue_user_role_id: req.body.role_id,
        },
      });
      return 1;
    } else {
      return 0;
    }
  },

  /* change role status */
  async changeStatusRolePermission(req) {
    let role = await Role.findOne({
      where: { id: req.body.role_id, deleted_at: null },
    });
    //if role exist
    if (role) {
      // Change Role Status
      let roleObj = {
        ...role,
        status: !role.status,
      };
      await Role.update(roleObj, { where: { id: req.body.role_id } });
      return 1;
    } else {
      return 0;
    }
  },
};
