// Used to interact with AWS Service

require('dotenv').config();

const request = require('request');
var AWS = require('aws-sdk');
const fs = require('fs');
const constant = require('../config/constant');

AWS.config.update({
	secretAccessKey: process.env.S3_SECRETKEY,
	accessKeyId: process.env.S3_ACCESSKEY,
	region: process.env.S3_REGION
});

var s3 = new AWS.S3({
	params: {
		Bucket: process.env.S3_BUCKET_NAME
	}
});

// Create an STS client
// const sts = new AWS.STS();

// async function assumeRole() {
// 	const params = {
// 		RoleArn: constant.AWSS3ARN,
// 		RoleSessionName: 'awsMyTabS3AccessRole'
// 	};

// 	try {
// 		const data = await sts.assumeRole(params).promise();
// 		return {
// 			accessKeyId: data.Credentials.AccessKeyId,
// 			secretAccessKey: data.Credentials.SecretAccessKey,
// 			sessionToken: data.Credentials.SessionToken
// 		};
// 	} catch (error) {
// 		console.log('Error assuming role:', error);
// 		throw error;
// 	}
// }

function s3UploadDoshiiFile(file, path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();

	return new Promise((resolve, reject) => {
		const options = {
			uri: file,
			encoding: null
		};

		request(options, async function (error, response, body) {
			if (error || response.statusCode !== 200) {
				console.log('failed to get image');
				console.log(error);
				reject({ message: 'Could not upload image', err: e });
			} else {
				s3.putObject(
					{
						Body: body,
						Key: path,
						Bucket: process.env.S3_PUBLIC_BUCKET_NAME
					},
					function (error, data) {
						if (error) {
							console.log('error downloading image to s3');
							reject({ message: 'Could not upload image', err: e });
						} else {
							resolve({ message: 'image uploaded to s3' });
						}
					}
				);
			}
		});
	});
}

module.exports = s3UploadDoshiiFile;
