"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
    class settings extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            // define association here
        }
    }
    settings.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            paramKey: DataTypes.TEXT,
            barID: DataTypes.INTEGER,
            paramFix: DataTypes.FLOAT,
            paramVar: DataTypes.FLOAT
        },
        {
            sequelize,
            modelName: "settings",
            timestamps: false,
            freezeTableName: true,
        }
    );
    return settings;
};
