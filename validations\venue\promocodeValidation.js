const Joi = require('joi');

module.exports = {
	async add(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			name: Joi.string().required().messages({
				'any.required': 'name is required',
				'string.empty': 'name is required'
			}),
			code: Joi.string().required().messages({
				'any.required': 'code is required',
				'string.empty': 'code is required'
			}),
			description: Joi.string().optional().messages({
				'any.required': 'description is required',
				'string.empty': 'description is required'
			}),
			starts_at: Joi.date().required().messages({
				'any.required': 'startsAt is required',
				'string.empty': 'startsAt is required'
			}),
			discount_amount: Joi.number().required().messages({
				'any.required': 'discount_amount is required',
				'string.empty': 'discount_amount is required'
			})
		}).unknown(true);
		return schema.validate(req.body);
	},
	async list(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			})
		});
		return schema.validate(req.body);
	},
	async edit(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			name: Joi.string().optional().messages({
				'any.required': 'name is required',
				'string.empty': 'name is required'
			}),
			code: Joi.string().optional().messages({
				'any.required': 'code is required',
				'string.empty': 'code is required'
			}),
			description: Joi.string().optional().messages({
				'any.required': 'description is required',
				'string.empty': 'description is required'
			}),
			starts_at: Joi.date().optional().messages({
				'any.required': 'startsAt is required',
				'string.empty': 'startsAt is required'
			}),
			discount_amount: Joi.number().optional().messages({
				'any.required': 'discount_amount is required',
				'string.empty': 'discount_amount is required'
			}),
			status: Joi.string().valid('Active', 'Inactive').optional({})
		}).unknown(true);
		return schema.validate(req.body);
	},
	async delete(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			})
		});
		return schema.validate(req.body);
	}
};
