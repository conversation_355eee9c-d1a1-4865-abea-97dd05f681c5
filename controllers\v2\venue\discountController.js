const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;
const discountService = require('../../../services/v2/venue/discountService');
const discountValidation = require('../../../validations/v2/venue/discountValidation');

module.exports = {
	async addDiscount(req, res) {
		try {
			const valid = await discountValidation.addDiscountValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await discountService.addDiscount(req);

			if (data.code > 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data.data,
					data.message,
					status.SUCCESS
				);
			} else if (data.code == '-3') {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.CUSTOMERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('Controller error:', error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async updateDiscount(req, res) {
		try {
			const valid = await discountValidation.updateDiscountValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await discountService.updateDiscount(req);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('Controller error:', error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async deleteDiscount(req, res) {
		try {
			const valid = await discountValidation.deleteDiscountValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await discountService.deleteDiscount(req);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('Controller error:', error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getDiscountList(req, res) {
		try {
			const valid = await discountValidation.getDiscountValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await discountService.getDiscountList(req);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data.data,
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('Controller error:', error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getDiscountDetails(req, res) {
		try {
			const valid = await discountValidation.getDiscountDetailsValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			const data = await discountService.getDiscountDetails(req);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data.data,
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('Controller error:', error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
