var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/adminAuth").adminAuthorization;

/*require for security */
const TodoController = require("../../controllers/cms/todoController");

/*Add todo */
router.post("/add", Authorization, TodoController.add);

/*delete todo */
// router.delete("/delete", Authorization, TodoController.deleteTodo);

/*edit todo */
router.put("/update", Authorization, TodoController.update);

/*list todo */
router.post("/list", Authorization, TodoController.list);

/* change status to completed  */
router.put("/update-status", Authorization, TodoController.updateStatus);

module.exports = router;
