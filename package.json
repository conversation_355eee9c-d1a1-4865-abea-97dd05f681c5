{"name": "MyTab", "version": "0.0.0", "private": true, "main": "app.js", "scripts": {"start": "nodemon app.js"}, "dependencies": {"@joi/date": "^2.1.0", "apn": "^2.2.0", "aws-sdk": "^2.1692.0", "axios": "^0.21.4", "bcryptjs": "^2.4.3", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "crypto": "^1.0.1", "debug": "~2.6.9", "dotenv": "^8.2.0", "ejs": "^3.1.5", "eventsource": "^1.1.0", "express": "^4.16.4", "express-actuator": "^1.8.2", "fcm-notification": "^2.0.0", "hbs": "~4.0.4", "http-errors": "~1.6.3", "joi": "^17.7.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^8.5.1", "jwt-decode": "^3.1.2", "mime": "^2.5.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "~1.9.1", "multer": "^1.4.2", "mysql2": "^2.1.0", "node-cron": "^3.0.2", "nodemailer": "^6.4.11", "nodemon": "^2.0.4", "path": "^0.12.7", "qrcode": "^1.4.4", "sequelize": "^5.21.2", "sequelize-cli": "^6.2.0", "sharp": "^0.31.3", "speakeasy": "^2.0.0", "stripe": "^12.7.0"}}