'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class segment_tags extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {}
	}
	segment_tags.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			isActive: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '1'
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE }
		},

		{
			sequelize,
			modelName: 'segment_tags',
			timestamps: true,
			freezeTableName: true
		}
	);
	return segment_tags;
};
