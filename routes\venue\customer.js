var express = require('express');
var router = express.Router();

/* require for Authentication */
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;

const CustomerController = require('../../controllers/venue/customerController');

/* Customer list API */
router.post(
	'/list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	CustomerController.list
);
router.post(
	'/details',
	venueAuthorization,
	IsVenueConnectedWithBar,
	CustomerController.details
);
router.post(
	'/order-list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	CustomerController.orderList
);
router.post(
	'/segment-list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	CustomerController.segmentList
);
router.post(
	'/download',
	venueAuthorization,
	IsVenueConnectedWithBar,
	CustomerController.download
);

router.get('/cron', CustomerController.cron);

module.exports = router;
