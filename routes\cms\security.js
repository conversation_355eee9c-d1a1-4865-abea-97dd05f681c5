var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/adminAuth").adminAuthorization;

/*require for security */
const SecurityController = require("../../controllers/cms/securityController");

/* Security API */
router.post(
  "/change-password",
  Authorization,
  SecurityController.changePassword
);
router.get(
  "/change-password-date",
  Authorization,
  SecurityController.changePasswordDate
);

router.get("/device-list", Authorization, SecurityController.getDeviceList);

router.post("/logout-device", Authorization, SecurityController.logoutDevice);

module.exports = router;
