'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class itemActiveHours extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// pos_conf.belongsTo(models.admin, {
			//   foreignKey: "assigned_to",
			//   targetKey: "id",
			//   onDelete: "CASCADE",
			// });
			// product_food_options.belongsTo(models.food_options, {
			//   foreignKey: "foodOptionID",
			//   targetKey: "id",
			//   onDelete: "CASCADE",
			// });
			itemActiveHours.belongsTo(models.sub_category, {
				foreignKey: 'subCategoryID'
			});
			itemActiveHours.belongsTo(models.bar, {
				foreignKey: 'barID',
				targetKey: 'id'
			});
			models.sub_category.hasMany(itemActiveHours, {
				foreignKey: 'subCategoryID'
			});
		}
	}
	itemActiveHours.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			weekDay: {
				type: DataTypes.INTEGER,
				defaultValue: 0
			},
			activeHours: {
				type: DataTypes.TIME
			},
			inActiveHours: {
				type: DataTypes.TIME
			},
			status: {
				type: DataTypes.ENUM('0', '1')
			}
		},

		{
			sequelize,
			modelName: 'itemActiveHours',
			freezeTableName: true,
			timestamps: false
		}
	);
	return itemActiveHours;
};
