/*Messages,status code and services require*/
require('dotenv').config();

const moment = require('moment');

const BarModel = require('../../../database/models').bar;
const SegmentTagsModel = require('../../../database/models').segment_tags;

const ProductModel = require('../../../database/models').product;
const BarCategorySequenceModel =
	require('../../../database/models').bar_category_sequence;
const SubCategoryModel = require('../../../database/models').sub_category;
const { sequelize } = require('../../../database/models');
const message = require('../../../config/cmsMessage').cmsMessage;
const Sequelize = require('sequelize');

const Op = Sequelize.Op;

module.exports = {
	async getVenueSubCategories(req, res) {
		var barID = req.body.bar_id;

		try {
			const barData = await BarModel.findOne({
				attributes: ['id', 'restaurantName', 'posStatus'],
				where: {
					id: barID
				}
			});
			if (
				barData.dataValues.posStatus &&
				barData.dataValues.posStatus === '1'
			) {
				var whereClause = {
					categoryID: '-1',
					isDeleted: 'No'
				};
			} else {
				var whereClause = {
					[Op.not]: [{ categoryID: '-1' }],
					isDeleted: 'No'
				};
			}

			let categories = await SubCategoryModel.findAll({
				attributes: [
					'id',
					'categoryID',
					'name',
					[
						Sequelize.literal(`(
              				SELECT COUNT(product.id) 
              				FROM product 
              				WHERE product.subCategoryID = sub_category.id 
                			AND product.isDeleted = "No" 
                			AND product.barID = ${barID}
            			)`),
						'productCount'
					]
				],
				include: [
					{
						model: BarCategorySequenceModel,
						as: 'bar_category_sequence',
						required: false,
						attributes: [
							[
								Sequelize.fn(
									'coalesce',
									Sequelize.col('subCategorySequence'),
									1000000000000
								),
								'subCategorySequence'
							]
						],
						where: { barId: barID }
					}
				],
				order: [
					[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
					'id'
				],
				where: {
					...whereClause,
					[Op.and]: [
						Sequelize.literal(`(
						SELECT COUNT(product.id) 
						FROM product 
						WHERE product.subCategoryID = sub_category.id 
						  AND product.isDeleted = "No" 
						  AND product.barID = ${barID}
					  ) > 0`)
					]
				}
			});

			if (categories.length > 0) {
				return categories;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async getSegmentTags(req, res) {
		try {
			// const segmentTags = await SegmentTagsModel.findAll();
			const segmentTags = await SegmentTagsModel.findAll({
				where: { isActive: '1'},
				order: [['sequence', 'ASC']] // or DESC
			});
			return segmentTags;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async globalPriceUpdate(req, res) {
		const { bar_id, sign, unit, amount } = req.body;

		try {
			// Fetch products based on barID
			const productsData = await ProductModel.findAll({
				where: {
					barID: bar_id,
					isDeleted: 'No'
				}
			});

			if (!productsData || productsData.length === 0) {
				return 0; // No products found
			}

			// Update product prices
			const updatedProducts = productsData.map((product) => {
				let updatedPrice = product.price;

				if (unit == '$') {
					updatedPrice =
						sign == '+'
							? parseFloat(product.price) + parseFloat(amount)
							: parseFloat(product.price) - parseFloat(amount);
				} else if (unit == '%') {
					const adjustment =
						(parseFloat(product.price) * parseFloat(amount)) / 100;
					updatedPrice =
						sign == '+'
							? parseFloat(product.price) + adjustment
							: parseFloat(product.price) - adjustment;
				}

				// Ensure price doesn't go below 0 and format to 2 decimal places
				updatedPrice = Math.max(0, updatedPrice).toFixed(2);

				return {
					id: product.id, // Ensure `id` is included
					price: parseFloat(updatedPrice) // Convert back to number for database compatibility
				};
			});

			// Update database
			await Promise.all(
				updatedProducts.map(async (product) => {
					await ProductModel.update(
						{ price: product.price },
						{ where: { id: product.id } }
					);
				})
			);

			return 1; // Successfully updated products
		} catch (error) {
			throw error; // Re-throw the error for further handling
		}
	}
};
