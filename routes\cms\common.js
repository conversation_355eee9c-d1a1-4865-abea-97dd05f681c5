var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/adminAuth").adminAuthorization;
const MulterMiddleware = require("../../middleware/multer");

/* require for Controller */
const AdminController = require("../../controllers/cms/adminController");

/* routes of admin panel */

/* Login API */
router.post("/login", AdminController.signIn);

/* Verify MFA API */
router.post("/verify-mfa", AdminController.verifyMFA);

/* Verify MFA OTP */
router.post("/verify-mfa-otp", AdminController.verifyMFAOtp);

/* Setup MFA API */
router.post("/setup-mfa", AdminController.setupMFA);

/* Logout API */
router.delete("/logout", Authorization, AdminController.signOut);

/* Forgot Password API */
router.post("/forgot-password", AdminController.forgotPassword);

/* Verify token */
router.post("/verify-token", AdminController.verifyToken);

/* Reset Password API */
router.post("/reset-password", AdminController.resetPassword);

/* Edit Profile API */
router.put(
  "/edit-profile",
  MulterMiddleware.singleProfilePic,
  Authorization,
  AdminController.updateProfile
);

/* get profile API */
router.get(
  "/get-profile-details",
  Authorization,
  AdminController.getProfileDetails
);



module.exports = router;
