"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class venue_user_roles extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      venue_user_roles.hasMany(models.venue_user_role_permission, {
        foreignKey: "venue_user_role_id",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  venue_user_roles.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      role: DataTypes.STRING(50),
      status: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
    },
    {
      sequelize,
      paranoid: true,
      underscored: true,
      modelName: "venue_user_roles",
      timestamps: true,
    }
  );
  return venue_user_roles;
};
