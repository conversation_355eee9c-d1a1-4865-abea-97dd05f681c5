const Joi = require('joi');

module.exports = {
	async list(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			})
		});
		return schema.validate(req.body);
	},

	async update(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			matchCategoryOpeningHours: Joi.string()
				.valid('Yes', 'No')
				.required()
				.messages({
					'any.required': 'Match category opening hours field is required',
					'string.empty': 'Match category opening hours field is required',
					'any.only':
						'Match category opening hours field must be either "Yes" or "No".'
				}),
			venueOpeningHours: Joi.array()
				.items(
					Joi.object({
						weekDay: Joi.number().integer().min(0).max(6).required(),
						isClosed: Joi.string().required(),
						timeSlots: Joi.array()
							.items(
								Joi.object({
									openingHours: Joi.string().required(),
									closingHours: Joi.string().required()
								})
							)
							.required()
					})
				)
				.required()
				.messages({
					'any.required': 'venueOpeningHours is required',
					'array.base': 'venueOpeningHours must be an array'
				})
		});
		return schema.validate(req.body);
	},
	async updateCategoryHours(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			subCategoryID: Joi.string().required().messages({
				'any.required': 'Sub category id is required',
				'string.empty': 'Sub category id is required'
			}),
			subCategoryOpeningHours: Joi.array()
				.items(
					Joi.object({
						weekDay: Joi.number().integer().min(0).max(6).required(),
						isClosed: Joi.string().required(),
						timeSlots: Joi.array()
							.items(
								Joi.object({
									openingHours: Joi.string().required(),
									closingHours: Joi.string().required()
								})
							)
							.required()
					})
				)
				.required()
				.messages({
					'any.required': 'Sub category opening hours is required',
					'array.base': 'Sub category opening hours must be an array'
				})
		});
		return schema.validate(req.body);
	}
};
