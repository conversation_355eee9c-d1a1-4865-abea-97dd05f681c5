"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert(
      "admin_roles",
      [
        {
          id: "1",
          role: "System Administrator",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: "2",
          role: "Administrator",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: "3",
          role: "Registered Dietitian",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ],
      {}
    );
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  },
};
