CREATE TABLE `mytab`.`venue_user_permission` ( `id` INT NOT NULL AUTO_INCREMENT , `role` VARCHAR(50) NULL DEFAULT NULL , `status` TINYINT(1) NOT NULL , `created_at` DATETIME NOT NULL , `updated_at` DATETIME NOT NULL , `deleted_at` DATETIME NULL DEFAULT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;
CREATE TABLE `mytab`.`venue_user_roles` ( `id` INT NOT NULL AUTO_INCREMENT , `permission` VARCHAR(50) NULL , `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP , `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP , `deleted_at` DATETIME NULL DEFAULT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;
CREATE TABLE `mytab`.`venue_user_role_permission` ( `id` INT NOT NULL AUTO_INCREMENT , `venue_user_role_id` INT NOT NULL , `venue_user_permission_id` INT NOT NULL , `view` TINYINT(1) NOT NULL , `add` TINYINT(1) NOT NULL , `delete` TINYINT(1) NOT NULL , `edit` TINYINT(1) NOT NULL , `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP , `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP , `deleted_at` DATETIME NULL DEFAULT NULL , `status` TINYINT(1) NOT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;