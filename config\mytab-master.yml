include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'

stages:
  - build
  - deploy

variables:
  PROJECT: 'mytab-newcms-api'
  TECHNOLOGY: 'nodejs'

build:
  stage: build
  extends: .build
  variables:
    BUILD_ARGS: '--build-arg APP_NAME=${PROJECT} --build-arg NODE_ENV=${CI_COMMIT_REF_NAME}'

  only:
    - master

deploy_master:
  stage: deploy
  extends: .deploy
  environment:
    name: $CI_COMMIT_REF_NAME
    # url: https://cms.mytabinfo.com/api

  variables:
    CONT_PORT: '6823'
    DEPLOY_PATH: $PROJECT
    IP_ADDRESS: '***********'
    DOCKER_COMPOSE_TEMPLATE: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/docker/docker-compose.yaml'
  only:
    - master
