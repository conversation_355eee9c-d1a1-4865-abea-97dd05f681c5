// Used to interact with AWS Service

require('dotenv').config();

var AWS = require('aws-sdk');
const fs = require('fs');
const constant = require('../config/constant');

AWS.config.update({
	secretAccessKey: process.env.S3_SECRETKEY,
	accessKeyId: process.env.S3_ACCESSKEY,
	region: process.env.S3_REGION
});

var s3 = new AWS.S3({
	params: {
		Bucket: process.env.S3_BUCKET_NAME
	}
});

// Create an STS client
// const sts = new AWS.STS();

// async function assumeRole() {
// 	const params = {
// 		RoleArn: constant.AWSS3ARN,
// 		RoleSessionName: 'awsMyTabS3AccessRole'
// 	};

// 	try {
// 		const data = await sts.assumeRole(params).promise();
// 		return {
// 			accessKeyId: data.Credentials.AccessKeyId,
// 			secretAccessKey: data.Credentials.SecretAccessKey,
// 			sessionToken: data.Credentials.SessionToken
// 		};
// 	} catch (error) {
// 		console.log('Error assuming role:', error);
// 		throw error;
// 	}
// }

function s3Delete(path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();

	return s3.deleteObject(
		{
			Bucket: process.env.S3_BUCKET_NAME,
			Key: path
		},
		function (err, data) {
			if (err) {
				console.log('err', err);
			}
			console.log('Successfully deleted image on Amazon S3 ', data);
		}
	);
}

function s3PublicDelete(path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();
	return s3.deleteObject(
		{
			Bucket: process.env.S3_PUBLIC_BUCKET_NAME,
			Key: path
		},
		function (err, data) {
			if (err) {
				console.log('err', err);
			}
			console.log('Successfully deleted image on Amazon S3 ', data);
		}
	);
}

module.exports = { s3Delete, s3PublicDelete };
