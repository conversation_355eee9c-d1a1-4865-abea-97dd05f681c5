sherlock: 
  stage: .pre
  image: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:sherlock
  script: 
    - FILENAME=$(ls -1 | grep -io "readme.md"); cp $FILENAME /compare
    - SCORE=$(sherlock -e .md /compare | cut -d';' -f3 | cut -d'%' -f1)
    - if [ "$SCORE" -gt 50 ];then echo "Readme Template Match more than threshold" ;exit 1;fi
  tags:
    - spot-runner

default:
 stage: .pre
 image: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:node14alpine
 script:
   - apk add git
   - npm install -g node-git-info
   - node-git-info
 artifacts:
   paths: 
    - git.properties
.build:
  environment:
    name: $CI_COMMIT_REF_SLUG
  variables:
    BUILD_ARGS: ""
    DOCKERFILE_PATH: "Dockerfile" 
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    #- if [ -z "$TARGET_PATH" ];then cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env;else echo "read env from s3";fi
    - cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env || ls -la
    - rm -rf .env.*; rm -rf env
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --cache=true --context ${CONTEXT:-$CI_PROJECT_DIR} --dockerfile $CI_PROJECT_DIR/$DOCKERFILE_PATH $BUILD_ARGS --registry-mirror mirror.gcr.io --registry-mirror index.docker.io --destination $CI_REGISTRY_IMAGE:$IMAGE_TAG
  tags:
    - spot-runner  
  only:
    - development
    - master
    - production

.build_static:
  environment:
    name: $CI_COMMIT_REF_SLUG
  variables:
    CI: "false"
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
  tags:
    - spot-runner
  image:
    name: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:node14alpine
  before_script:
      - apk add git curl
  script:
    #- if [ -z "$TARGET_PATH" ];then cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env;else echo "read env from s3";fi
    - cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env || ls -la
    - rm -rf .env.*
    - npm install
    - npm run build
  artifacts:
    paths: 
     - build/*
  only:
    - development
    - master
    - production

.deploy_static:
  environment:
    name: $CI_COMMIT_REF_SLUG
    url: https://$PROJECT.openxcell.dev
  variables:
    BUILD_PATH: build
    DEPLOY_PATH: /srv/www/react/$PROJECT/
    USER: "ubuntu"
    IP_ADDRESS: "ssh.openxcell.dev"
  image: 
    name: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:openxcelltechnolabsshalpine
    #name: openxcelltechnolab/ssh:alpine
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "sudo mkdir -p $DEPLOY_PATH || exit 0; sudo chown -R $USER:$USER $DEPLOY_PATH"
    - rsync -avrx -e 'ssh -o StrictHostKeychecking=no' --delete ./$BUILD_PATH/ ${USER}@${IP_ADDRESS}:$DEPLOY_PATH 
    - if [[ $CI_COMMIT_REF_SLUG == "development" ]]; then \
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk https://${CI_SERVER_HOST}/-/snippets/2/raw | sed 's%__PROJECT_NAME__%${PROJECT}%g' > ${NGINX_PATH}/${PROJECT}.conf";\
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "if docker exec web nginx -t;then docker exec web nginx -s reload;fi";fi
  tags:
    - spot-runner  
  only:
    - development
    - master
    - production

.static:
  environment:
    name: $CI_COMMIT_REF_SLUG
    url: https://$PROJECT.openxcell.dev
  variables:
    BUILD_PATH: .
    DEPLOY_PATH: /srv/www/static/$PROJECT/
    USER: "ubuntu"
    IP_ADDRESS: "ssh.openxcell.dev"
  image: 
    name: openxcelltechnolab/ssh:alpine
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "sudo mkdir -p $DEPLOY_PATH || exit 0; sudo chown -R $USER:$USER $DEPLOY_PATH"
    - rsync -avrx -e 'ssh -o StrictHostKeychecking=no' --delete ./$BUILD_PATH/ ${USER}@${IP_ADDRESS}:$DEPLOY_PATH 
    - if [[ $CI_COMMIT_REF_SLUG == "development" ]]; then \
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk https://${CI_SERVER_HOST}/-/snippets/18/raw | sed 's%__PROJECT_NAME__%${PROJECT}%g' > ${NGINX_PATH}/${PROJECT}.conf";\
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "if docker exec web nginx -t;then docker exec web nginx -s reload;fi";fi
  tags:
    - spot-runner  
  only:
    - development
    - master
    - production

.get_env:
  environment:
    name: $CI_COMMIT_REF_SLUG
  variables:
    TARGET_PATH: ".env"
  image:
    name: amazon/aws-cli:2.4.9
    entrypoint: [""]
  script:
   # - echo "S3_ENV=true" > s3.env
    - aws s3 cp s3://$BUCKET_NAME $TARGET_PATH
  artifacts:
    paths: 
     - $TARGET_PATH
   # reports:
   #   dotenv: s3.env
    expire_in: 1 hour
  tags:
    - spot-runner  
  only:
    - development
    - master
    - production

.deploy:
  environment:
    name: $CI_COMMIT_REF_SLUG
    url: https://$PROJECT.api.openxcell.dev
  variables:
    CONT_PORT: ""
    DEPLOY_PATH: /srv/www/$TECHNOLOGY/$PROJECT
    USER: "ubuntu"
    IP_ADDRESS: "api.openxcell.dev"
    DOCKER_COMPOSE_TEMPLATE: "https://${CI_SERVER_HOST}/-/snippets/15/raw"
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
  image: 
    name: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:openxcelltechnolabsshalpine
    #name: openxcelltechnolab/ssh:alpine
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker system prune -f
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} mkdir -p ${DEPLOY_PATH}
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk $DOCKER_COMPOSE_TEMPLATE | sed 's%__PROJECT_NAME__%${PROJECT}%g;s%__PROJECT_SLUG__%${PROJECT_SLUG}%g;s%__PORT__%${CONT_PORT}%g;s%__IMAGE_URI__%${CI_REGISTRY_IMAGE}%g;s%__IMAGE_TAG__%${IMAGE_TAG}%g' > ${DEPLOY_PATH}/docker-compose.yml"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose pull"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose up -d --force-recreate"
  tags:
    - spot-runner  
  only:
    - development
    - master
    - production

.cf_deploy:
  stage: deploy
  extends: .deploy_devspace

.deploy_devspace:
  stage: deploy

  environment:
      name: $CI_COMMIT_REF_SLUG
      url: https://$PROJECT_SLUG.$DEV_BASE_DOMAIN
  variables:
    # PORT: Must Declare in CI , No Defaults Set
    NAMESPACE: "development"
    PROJECT_SLUG: $PROJECT
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
    CPU_REQUEST: 30m
    MEMORY_REQUEST: 150Mi
    CPU_LIMIT: 120m
    MEMORY_LIMIT: 712Mi
    AWS_ACCESS_KEY_ID: $KOPS_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $KOPS_AWS_SECRET_ACCESS_KEY
  image: registry.gitlab.openxcell.dev/public-resources/gitlab-ci:devspace
  script:
    - if [ -f "aws_auth.txt" ]; then source aws_auth.txt ;fi
    - if [ "${CONT_PORT}" == "" ];then exit;fi
    - export IMAGE_DIGEST=$(skopeo inspect --creds "${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD}" docker://$CI_REGISTRY_IMAGE:$IMAGE_TAG | jq -r .Digest)
    - echo $IMAGE_DIGEST
    - if [ -z ${MOUNT_PATH} ];then curl -sLo devspace.yaml $DEVSPACE_TEMPLATE ;else curl -sLo devspace.yaml $DEVSPACE_STATEFUL_TEMPLATE;fi
    - devspace use namespace $NAMESPACE
    - devspace deploy --force-deploy --silent
  tags:
    - spot-runner  
  only: 
    - development
