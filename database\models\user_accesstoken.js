'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class user_accesstoken extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			user_accesstoken.belongsTo(models.user, { foreignKey: 'userID' });
		}
	}
	user_accesstoken.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			accessToken: DataTypes.TEXT,
			deviceType: DataTypes.ENUM('ios', 'android'),
			deviceToken: DataTypes.TEXT,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE
		},
		{
			sequelize,
			paranoid: true,
			freezeTableName: true,
			modelName: 'user_accesstoken'
		}
	);
	return user_accesstoken;
};
