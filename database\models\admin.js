'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class admin extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			admin.belongsTo(models.admin_role, {
				foreignKey: 'admin_role_id',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
		}
	}
	admin.init(
		{
			admin_role_id: DataTypes.INTEGER,
			first_name: DataTypes.STRING(100),
			last_name: DataTypes.STRING(100),
			email: DataTypes.STRING(150),
			password: DataTypes.STRING(100),
			password_updated_at: DataTypes.DATE,
			profile_image: {
				type: DataTypes.STRING,
				get() {
					return this.getDataValue('profile_image')
						? constant.AWSS3URL +
								constant.AWSS3ADMINFOLDER +
								this.getDataValue('profile_image')
						: null;
				}
			},
			otp: DataTypes.STRING(100),
			otp_token: DataTypes.STRING(100),
			mfa_code: DataTypes.STRING(50),
			mfa_qr_code: {
				type: DataTypes.STRING(50),
				get() {
					return this.getDataValue('mfa_qr_code')
						? constant.AWSS3URL +
								constant.AWSS3ADMINQRCODEFOLDER +
								this.getDataValue('mfa_qr_code')
						: null;
				}
			},

			status: DataTypes.INTEGER(4),
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			deletedAt: DataTypes.DATE
		},
		{
			hooks: {
				beforeCreate: function (admin, options) {
					if (admin.password) {
						return bcrypt
							.hash(admin.password, 10)
							.then((hash) => {
								admin.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				beforeUpdate: function (admin, options) {
					if (admin.password) {
						return bcrypt
							.hash(admin.password, 10)
							.then((hash) => {
								admin.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				afterFind: async function (admin) {
					if (admin && admin.constructor === Array) {
						admin.map(async (data) => {
							if (data && data.profile_image) {
								// data.profile_image = await helper.s3GetImage(constant.AWSS3ADMINFOLDER + data.profile_image);
							}
							if (data && data.mfa_qr_code) {
								// data.mfa_qr_code = await helper.s3GetImage(constant.AWSS3QRCODEFOLDER + data.mfa_qr_code);
							}
						});
					} else {
						if (admin && admin.profile_image) {
							// admin.profile_image = await helper.s3GetImage(constant.AWSS3ADMINFOLDER + admin.profile_image);
						}
						if (admin && admin.mfa_qr_code) {
							// admin.mfa_qr_code = await helper.s3GetImage(constant.AWSS3QRCODEFOLDER + admin.mfa_qr_code);
						}
					}
				}
			},
			sequelize,
			freezeTableName: true,
			modelName: 'admin',
			timestamps: true
		}
	);
	return admin;
};
