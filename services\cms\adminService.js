/*Messages,status code and services require*/
require('dotenv').config();

const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const Admin = require('../../database/models').admin; //import modal always Capital
const AdminRole = require('../../database/models').admin_role; //import modal always Capital
const constant = require('../../config/constant');
const generateRandomString =
	require('../../helper/generalHelper').generateRandomString;
const Sequelize = require('sequelize');
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const AdminToken = require('../../database/models').admin_login_session;
const { default: jwtDecode } = require('jwt-decode');
const { s3QrUpload } = require('../../middleware/multerAwsUpload');
const User = require('../../database/models').user;
const Op = Sequelize.Op;

module.exports = {
	/* Sign-In */
	async signIn(req, res) {
		try {
			let findAdmin = await Admin.findOne({
				where: { email: req.body.email, deletedAt: null },
				include: [
					{
						model: AdminRole,
						attributes: ['id', 'role']
					}
				],
				attributes: {
					exclude: [
						'createdAt',
						'updatedAt',
						'deletedAt',
						'otp_token',
						'otp',
						'admin_role_id'
					]
				}
			});

			if (findAdmin) {
				if (
					findAdmin.dataValues.status === constant.PERMANENTLY_INACTIVE &&
					findAdmin.dataValues.status === constant.TEMPORARY_INCTIVE
				) {
					return 1; // account is inactive
				}

				// Check Password
				if (
					!Bcryptjs.compareSync(
						req.body.password,
						findAdmin.dataValues.password
					)
				) {
					return 0; //password is incorrect
				}

				if (findAdmin.dataValues.mfa_code === null) {
					let secret = speakeasy.generateSecret({
						length: 20,
						name: constant.APP_NAME
					});
					let imageData = await QRCode.toDataURL(secret.otpauth_url);

					let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
					let imageName =
						findAdmin.dataValues.id +
						'_' +
						generateRandomString(5) +
						'_' +
						Date.now() +
						'.' +
						extension;

					await QrimageUpload(
						imageData,
						constant.AWSS3ADMINQRCODEFOLDER + imageName
					);

					let qrCode = await helper.s3GetImage(
						constant.AWSS3ADMINQRCODEFOLDER + imageName
					);

					await Admin.update(
						{ mfa_code: secret.base32, mfa_qr_code: imageName },
						{ where: { id: findAdmin.dataValues.id, deletedAt: null } }
					);

					let subject = 'Multi-Factor Authentication - ' + constant.APP_NAME;
					let mailbody =
						'<div>' +
						'<p> Hello ' +
						findAdmin.dataValues.first_name +
						', </p>' +
						'<p>Welcome! The first step to gaining access to ' +
						constant.APP_NAME +
						' admin portal is setting up mulit-factor authentication, which helps us keep ' +
						constant.APP_NAME +
						' and our community safe. Follow the below instructions to set up multi-factor authentication for your account: </p>' +
						'<p>1. Download Google Authenticator <br/> 2. Scan the QR Code below to get set up in the Google Authenticator app <br/> 3.Enter the 6-digit code from Google Authenicator to login to your MyTab Venue CMS account </p>' +
						"<img src='" +
						qrCode +
						"' width='200'/>" +
						'<p>If you did not request this email, please disregard.</p>' +
						'<p>' +
						constant.APP_NAME +
						'</p>' +
						'</div>';

					await mail.sendmail(res, req.body.email, subject, mailbody);
				}
				delete findAdmin.dataValues.password;
				delete findAdmin.dataValues.mfa_qr_code;
				return findAdmin;
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	//verify MFA
	async verifyMFA(req) {
		let findAdmin = await Admin.findOne({
			where: {
				email: req.body.email,
				id: req.body.admin_id,
				deletedAt: null
			},
			include: [
				{
					model: AdminRole,
					attributes: ['id', 'role']
				}
			]
		});

		if (findAdmin) {
			let verified = speakeasy.totp.verify({
				secret: findAdmin.dataValues.mfa_code,
				encoding: 'base32',
				token: req.body.code,
				window: 1
			});

			if (verified) {
				// generate JWT token with email and admin id
				let jwtString = JwtToken.sign(
					{
						email: findAdmin.email,
						admin_id: findAdmin.id
					},
					constant.JWTTOKEN.secret,
					{
						expiresIn: constant.JWTTOKEN.expiresIn,
						algorithm: constant.JWTTOKEN.algo
					}
				); // default: HS256 encryption

				await AdminToken.create({
					access_token: jwtString,
					admin_id: findAdmin.dataValues.id,
					device_name: req.body.device_name,
					device_location: req.body.device_location,
					device_type: req.body.device_type,
					device_token: req.body.device_token
				});

				// await imageDelete(
				//     constant.AWSS3QRCODEFOLDER +
				//     findAdmin.mfa_qr_code
				// );

				// await Admin.update(
				//   {
				//     mfa_qr_code: null,
				//   },
				//   {
				//     where: {
				//       id: req.body.admin_id,
				//     },
				//   }
				// );

				findAdmin.dataValues.token = jwtString;
				delete findAdmin.dataValues.password;
				return findAdmin;
			}
			return 0;
		} else {
			return 0;
		}
	},

	//verify MFA OTP
	async verifyMFAOtp(req) {
		let findAdmin = await Admin.findOne({
			where: {
				email: req.body.email,
				id: req.body.admin_id,
				deletedAt: null
			},
			include: [
				{
					model: AdminRole,
					attributes: ['id', 'role']
				}
			]
		});
		if (findAdmin) {
			let verified = speakeasy.totp.verify({
				secret: findAdmin.dataValues.mfa_code,
				encoding: 'base32',
				token: req.body.code,
				window: 1
			});

			if (verified) {
				return 1;
			}
			return 0;
		} else {
			return 0;
		}
	},

	//setup MFA
	async setupMFA(req, res) {
		let findAdmin = await Admin.findOne({
			where: {
				email: req.body.email,
				id: req.body.admin_id,
				deletedAt: null
			}
		});

		if (!findAdmin) {
			return 0;
		}
		let qrCode;
		//deleted in verify api

		qrCode = await helper.s3GetImage(
			constant.AWSS3ADMINQRCODEFOLDER + findAdmin.dataValues.mfa_qr_code
		);

		if (findAdmin) {
			if (findAdmin.dataValues.mfa_code === null) {
				let secret = speakeasy.generateSecret({
					length: 20,
					name: constant.APP_NAME
				});
				let imageData = await QRCode.toDataURL(secret.otpauth_url);
				let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
				let imageName =
					findAdmin.dataValues.id +
					'_' +
					generateRandomString(5) +
					'_' +
					Date.now() +
					'.' +
					extension;

				await s3QrUpload(
					imageData,

					constant.AWSS3ADMINQRCODEFOLDER + imageName
				);

				qrCode = await helper.s3GetImage(
					constant.AWSS3ADMINQRCODEFOLDER + imageName
				);

				await Admin.update(
					{ mfa_code: secret.base32, mfa_qr_code: imageName },
					{ where: { id: findAdmin.dataValues.id, deletedAt: null } }
				);
			}

			let subject = 'Multi-Factor Authentication - ' + constant.APP_NAME;
			let mailbody =
				'<div>' +
				'<p> Hello ' +
				findAdmin.dataValues.first_name +
				', </p>' +
				'<p>Welcome! The first step to gaining access to ' +
				constant.APP_NAME +
				' admin portal is setting up mulit-factor authentication, which helps us keep ' +
				constant.APP_NAME +
				' and our community safe. Follow the below instructions to set up multi-factor authentication for your account: </p>' +
				'<p>1. Download Google Authenticator <br/> 2. Scan the QR Code below to get set up in the Google Authenticator app <br/> 3.Enter the 6-digit code from Google Authenicator to login to your MyTab Venue CMS account </p>' +
				"<img src='" +
				qrCode +
				"' width='200'/>" +
				'<p>If you did not request this email, please disregard.</p>' +
				'<p>' +
				constant.APP_NAME +
				'</p>' +
				'</div>';

			mail.sendmail(res, req.body.email, subject, mailbody);
			return 1;
		} else {
			return 0;
		}
	},

	/* Sign-Out */
	async signOut(req) {
		let signOut = await AdminToken.destroy({
			where: {
				access_token: req.headers.authorization
			}
		});
		return signOut;
	},

	/* Forgot-Password */
	async forgotPassword(req, res) {
		let findAdmin = await Admin.findOne({
			where: {
				email: req.body.email,
				deletedAt: null
			}
		});
		if (!findAdmin) {
			return 0; // user not found
		}
		let token = helper.generateExpirationToken(req.body.email);
		//save token
		await Admin.update(
			{ otp_token: token },
			{
				where: {
					email: req.body.email
				}
			}
		);

		if (findAdmin) {
			var subject = 'Reset Your my-tab Password';
			var mailbody =
				'<div><p>We have received a request to reset your password for <b>' +
				req.body.email +
				'</b>.</p>' +
				'<p> If you didn’t make the request, just ignore this email.Otherwise, please reset your password by ' +
				'<a href="' +
				constant.CMS_URL + //for local testing
				// constant.API_URL +
				// "app/auth/reset-password-page/" +
				'/reset-password/admin/' +
				findAdmin.id +
				'_' +
				helper.generateRandomString(8) +
				'~' + //using tilde a its is the only character left which does not need encoding and .,-,_ are used in jwttoken
				token +
				'" >clicking here</a>' +
				'.</p>' +
				'<p>For your security, this link expires in 2 days.</p><p>#MyTab</p></div>';

			mail.sendmail(res, findAdmin.email, subject, mailbody);
			delete findAdmin.dataValues.otp_token;
			return 1;
		} else {
			return 0;
		}
	},
	async verifyToken(req) {
		try {
			const jwtVerify = JwtToken.verify(
				req.body.token,
				constant.JWTTOKEN.secret
			);

			if (jwtVerify) {
				const findToken = await Admin.findOne({
					where: {
						otp_token: req.body.token
					}
				});

				if (findToken) {
					return 1; // token valid
				}
			}
			return 0; // token invalid
		} catch (error) {
			return 0;
		}
	},
	//reset password
	async resetPasswsord(req) {
		try {
			let otp_token = req.body.otp_token;

			const JwtDetails = jwtDecode(otp_token);

			let admin = await Admin.findOne({
				where: {
					email: JwtDetails.email,
					otp_token: otp_token,
					deletedAt: null
				}
			});

			if (admin) {
				const generatedSalt = Bcryptjs.genSaltSync(10);
				let encryptedPassword = await Bcryptjs.hash(
					req.body.password,
					generatedSalt
				);

				const updatedAdmin = await Admin.update(
					{ password: encryptedPassword, otp_token: null },
					{
						where: {
							email: admin.email
						}
					}
				);

				return updatedAdmin;
			}

			return 0; // invalid user
		} catch (error) {
			return error;
		}
	},
	/* Update Profile */
	async updateProfile(req) {
		let adminId = req.admin_id;

		let findAdmin = await Admin.findOne({
			where: {
				id: adminId,
				deletedAt: null
			}
		});
		if (!findAdmin) {
			return 0; // user not found
		}

		if (req.file) {
			// image presernt

			if (findAdmin.profile_image) {
				// delete old image
				await imageDelete(findAdmin.profile_image.split('.com/')[1]);
			}

			await imageUpload(
				req.file,

				constant.AWSS3ADMINFOLDER + req.file.originalname
			);
		}
		await Admin.update(
			{
				first_name: req.body.first_name,
				last_name: req.body.last_name,
				profile_image: req.file?.originalname
			},
			{
				where: {
					id: adminId
				}
			}
		);
		//updated admin
		return await Admin.findOne({
			where: {
				id: adminId
			},
			attributes: ['profile_image', 'first_name', 'last_name', 'email']
		});
	},
	//get profile
	async getProfileDetails(req) {
		try {
			let adminId = req.admin_id;
			let findAdmin = await Admin.findOne({
				where: {
					id: adminId,
					deletedAt: null
				},

				attributes: ['first_name', 'last_name', 'email', 'profile_image']
			});

			return findAdmin;
		} catch (error) {
			console.log(error);
		}
	}
};
