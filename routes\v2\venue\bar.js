var express = require('express');
var router = express.Router();

const Authorization =
	require('../../../middleware/venueAuth').venueAuthorization;
const IsVenueConnectedWithBar =
	require('../../../middleware/venueAuth').isVenueUserConnectedWithBar;

const BarController = require('../../../controllers/v2/venue/barController');

router.post(
	'/getAccountDeleteForm',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getAccountDeleteForm
);
router.get('/getProductTax', Authorization, BarController.getProductTax);

router.post(
	'/getSubHeadingWaitTime',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getSubHeadingWaitTime
);

router.post(
	'/updateSubHeadingWaitTime',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updateSubHeadingWaitTime
);

module.exports = router;
