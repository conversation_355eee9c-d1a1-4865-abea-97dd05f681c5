"use strict";

const constant = require("../../config/constant");
//const helper = require("../helper/generalHelper");
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class venue_user_bar extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      venue_user_bar.belongsTo(models.bar, { foreignKey: 'bar_id' });
    }
  }
  venue_user_bar.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      user_id: {
        type: DataTypes.INTEGER(12),
        allowNull: false,
        references: {
          model: "venue_user",
          key: "id",
        },
      },
      bar_id: {
        type: DataTypes.INTEGER(12),
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
    },
    {
      sequelize,
      paranoid: true,
      underscored: true,
      freezeTableName: true,
      modelName: "venue_user_bar",
      timestamps: true,
    }
  );
  return venue_user_bar;
};
