APPNAME="MyTab Venue CMS"
APPCOLOR=#58585a
PORT=6823
CMS_URL=https://manage.mytabinfo.com

DB_CONNECTION=mysql
DB_HOST=mytab.c3ky4aqyslgw.ap-southeast-2.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=mytab
DB_USERNAME=root
DB_PASSWORD=']*HZsA8Y_FZyjfgF6sn8Aax9LaoL'

NODE_ENV='master'
SHOULD_RUN_ON_HTTP=true
PUSHIOSPRODUCTION=true
FCM_FILE='push_android.json'

AWS_SIGNED_URL_EXPIRE_TIME=600
S3_BUCKET_NAME=cms-mytab-live-private
S3_PUBLIC_BUCKET_NAME=cms-mytab-live-public
S3_AWS_URL=https://cms-mytab-live-private.s3-ap-southeast-2.amazonaws.com/
S3_PUBLIC_AWS_URL=https://d145wuyo0abrjc.cloudfront.net/
S3_PRIVATE_AWS_URL=https://d6rwyh6mo3l8h.cloudfront.net/
S3_ACCESSKEY=********************
S3_SECRETKEY='rExidjXXYxk1patbidpG/JBZOsqDdf2lVfnIgQC2'
S3_REGION=ap-southeast-2
S3_AWS_PROJECT_FOLDER=new-cms/
S3_AWS_ARN=arn:aws:iam::693944493302:role/mytab-s3-access-role

MAIL_FROM_AUTH=<EMAIL>
MAIL_FROM=********************
MAIL_PASSWORD=BCe3v5oKpT27eY096385W4VCrH6IoBYlBBdSwqMI8S3y
MAIL_SERVICE=gmail
MAIL_HOST=email-smtp.ap-southeast-2.amazonaws.com
MAIL_PORT=587
MAIL_METHOD=SMTP
MAIL_SECURE=true
BAREMAILTO=<EMAIL>

LOGOURL=https://d145wuyo0abrjc.cloudfront.net/MyTab_Logo_new.png

STRIPE_SECRET_KEY='***********************************************************************************************************'
STRIPE_PUBLISHABLE_KEY='pk_live_51HMNwxJPU0uRL2zZkWcRhweTROMkVecNJwNXD1o8WdQoN2z7c72rKwzUpZHzKPv4hbJmI2Ja3TFN1aoB7iuKzt7c00B4oBSNMw',
STRIPE_POS_PAYMENT_ID='price_1NTgl9JPU0uRL2zZ87bTnWx2'

DOSHI_APP_BASE_URL='https://live.doshii.co/partner/v3/'
DOSHI_APP_CLIENT_ID="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJkb3NoaWkiLCJzdWIiOnsiZm9yIjoiQXBwQ2xpZW50SWQiLCJpZCI6bnVsbH0sImV4cCI6MTcwODEzODE1NX0.I3FuEvqrm0H0rvsRBFzLSNTQLYEM2s6wH0tLcPbFbrE"
DOSHI_APP_CLIENT_SECRET="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJkb3NoaWkiLCJzdWIiOnsiZm9yIjoiQXBwQ2xpZW50U2VjcmV0IiwiaWQiOm51bGx9LCJleHAiOjE3MDgxMzgxNTV9.k7UXXf3VRDKxyabkQp-NI-ngxCw7cr4b0KD-sfQqKhM"
