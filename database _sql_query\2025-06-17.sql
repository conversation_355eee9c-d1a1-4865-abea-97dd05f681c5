CREATE TABLE IF NOT EXISTS `admin_login_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `device_token` varchar(255) DEFAULT NULL,
  `access_token` varchar(512) NOT NULL,
  `device_type` varchar(255) DEFAULT NULL,
  `device_name` varchar(255) DEFAULT NULL,
  `device_location` varchar(255) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `admin_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `applied_discounts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `userID` int NOT NULL,
  `barID` int NOT NULL,
  `discountID` int NOT NULL,
  `discountType` enum('percentage','fixed') NOT NULL,
  `discountCode` varchar(255) NOT NULL,
  `type` enum('manual','automatic') NOT NULL,
  `discountValue` decimal(10,2) NOT NULL,
  `discountAmount` decimal(10,2) NOT NULL,
  `createdAt` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `firebase_crash_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `payload` json DEFAULT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE IF NOT EXISTS `order_discount` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `orderID` int NOT NULL,
  `discountID` int NOT NULL,
  `discountCode` varchar(255) NOT NULL,
  `discountType` enum('percentage','fixed') NOT NULL,
  `type` enum('manual','automatic') NOT NULL,
  `discountValue` decimal(10,2) NOT NULL,
  `discountAmount` decimal(10,2) NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime DEFAULT NULL,
  `userID` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `order_items_bk` (
  `id` int NOT NULL AUTO_INCREMENT,
  `orderID` int NOT NULL,
  `productID` int NOT NULL,
  `price` float NOT NULL,
  `chargeAmount` float NOT NULL,
  `discountedAmount` float DEFAULT NULL,
  `quantity` int NOT NULL,
  `specialRequest` text NOT NULL,
  `isCanceled` enum('Yes','No') NOT NULL DEFAULT 'No',
  `refundedQuantity` int NOT NULL,
  `waitTime` time DEFAULT NULL,
  `orderStatus` enum('New','Preparing','Pickup','Pickedup','NotPickedup','Intoxicated') DEFAULT 'Preparing',
  `PreparingStartTime` datetime DEFAULT NULL,
  `ReadyTime` datetime DEFAULT NULL,
  `PickedupTime` datetime DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `isDeleted` enum('Yes','No') NOT NULL DEFAULT 'No',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ad_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'usd',
  `stripe_payment_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `stripe_customer_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `stripe_card_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_method_type` enum('card','bank_account','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `card_last4` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `card_brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_by_id` int NOT NULL,
  `created_by_type` enum('advertiser','venue','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ad_id` (`ad_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_by` (`created_by_id`,`created_by_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE IF NOT EXISTS `segment_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE IF NOT EXISTS `segment_user_venue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userID` int NOT NULL,
  `barID` int NOT NULL,
  `segmentID` int NOT NULL,
  `convertedDateTime` datetime NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `stripe_customers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `user_type` enum('advertiser','venue','cms') NOT NULL,
  `stripe_customer_id` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `todo_tables` (
  `id` int NOT NULL AUTO_INCREMENT,
  `priority` enum('low','medium','high') DEFAULT NULL,
  `task` text,
  `status` int DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `assigned_to` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `deletedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `assigned_to` (`assigned_to`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

ALTER TABLE `pickup_location` ADD `isDefault` ENUM('0','1') NOT NULL DEFAULT '0' AFTER `status`;
ALTER TABLE `food_options` ADD `listingOrder` INT NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `product` ADD `calorie` VARCHAR(50) NULL DEFAULT NULL AFTER `pickupLocationID`, ADD `fat` VARCHAR(50) NULL DEFAULT NULL AFTER `calorie`, ADD `carbohydrates` VARCHAR(50) NULL DEFAULT NULL AFTER `fat`, ADD `protein` VARCHAR(50) NULL DEFAULT NULL AFTER `carbohydrates`;
ALTER TABLE `venue_user_accesstoken` ADD `loginType` ENUM('NORMAL','GOOGLE','APPLE','FACEBOOK') NOT NULL DEFAULT 'NORMAL' AFTER `access_token`;
