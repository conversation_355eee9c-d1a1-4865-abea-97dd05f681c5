"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class tax extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      tax.belongsTo(models.bar, { foreignKey: "barID" });
      models.bar.hasMany(tax, { foreignKey: "barID" });

      // define association here
    }
  }
  tax.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      barID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
      name: DataTypes.TEXT,
      percentage: DataTypes.FLOAT,
      status: {
        type: DataTypes.ENUM("Active", "Inactive"),
        defaultValue: "Active",
      },
      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },
    {
      sequelize,
      modelName: "tax",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return tax;
};
