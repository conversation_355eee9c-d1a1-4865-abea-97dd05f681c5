const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;
const upsellService = require('../../../services/v2/venue/upsellService');
const upsellValidation = require('../../../validations/v2/venue/upsellValidation');

module.exports = {
	async addVenueSubCategoryUpsell(req, res) {
		try {
			const valid =
				await upsellValidation.addOrUpdateUpsellSubCategoriesValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await upsellService.addVenueSubCategoryUpsell(req, res);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data.data,
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async updateVenueSubCategoryUpsell(req, res) {
		try {
			const valid =
				await upsellValidation.addOrUpdateUpsellSubCategoriesValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await upsellService.updateVenueSubCategoryUpsell(req, res);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async deleteVenueSubCategoryUpsell(req, res) {
		try {
			const valid = await upsellValidation.deleteUpsellSubCategoriesValidation(
				req
			);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await upsellService.deleteVenueSubCategoryUpsell(req, res);

			if (data.code) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					data.message,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getVenueSubCategoryUpsell(req, res) {
		try {
			const valid = await upsellValidation.getUpsellSubCategoriesValidation(
				req
			);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await upsellService.getVenueSubCategoryUpsell(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
