const Joi = require('joi');

module.exports = {
	async addCampaingValidation(req) {
		const schema = Joi.object({
			type: Joi.string().valid('venue', 'advertiser').required().messages({
				'string.base': 'Type must be a string',
				'any.only': 'Type must be either venue or advertiser',
				'any.required': 'Type is required'
			}),
			name: Joi.string().required().messages({
				'any.required': 'Please enter your name.',
				'string.empty': 'Please enter your name.'
			}),
			created_by_type: Joi.string().optional().valid('venue', 'advertiser', 'admin'),			
			created_by_id: Joi.number().required().messages({
				'any.required': 'Id is required.',
				'number.empty': 'Id is required.'
			}),
		});

		return schema.validate(req.body);
	},
	async getCampaignValidation(req) {
		const schema = Joi.object({
			type: Joi.string().valid('venue', 'advertiser').required().messages({
				'string.base': 'Type must be a string',
				'any.only': 'Type must be either venue or advertiser',
				'any.required': 'Type is required'
			}),
			created_by_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Created By ID must be a number',
				'number.integer': 'Created By ID must be an integer',
				'number.positive': 'Created By ID must be positive',
				'any.required': 'Created By ID is required'
			}),
			page: Joi.number().integer().min(1).messages({
				'number.base': 'Page must be a number',
				'number.integer': 'Page must be an integer',
				'number.min': 'Page must be at least 1'
			}),
			limit: Joi.number().integer().min(1).messages({
				'number.base': 'Limit must be a number',
				'number.integer': 'Limit must be an integer',
				'number.min': 'Limit must be at least 1'
			}),
			search: Joi.string().optional().allow(''),
			sort_by: Joi.string()
				.valid(
					'newest',
					'oldest',
					'alphabeticAsc',
					'alphabeticDesc',
					'hightolowSpend',
					'lowtohighSpend',
				)
				.default('newest')
				.messages({
					'string.base': 'Sort by must be a string',
					'any.only':
						'Sort by must be one of newest, oldest, alphabeticAsc, alphabeticDesc'
				}),
			otherwise: Joi.alternatives().try(Joi.string(), Joi.valid(null))
		});
		return schema.validate(req.body);
	},
	async getAllCampaignValidation(req) {
		const schema = Joi.object({
			search: Joi.string().optional().allow(''),
		});
		return schema.validate(req.body);
	},
};
