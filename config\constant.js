require('dotenv').config();

/** express-mailer configurations */
const MAIL_FROM = process.env.MAIL_FROM;
const APP_NAME = process.env.APPNAME;
const MAIL_FROM_AUTH = process.env.MAIL_FROM_AUTH;
const MAIL_PASSWORD = process.env.MAIL_PASSWORD;
const MAIL_SERVICE = process.env.MAIL_SERVICE;
const MAIL_HOST = process.env.MAIL_HOST;
const MAIL_PORT = process.env.MAIL_PORT;
const MAIL_METHOD = process.env.MAIL_METHOD;
const MAIL_SECURE = true;
const AWSS3ARN = process.env.S3_AWS_ARN;
const AWSS3URL = process.env.S3_AWS_URL;
const AWSS3PUBLICURL = process.env.S3_PUBLIC_AWS_URL;
const AWSS3PRIVATEURL = process.env.S3_PRIVATE_AWS_URL;
const AWSS3PROJECTFOLDER = process.env.S3_AWS_PROJECT_FOLDER;
const AWSBARFOLDER = 'mytab/bar/original/';
const AWSORIGINALFOLDER = 'original/';
const AWSS3VENUEFOLDER = 'venue/';
const AWSS3VENUEUSERFOLDER = 'mytab/venue_user/';
const AWSS3ADMINFOLDER = 'mytab/admin/original/';
const AWSS3ADMINQRCODEFOLDER = 'mytab/qr_code_admin/';
const AWSS3VENUEQRCODEFOLDER = 'mytab/qr_code_venue/';
const AWSS3ADVERTISERQRCODEFOLDER = 'mytab/qr_code_advertiser/';
const AWSS3ADVERTISERFOLDER = 'mytab/advertiser/';
const AWSS3PRODUCTFOLDER = 'mytab/product/original/';
const BAREMAILTO = process.env.BAREMAILTO;
const PUSHIOSBUNDLEIDS = 'com.mytab.customer';
const PUSHIOSKEYID = '778HQ6BCC2';
const PUSHIOSTEAMID = '9YB3SM7F58';
const PUSHIOSPRODUCTION = process.env.PUSHIOSPRODUCTION;
const FCM_FILE = process.env.FCM_FILE;
const AWS_SECRET_ID = 'stage/rds/mytab_staging'
/** Use for JWT Authentication */
const JWTTOKEN = {
	algo: 'HS256',
	secret: 'itstopsecret',
	expiresIn: '4h'
};
const JWTAPPTOKEN = {
	algo: 'HS256',
	secret: 'itstopsecret'
};
const JWTREQUESTTOKEN = {
	algo: 'HS256',
	secret: 'itstopsecretrequest',
	expiresIn: '24h'
};

const ENCRYPTKEY = 'itstopsecretrequest';

module.exports = {
	APP_NAME: APP_NAME,
	MAIL_FROM: MAIL_FROM,
	MAIL_FROM_AUTH: MAIL_FROM_AUTH,
	MAIL_PASSWORD: MAIL_PASSWORD,
	MAIL_SERVICE: MAIL_SERVICE,
	MAIL_HOST: MAIL_HOST,
	MAIL_PORT: MAIL_PORT,
	MAIL_METHOD: MAIL_METHOD,
	MAIL_SECURE: MAIL_SECURE,
	PUSHIOSBUNDLEIDS: PUSHIOSBUNDLEIDS,
	PUSHIOSKEYID: PUSHIOSKEYID,
	PUSHIOSTEAMID: PUSHIOSTEAMID,
	PUSHIOSPRODUCTION: PUSHIOSPRODUCTION,
	FCM_FILE: FCM_FILE,
	AWS_SECRET_ID: AWS_SECRET_ID,
	BAREMAILTO: BAREMAILTO,
	CMS_URL: process.env.CMS_URL,
	JWTTOKEN: JWTTOKEN,
	JWTAPPTOKEN: JWTAPPTOKEN,
	JWTREQUESTTOKEN: JWTREQUESTTOKEN,
	LIMIT: 10,
	AWSS3ARN: AWSS3ARN,
	AWSS3URL: AWSS3URL,
    AWSS3ADVERTISERFOLDER: AWSS3ADVERTISERFOLDER,
	AWSS3PUBLICURL: AWSS3PUBLICURL,
	AWSS3PRIVATEURL: AWSS3PRIVATEURL,
	AWSS3PROJECTFOLDER: AWSS3PROJECTFOLDER,
	AWSS3VENUEUSERFOLDER: AWSS3VENUEUSERFOLDER,
	AWSS3ADMINFOLDER: AWSS3ADMINFOLDER,
	AWSS3ADMINQRCODEFOLDER: AWSS3ADMINQRCODEFOLDER,
	AWSS3VENUEQRCODEFOLDER: AWSS3VENUEQRCODEFOLDER,
	AWSS3ADVERTISERQRCODEFOLDER: AWSS3ADVERTISERQRCODEFOLDER,
	AWSS3PRODUCTFOLDER: AWSS3PRODUCTFOLDER,
	AWSBARFOLDER: AWSBARFOLDER,
	AWSORIGINALFOLDER: AWSORIGINALFOLDER,
	AWSS3VENUEFOLDER: AWSS3VENUEFOLDER,
	TEMPORARY_INCTIVE: 1,
	PERMANENTLY_INACTIVE: 2,
	ACTIVE: true,
	INACTIVE: false,
	ENCRYPTKEY: ENCRYPTKEY,
	INFO: 'info',
	ERROR: 'error',
	REQUEST: 'request',
	APPROVED: 'approved'
};
