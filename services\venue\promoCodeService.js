/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const moment = require('moment');
const speakeasy = require('speakeasy');
const CouponModel = require('../../database/models').coupon;
const PickupLocationModel = require('../../database/models').pickup_location;
// const couponsSubCatIdsModel = require('../../database/models').couponssubcatids;
const SubCategoryModel = require('../../database/models').sub_category;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = {
	/* add */
	async add(req, res) {
		//bar_id name code  description startsAt expiresAt discount_amount subCategoryID

		const barID = req.body.bar_id;

		try {
			// 9. Promo codes - Subheading specific....  Starts

			let subIds = req.body.sub_category_id;
			let arrCouponsSubIds = [];
			// 9. Promo codes - Subheading specific....  Ends
			let couponResult = await CouponModel.findOne({
				where: {
					barID: barID,
					[Op.or]: [{ name: req.body.name }, { code: req.body.code }],
					isDeleted: 'No'
				},
				attributes: ['name', 'code', 'discount_amount', 'startsAt', 'expiresAt']
			});

			if (couponResult) {
				//  else {
				//   res.status(200).send({
				//     success: 0,
				//     message: "Coupon code or name already in use.",
				//   });
				// }
				return 0;
			}
			// if not get data

			if (req.body.discount_amount > 80) {
				// res.status(200).send({
				//   success: 0,
				//   message:
				//     "Your venues promo codes can only be set to a maximum of 80% off due merchant fees. Thank you.",
				// });
				return 1;
			}

			let couponData = await CouponModel.create({
				barID: barID,
				code: req.body.code,
				name: req.body.name,
				description: req.body.description,
				startsAt: req.body.starts_at,
				expiresAt: req.body.expires_at,
				discount_amount: req.body.discount_amount
			});

			//create subcategroy wi1se data
			let couponId = couponData.dataValues.id;
			// 9. Promo codes - Subheading specific....  Starts
			// let ids = [];

			// subIds.map((subId) => {
			// 	ids = {
			// 		couponsID: couponId,
			// 		subCategoryID: subId
			// 	};
			// 	arrCouponsSubIds.push(ids);
			// });

			// let bulkcreate = await couponsSubCatIdsModel.bulkCreate(arrCouponsSubIds);

			return 2;

			//already exist
		} catch (error) {
			console.log('error', error);
			throw error;
		}
	},
	/* list */
	async list(req, res) {
		try {
			let barId = req.body.bar_id;
			const today = moment(new Date()).format('YYYY-MM-DD');
			let data = await CouponModel.findAll({
				where: { barID: barId, isDeleted: 'No' },
				attributes: [
					'id',
					'barID',
					'name',
					'code',
					'discount_amount',
					'description',
					'startsAt',
					'expiresAt',

					//   [
					//     Sequelize.literal(`IF(expiresAt = '0000-00-00', null, expiresAt)`),
					//     "expiresAt",
					//   ],
					'status',
					'createdAt',
					[
						Sequelize.literal(`IF('${today}' >  expiresAt, 1, 0)`),
						'isExpired'

						// Sequelize.literal(
						//   `IF('${today}' > (SELECT IF(expiresAt = '0000-00-00', null, expiresAt)), 1, 0)`
						// ),
						// "isExpired",
					]
				],
				// 9. Promo codes - Subheading specific....  Starts
				// include: [
				// 	{
				// 		model: couponsSubCatIdsModel,
				// 		attributes: ['id', 'subCategoryID'],
				// 		include: [
				// 			{
				// 				model: SubCategoryModel,
				// 				attributes: ['name']
				// 			}
				// 		]
				// 	}
				// ],
				// 9. Promo codes - Subheading specific....  Ends
				order: [[Sequelize.literal('isExpired', 'ASC')], ['id', 'DESC']]
			});
			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* edit */
	async edit(req, res) {
		try {
			const barID = req.body.bar_id;
			let couponId = req.body.id;

			// 9. Promo codes - Subheading specific....  Starts
			var subIds = req.body.sub_category_id;
			var arrCouponsSubIds = [];
			// 9. Promo codes - Subheading specific....  Ends
			let couponResult = await CouponModel.findOne({
				where: { barID: barID, isDeleted: 'No', id: couponId },
				attributes: [
					'id',
					'name',
					'code',
					'description',
					'discount_amount',
					'startsAt',
					'expiresAt',
					'status'
				]
			});

			//coupon not found
			if (!couponResult) {
				return 0;
			}

			if (req.body.code) {
				const couponRecords = await CouponModel.findOne({
					attributes: ['*'],
					where: {
						barID: barID,
						isDeleted: 'No',
						id: { [Op.not]: couponId },
						[Op.or]: { name: req.body.name, code: req.body.code }
					}
				});
				//coupon already exist
				if (couponRecords) {
					return 1;
				}
			}

			if (req.body.discount_amount > 80.0) {
				return 2;
			}
			var updateDateObj = {};
			updateDateObj.barID = barID;
			updateDateObj.code = req.body.code ? req.body.code : couponResult.code;
			updateDateObj.name = req.body.name ? req.body.name : couponResult.name;
			updateDateObj.description = req.body.description
				? req.body.description
				: couponResult.description;
			updateDateObj.startsAt = req.body.startsAt
				? req.body.startsAt
				: couponResult.startsAt;
			updateDateObj.expiresAt = req.body.expiresAt
				? req.body.expiresAt
				: couponResult.expiresAt;
			updateDateObj.discount_amount = req.body.discount_amount
				? req.body.discount_amount
				: couponResult.discount_amount;
			updateDateObj.status = req.body.status
				? req.body.status
				: couponResult.status;

			//update coupon
			let update = await CouponModel.update(
				{
					barID: barID,
					code: req.body.code,
					name: req.body.name,
					description: req.body.description,
					startsAt: req.body.starts_at,
					expiresAt: req.body.expires_at,
					discount_amount: req.body.discount_amount,
					status: req.body.status
				},
				{ where: { id: couponId } }
			);

			//distroy privious subcategory
			// let distroy = await couponsSubCatIdsModel.destroy({
			// 	where: { couponsID: couponId }
			// });

			// var ids = [];
			// for (const ele of subIds) {
			// 	ids = {
			// 		couponsID: couponId,
			// 		subCategoryID: ele
			// 	};
			// 	arrCouponsSubIds.push(ids);
			// }
			// let bulkCreate = await couponsSubCatIdsModel.bulkCreate(arrCouponsSubIds);

			// 9. Promo codes - Subheading specific....  Ends

			let updateData = await CouponModel.findOne({
				attributes: [
					'id',
					'barID',
					'name',
					'code',
					'description',
					'discount_amount',
					'startsAt',
					'expiresAt',
					'status'
				],
				where: {
					barID: barID,
					isDeleted: 'No',
					id: couponId
				}
				// 9. Promo codes - Subheading specific....  Starts
				// include: [
				// 	{
				// 		model: couponsSubCatIdsModel,
				// 		attributes: ['id', 'subCategoryID'],
				// 		include: [
				// 			{
				// 				model: SubCategoryModel,
				// 				attributes: ['name']
				// 			}
				// 		]
				// 	}
				// ]
				// 9. Promo codes - Subheading specific....  Ends
			});

			return updateData;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	},
	/* delete */
	async delete(req, res) {
		try {
			let couponId = req.body.id;

			let deleted = await CouponModel.update(
				{ isDeleted: 'Yes' },
				{ where: { id: couponId, barID: req.body.bar_id } }
			);

			// let deler = await couponsSubCatIdsModel.destroy({
			// 	where: { couponsID: couponId }
			// }); // 9. Promo codes - Subheading specific....

			return 1;
		} catch (error) {
			console.log('err', error);
			throw error;
		}
	}
};
