"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class product_food_options extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // pos_conf.belongsTo(models.admin, {
      //   foreignKey: "assigned_to",
      //   targetKey: "id",
      //   onDelete: "CASCADE",
      // });
      product_food_options.belongsTo(models.food_options, {
        foreignKey: "foodOptionID",
        as: "foodOptions",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  product_food_options.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      foodOptionID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "food_options",
          key: "id",
        },
      },
      productID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "product",
          key: "id",
        },
      },
      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },

    {
      sequelize,
      modelName: "product_food_options",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return product_food_options;
};
