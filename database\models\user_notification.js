'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class user_notification extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// user_notification.belongsTo(models.order, { foreignKey: 'orderID' });
			// user_notification.belongsTo(models.user, { foreignKey: 'userID' });
			// user_notification.belongsTo(models.bar, { foreignKey: 'barID' });
		}
	}
	user_notification.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			dataID: DataTypes.INTEGER,
			notification_type: DataTypes.TEXT,
			message: DataTypes.TEXT,
			status: DataTypes.ENUM('seen', 'unseen'),
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No')
		},
		{
			sequelize,
			paranoid: true,
			freezeTableName: true,
			modelName: 'user_notification',
			timestamps: true
		}
	);
	return user_notification;
};
