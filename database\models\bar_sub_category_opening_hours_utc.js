'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class bar_sub_category_opening_hours_utc extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			bar_sub_category_opening_hours_utc.belongsTo(models.bar, {
				foreignKey: 'barID',
				targetKey: 'id'
			});
			bar_sub_category_opening_hours_utc.belongsTo(
				models.bar_sub_category_opening_hours,
				{
					foreignKey: 'barSubCategoryOpeningHoursID',
					targetKey: 'id'
				}
			);
		}
	}
	bar_sub_category_opening_hours_utc.init(
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true
			},
			barSubCategoryOpeningHoursID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar_sub_category_opening_hours',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			weekDay: { type: DataTypes.INTEGER, defaultValue: '0' },
			openingHours: DataTypes.TIME,
			closingHours: DataTypes.TIME,
			isClosed: { type: DataTypes.BOOLEAN, defaultValue: '0' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'bar_sub_category_opening_hours_utc',
			timestamps: true,
			freezeTableName: true
		}
	);
	return bar_sub_category_opening_hours_utc;
};
