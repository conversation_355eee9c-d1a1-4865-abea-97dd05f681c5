var express = require('express');
var router = express.Router();

/* require for Authentication */
const Authorization = require('../../middleware/venueAuth').venueAuthorization;
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;
const IsVenueUpdatableController =
	require('../../middleware/venueAuth').isVenueUpdatableController;

const MulterMiddleware = require('../../middleware/multer');

/*require for security */
const BarController = require('../../controllers/venue/barController');

/* get venue operating API */
router.post(
	'/getVenueOperatingHours',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getVenueOperatingHours
);

/*  update venue oprating api */
router.post(
	'/addVenueOperatingHours',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.addVenueOperatingHours
);

/* connect Venue api */
router.post('/connectVenue', Authorization, BarController.connectVenue);

/*Otp verify */
router.post('/venue-otp-verify', Authorization, BarController.venueOtpVerify);

/* get Venue Details */
router.post(
	'/getVenueDetails',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getVenueDetails
);

router.post(
	'/send-venue-otp',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.sendVenueOtp
);

/*Otp verify */
router.post(
	'/venue-otp-verify-only',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.venueOtpVerifyOnly
);

/*update venue details */
router.put(
	'/updateVenueDetails',
	MulterMiddleware.singleProfilePic,
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updateVenueDetails
);

/*update venue mobile */
router.post(
	'/updateVenueMobile',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updateVenueMobile
);

/*create venue api */
router.post(
	'/createVenue',
	MulterMiddleware.singleProfilePic,
	Authorization,
	BarController.createVenue
);
/*Contact us */
router.post('/contactUs', Authorization, BarController.contactUs);
/*Delete venue association */
router.post(
	'/delete',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.delete
);
/*Delete venue association */
router.delete(
	'/deleteVenue',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.deleteVenue
);
/* get service type API */
router.post(
	'/getServiceType',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getServiceType
);
/* update service type API */
router.post(
	'/updateServiceType',
	Authorization,
	IsVenueConnectedWithBar,
	IsVenueUpdatableController,
	BarController.updateServiceType
);
/* get wait time service type API */
router.post(
	'/getWaitTimeServiceType',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getWaitTimeServiceType
);
/* update wait time service type API */
router.post(
	'/updateWaitTimeServiceType',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updateWaitTimeServiceType
);

// Wait time for specific hour and sub-heading
router.post(
	'/getSubHeadingWaitTime',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.getSubHeadingWaitTime
);

router.post(
	'/updateSubHeadingWaitTime',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updateSubHeadingWaitTime
);

/* Change Password */
router.post(
	'/change-password',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.changePassword
);

/* Change PassCode */
router.post(
	'/change-passCode',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.changePassCode
);

router.post(
	'/updatePassCodeStatus',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.updatePassCodeStatus
);

router.post(
	'/setPassCode',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.setPassCode
);

router.post(
	'/verifyPassCode',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.verifyPassCode
);

/* verify venue before create */
router.post('/sent-otp-verify', Authorization, BarController.venueOtpSent);

/*Otp verify */
router.post(
	'/create-venue-otp-verify',
	Authorization,
	BarController.createVenueOtpVerify
);

router.post(
	'/check-stripe-integration',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.checkStripeIntegration
);

router.post(
	'/complete-stripe-integration',
	Authorization,
	IsVenueConnectedWithBar,
	BarController.completeStripeIntegration
);

router.get("/getTimezones", Authorization, BarController.getTimezones);

module.exports = router;
