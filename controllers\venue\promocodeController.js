const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const promoCodeService = require('../../services/venue/promoCodeService');
const promoCodeValidation = require('../../validations/venue/promocodeValidation');

module.exports = {
	/* add Promocode */
	async add(req, res) {
		try {
			// validation
			const valid = await promoCodeValidation.add(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await promoCodeService.add(req, res);

			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.COUPONCODEALREADY,
					status.ERROR
				);
			} else if (data == 1) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.MAXLIMITREACHED,
					status.ERROR
				);
			} else if (data == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PROMOCODEADDEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*list promocode */
	async list(req, res) {
		try {
			// validation
			const valid = await promoCodeValidation.list(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await promoCodeService.list(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LOCATIONSFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*edit Promocode */
	async edit(req, res) {
		try {
			// validation
			const valid = await promoCodeValidation.edit(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let data = await promoCodeService.edit(req, res);

			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.COUPONNOTFOUND,
					status.ERROR
				);
			} else if (data == 1) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.COUPONCODEALREADY,
					status.ERROR
				);
			} else if (data == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.MAXLIMITREACHED,
					status.ERROR
				);
			} else if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.UPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*delete  promocode*/
	async delete(req, res) {
		try {
			// validation
			const valid = await promoCodeValidation.delete(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await promoCodeService.delete(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.COUPONDELETED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
