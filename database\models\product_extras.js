"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class product_extras extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // pos_conf.belongsTo(models.admin, {
      //   foreignKey: "assigned_to",
      //   targetKey: "id",
      //   onDelete: "CASCADE",
      // });
    }
  }
  product_extras.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      productID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "product",
          key: "id",
        },
      },
      extraItem: DataTypes.STRING(255),
      price: DataTypes.FLOAT,
      posID: DataTypes.STRING(50),
      productOptionposID: DataTypes.STRING(50),
      productOptionName: DataTypes.STRING(255),
      extraSequence: DataTypes.INTEGER,
      status: {
        type: DataTypes.ENUM("Active", "Inactive"),
        defaultValue: "Active",
      },
      isDeleted: { type: DataTypes.ENUM("Yes", "No"), defaultValue: "No" },
      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },
    {
      sequelize,
      modelName: "product_extras",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return product_extras;
};
