"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class venue_user_permission extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  venue_user_permission.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      permission: DataTypes.STRING(50),
    },
    {
      sequelize,
      modelName: "venue_user_permission",
      timestamps: true,
      paranoid: true,
      underscored: true,
    }
  );
  return venue_user_permission;
};
