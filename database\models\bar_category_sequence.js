"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class bar_category_sequence extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      bar_category_sequence.belongsTo(models.sub_category, {
        foreignKey: "subCategoryId",
      });
      models.sub_category.hasMany(bar_category_sequence, {
        foreignKey: "subCategoryId",
        as: "bar_category_sequence",
      });

      bar_category_sequence.belongsTo(models.bar, { foreignKey: "barId" });
    }
  }
  bar_category_sequence.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      barId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
      subCategoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "sub_category",
          key: "id",
        },
      },
      subCategorySequence: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: "bar_category_sequence",
      freezeTableName: true,
      timestamps: false,
    }
  );
  return bar_category_sequence;
};
