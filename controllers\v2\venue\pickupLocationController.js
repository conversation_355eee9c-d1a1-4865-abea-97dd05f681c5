const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;
const pickupLocationService = require('../../../services/v2/venue/pickupLocationService');
const pickupLocationValidation = require('../../../validations/v2/venue/pickupLocationValidation');

module.exports = {
	async add(req, res) {
		try {
			const valid = await pickupLocationValidation.add(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.add(req, res);
			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LOCATIONADDED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async list(req, res) {
		try {
			const valid = await pickupLocationValidation.list(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.list(req, res);
			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LOCATIONSFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async edit(req, res) {
		try {
			const valid = await pickupLocationValidation.edit(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.edit(req, res);
			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LOCATIONUPDATED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async delete(req, res) {
		try {
			const valid = await pickupLocationValidation.delete(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.delete(req, res);
			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOLOCATIONFOUND,
					status.ERROR
				);
			} else if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.LOCATIONDELETED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);

			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async createDefaultPickupLocationForAllVenues(req, res) {
		try {
			let data =
				await pickupLocationService.createDefaultPickupLocationForAllVenues(
					req,
					res
				);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					'Default pickup locations created for all venues.',
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
