const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const { ChimeSDKMediaPipelines } = require('aws-sdk');
const productService = require('../../services/venue/productService');
const productValidation = require('../../validations/venue/productValidation');

module.exports = {
	/* add product */
	async addProduct(req, res) {
		try {
			// validation
			const valid = await productValidation.addProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let addedProduct = await productService.addProduct(req, res);
			if (addedProduct == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTIMAGEREQUIRED,
					status.ERROR
				);
			} else if (addedProduct === 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.TAGIDNOTFOUND,
					status.ERROR
				);
			} else if (addedProduct) {
				return response(
					res,
					status.SUCCESSSTATUS,
					addedProduct,
					message.MENUITEMADDED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* delete product */
	async deleteProduct(req, res) {
		try {
			// validation
			const valid = await productValidation.deleteProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let deletedProduct = await productService.deleteProduct(req, res);

			if (deletedProduct) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTDELETED,
					status.SUCCESS
				);
			} else if (deletedProduct == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTNOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* change Status */
	async chageStatusProduct(req, res) {
		try {
			// validation
			const valid = await productValidation.changeStatusProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updatedProduct = await productService.chageStatusProduct(req, res);

			if (updatedProduct == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTNOTFOUND,
					status.ERROR
				);
			} else if (updatedProduct == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.UPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* change get product options list */
	async getFoodOptionList(req, res) {
		try {
			let listoffoodoption = await productService.getFoodOptionList(req, res);

			if (listoffoodoption) {
				return response(
					res,
					status.SUCCESSSTATUS,
					listoffoodoption,
					message.FOODOPTIONLIST,
					status.SUCCESS
				);
			} else {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async editProduct(req, res) {
		try {
			// validation
			const valid = await productValidation.editProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let updatedProduct = await productService.editProduct(req, res);

			if (updatedProduct == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTNOTFOUND,
					status.ERROR
				);
			} else if (updatedProduct === 3) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.TAGIDNOTFOUND,
					status.ERROR
				);
			} else if (updatedProduct == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEISOPENCANNOTUPDATE,
					status.ERROR
				);
			} else if (updatedProduct) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.MENUITEAMUPDATED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			console.log('errr', error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	/* get product list */
	async getProductList(req, res) {
		try {
			// validation
			const valid = await productValidation.listProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let productList = await productService.listProduct(req, res);

			if (productList) {
				return response(
					res,
					status.SUCCESSSTATUS,
					productList,
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getProductListV2(req, res) {
		try {
			const valid = await productValidation.listProductValidationV2(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let productList = await productService.listProductV2(req, res);

			if (productList.totalCount) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{
						totalCount: productList.totalCount,
						count: productList.products.length,
						products: productList.products
					},
					message.LISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				error.message || message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	/* delete product varient*/
	async deleteProductVariant(req, res) {
		try {
			// validation
			const valid = await productValidation.deleteProductVariantValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let isProductDelete = await productService.deleteProductVariant(req, res);

			if (isProductDelete == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTVARIANTNOTEXIST,
					status.ERROR
				);
			} else if (isProductDelete == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTVARIANTDELETEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* delete product varient*/
	async deleteProductExtras(req, res) {
		try {
			// validation
			const valid = await productValidation.deleteProductExtrasValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let isProductExtraDeleted = await productService.deleteProductExtras(
				req,
				res
			);

			if (isProductExtraDeleted == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTEXTRANOTEXIST,
					status.ERROR
				);
			} else if (isProductExtraDeleted == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.DELETEPRODUCTEXTRASSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* get single product */
	async getSingleProduct(req, res) {
		try {
			const valid = await productValidation.getSingleProductValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let productData = await productService.getSingleProduct(req, res);

			if (productData) {
				return response(
					res,
					status.SUCCESSSTATUS,
					productData,
					message.PRODUCTFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else if (productData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTNOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	// Api to get time slot(active hour & inactive hours) of an Item....
	async getItemActiveHours(req, res) {
		try {
			const valid = await productValidation.getItemActiveHoursValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let productData = await productService.getItemActiveHours(req, res);

			if (productData) {
				return response(
					res,
					status.SUCCESSSTATUS,
					productData,
					message.ACTIVEHOURSFETCHED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	// Api to get time slot(active hour & inactive hours) of an Item....
	async addUpdateItemActiveHours(req, res) {
		try {
			const valid = await productValidation.addUpdateItemActiveHoursValidation(
				req
			);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let productData = await productService.addUpdateItemActiveHours(req, res);

			if (productData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INVALIDTIMESLOT,
					status.ERROR
				);
			} else if (productData == 4) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.TIMESLOTADDED,
					status.SUCCESS
				);
			} else if (productData == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.TIMESLOTUPDATED,
					status.SUCCESS
				);
			} else if (productData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.TIMESLOTALREADYEXIST,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async productSequence(req, res) {
		try {
			const valid = await productValidation.productSequenceValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let dataUpdated = await productService.productSequence(req, res);

			if (dataUpdated == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INVALIDSUBCATEGORY,
					status.ERROR
				);
			} else if (dataUpdated == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PRODUCTSEQUENCEUPDATEDSUCESSFULLY,
					status.SUCCESS
				);
			} else if (dataUpdated == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INCORRECTCURRENTPRODUCT,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//list of pickuplocation
	async pickLocationList(req, res) {
		try {
			let list = await productService.pickupLocationList(req, res);

			if (list) {
				return response(
					res,
					status.SUCCESSSTATUS,
					list,
					message.DATAFETCHSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//delete item active hours
	async deleteItemActiveHours(req, res) {
		try {
			let isDeleted = await productService.deleteItemActiveHours(req, res);
			if (isDeleted) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.DELETEITEMHOURSSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
