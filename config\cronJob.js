const Sequelize = require('sequelize');
const moment = require('moment');
const cron = require('node-cron');
const Op = Sequelize.Op;
const commonFunction = require('../common/commonFunction');
const BarModel = require('../database/models').bar; //import modal always Capital
const BarPOSSubscriptionsModel =
	require('../database/models').bar_pos_subscriptions; //import modal always Capital
const Order = require('../database/models').orders;
const UserModel = require('../database/models').user;
const UserConsentVenueModel = require('../database/models').user_consent_venue;
const OrderModel = require('../database/models').orders;
const SegmentUserVenueModel = require('../database/models').segment_user_venue;

const {
	updateOrderInDoshii,
	updateCheckInDoshii,
	updateTransactionInDoshii
} = require('../helper/doshiiHelper');

// every two hours
// cron.schedule('0 */2 * * *', function () {
// 	console.log('checkVenuePOSSubscription run---');
// 	checkVenuePOSSubscription();
// });

// every two minutes
cron.schedule('*/2 * * * *', function () {
	console.log('checkPOSOrderResponse run---');
	checkPOSOrderResponse();
});

// every day at 12:00 AM
cron.schedule('30 21 * * *', function () {
	// removeOldVenueUserAccessToken();
	console.log('segmentUserCron run start---');
	segmentUserCron();
	console.log('segmentUserCron run completed---');
});

async function checkVenuePOSSubscription() {
	try {
		let barDetails = await BarModel.findAll({
			where: {
				posStatus: {
					[Op.not]: '-1'
				}
			}
		});
		barDetails.map(async (bar) => {
			const barSubscription = await BarPOSSubscriptionsModel.findOne({
				where: {
					barID: bar.id
				},
				order: [['id', 'DESC']]
			});
			if (barSubscription) {
				// console.log(
				// 	moment(barSubscription.end_date).utc().add(2, 'days').valueOf() <=
				// 		moment().utc().valueOf(),
				// 	moment(barSubscription.end_date).utc().add(2, 'days'),
				// 	moment().utc()
				// );
				if (
					moment(barSubscription.end_date).utc().add(2, 'days').valueOf() <=
					moment().utc().valueOf()
				) {
					await BarModel.update(
						{
							posStatus: '-1',
							venueId: null,
							posReferralLink: null
						},
						{
							where: {
								id: bar.id
							}
						}
					);
				} else {
					if (
						barSubscription.subscription_status == 'incomplete_expired' ||
						barSubscription.subscription_status == 'unpaid'
					) {
						await BarModel.update(
							{
								posStatus: '-1',
								venueId: null,
								posReferralLink: null
							},
							{
								where: {
									id: bar.id
								}
							}
						);
					}
				}
			} else {
				await BarModel.update(
					{
						posStatus: '-1',
						venueId: null,
						posReferralLink: null
					},
					{
						where: {
							id: bar.id
						}
					}
				);
			}
		});
	} catch (error) {
		console.log(error);
	}
}

async function removeOldVenueUserAccessToken() {
	try {
		let venueUserToken = await VenueUserTokenModel.findAll({
			where: {
				orderDate: {
					[Op.gte]: req.body.startDate,
					[Op.lte]: req.body.startDate
				}
			}
		});
		barDetails.map(async (bar) => {
			const barSubscription = await BarPOSSubscriptionsModel.findOne({
				where: {
					barID: bar.id
				},
				order: [['id', 'DESC']]
			});
			if (barSubscription) {
				// console.log(
				// 	moment(barSubscription.end_date).utc().add(2, 'days').valueOf() <=
				// 		moment().utc().valueOf(),
				// 	moment(barSubscription.end_date).utc().add(2, 'days'),
				// 	moment().utc()
				// );
				if (
					moment(barSubscription.end_date).utc().add(2, 'days').valueOf() <=
					moment().utc().valueOf()
				) {
					await BarModel.update(
						{
							posStatus: '-1',
							venueId: null,
							posReferralLink: null
						},
						{
							where: {
								id: bar.id
							}
						}
					);
					await BarModel.update(
						{
							posStatus: '-1',
							venueId: null,
							posReferralLink: null
						},
						{
							where: {
								id: bar.id
							}
						}
					);
				} else {
					if (
						barSubscription.subscription_status == 'incomplete_expired' ||
						barSubscription.subscription_status == 'unpaid'
					) {
						await BarModel.update(
							{
								posStatus: '-1',
								venueId: null,
								posReferralLink: null
							},
							{
								where: {
									id: bar.id
								}
							}
						);
					}
				}
			} else {
				await BarModel.update(
					{
						posStatus: '-1',
						venueId: null,
						posReferralLink: null
					},
					{
						where: {
							id: bar.id
						}
					}
				);
			}
		});
	} catch (error) {
		console.log(error);
	}
}

async function checkPOSOrderResponse() {
	try {
		let barDetails = await BarModel.findAll({
			where: {
				posStatus: '1'
			},
			include: [
				{
					model: Order,
					where: {
						posOrderStatus: 'Pending',
						posOrderId: {
							[Op.ne]: null
						}
					}
				}
			]
		});
		barDetails.map(async (bar) => {
			if (bar.orders && bar.orders.length > 0) {
				bar.orders.map(async (order) => {
					if (
						moment(order.createdAt).utc().add(2, 'minutes').valueOf() <=
						moment().utc().valueOf()
					) {
						var payload = {
							status: 'cancelled',
							mealPhase: 'ordered',
							version: order.dataValues.posOrderVersion
						};
						await updateOrderInDoshii(
							payload,
							bar.dataValues.venueId,
							order.posOrderId
						);

						var transactionPayload = {
							status: 'cancelled',
							version: order.dataValues.posTransactionVersion
						};
						await updateTransactionInDoshii(
							transactionPayload,
							bar.dataValues.venueId,
							order.posTransactionId
						);
						if (order.posCheckInId != null) {
							var checkInPayload = {
								status: 'cancelled',
								type: 'table'
							};
							await updateCheckInDoshii(
								checkInPayload,
								bar.dataValues.venueId,
								order.posCheckInId
							);
						}
					}
				});
			}
		});
	} catch (error) {
		console.log(error);
	}
}

async function segmentUserCron() {
	try {
		const allUserDataVenueWise = await OrderModel.findAll({
			attributes: [
				'userID',
				'barID',
				[Sequelize.fn('SUM', Sequelize.col('total')), 'totalSpent']
			],
			include: [
				{
					model: UserModel,
					attributes: [],
					required: true
				}
			],
			group: ['userID', 'barID'],
			order: [[Sequelize.fn('SUM', Sequelize.col('total')), 'DESC']]
		});

		const venueIds = Array.from(
			new Set(allUserDataVenueWise.map((record) => record.barID))
		);

		const userSpendsByVenue = allUserDataVenueWise.reduce((acc, record) => {
			if (!acc[record.barID]) {
				acc[record.barID] = [];
			}
			acc[record.barID].push({
				userID: record.userID,
				totalSpent: record.dataValues.totalSpent
			});
			return acc;
		}, {});

		const topSpendersByVenue = {};

		venueIds.forEach((venueId) => {
			const topSpenders = userSpendsByVenue[venueId].map((user) => user.userID);

			topSpendersByVenue[venueId] = {
				top5: topSpenders.slice(0, 5),
				top10: topSpenders.slice(0, 10),
				top20: topSpenders.slice(0, 20),
				top50: topSpenders.slice(0, 50),
				top100: topSpenders.slice(0, 100)
			};
		});

		const allUserData = await OrderModel.findAll({
			attributes: [
				'userID',
				'barID',
				[Sequelize.fn('SUM', Sequelize.col('total')), 'totalSpent'],
				[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '06:00:00' AND '08:59:59' THEN 1 ELSE 0 END`
						)
					),
					'earlyMorning'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '09:00:00' AND '10:59:59' THEN 1 ELSE 0 END`
						)
					),
					'midMorning'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '11:00:00' AND '13:59:59' THEN 1 ELSE 0 END`
						)
					),
					'lunch'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '14:00:00' AND '16:59:59' THEN 1 ELSE 0 END`
						)
					),
					'afternoon'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '17:00:00' AND '20:59:59' THEN 1 ELSE 0 END`
						)
					),
					'evening'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN TIME(createdAt) BETWEEN '21:00:00' AND '23:59:59' THEN 1 ELSE 0 END`
						)
					),
					'lateNight'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN orderServiceType = 'PICKUP' THEN 1 ELSE 0 END`
						)
					),
					'hasPickup'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN orderServiceType = 'TABLE' THEN 1 ELSE 0 END`
						)
					),
					'hasTableService'
				],
				[
					Sequelize.literal(
						`CASE WHEN MAX(CASE WHEN orderServiceType = 'PICKUP' THEN 1 ELSE 0 END) = 1 AND MAX(CASE WHEN orderServiceType = 'TABLE' THEN 1 ELSE 0 END) = 1 THEN 1 ELSE 0 END`
					),
					'hasBoth'
				],
				[
					Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top5.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
					'isTop5Customer'
				],
				[
					Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top10.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
					'isTop10Customer'
				],
				[
					Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top20.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
					'isTop20Customer'
				],
				[
					Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top50.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
					'isTop50Customer'
				],
				[
					Sequelize.literal(`
							CASE WHEN CONCAT(barID, '-', userID) IN (
								${Object.keys(topSpendersByVenue)
									.flatMap((venueId) =>
										topSpendersByVenue[venueId].top100.map(
											(userID) => `'${venueId}-${userID}'`
										)
									)
									.join(',')}
							) THEN 1 ELSE 0 END
						`),
					'isTop100Customer'
				],
				[
					Sequelize.literal(`CASE WHEN SUM(total) < 50 THEN 1 ELSE 0 END`),
					'croissantClub'
				],
				[
					Sequelize.literal(
						`CASE WHEN SUM(total) >= 50 AND SUM(total) < 200 THEN 1 ELSE 0 END`
					),
					'caramelClub'
				],
				[
					Sequelize.literal(`CASE WHEN SUM(total) > 200 THEN 1 ELSE 0 END`),
					'caviarClub'
				],
				[
					Sequelize.literal(
						`CASE WHEN DATE_FORMAT(user.birthday, '%m-%d') = DATE_FORMAT(CURDATE(), '%m-%d') THEN 1 ELSE 0 END`
					),
					'birthdayToday'
				],
				[
					Sequelize.literal(
						`CASE WHEN DATE_FORMAT(user.birthday, '%m-%d') BETWEEN DATE_FORMAT(CURDATE() - INTERVAL (WEEKDAY(CURDATE())) DAY, '%m-%d') AND DATE_FORMAT(CURDATE() + INTERVAL (6 - WEEKDAY(CURDATE())) DAY, '%m-%d') THEN 1 ELSE 0 END`
					),
					'birthdayThisWeek'
				],
				[
					Sequelize.literal(
						`CASE WHEN DATE_FORMAT(user.birthday, '%m') = DATE_FORMAT(CURDATE(), '%m') THEN 1 ELSE 0 END`
					),
					'birthdayThisMonth'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN MONTH(createdAt) IN (12, 1, 2) THEN 1 ELSE 0 END`
						)
					),
					'orderedInSummer'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN MONTH(createdAt) IN (3, 4, 5) THEN 1 ELSE 0 END`
						)
					),
					'orderedInAutumn'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN MONTH(createdAt) IN (6, 7, 8) THEN 1 ELSE 0 END`
						)
					),
					'orderedInWinter'
				],
				[
					Sequelize.fn(
						'MAX',
						Sequelize.literal(
							`CASE WHEN MONTH(createdAt) IN (9, 10, 11) THEN 1 ELSE 0 END`
						)
					),
					'orderedInSpring'
				],
				[
					Sequelize.literal(
						`CASE WHEN MAX(createdAt) >= CURDATE() - INTERVAL 30 DAY THEN 1 ELSE 0 END`
					),
					'newCustomer'
				],
				[
					Sequelize.literal(
						`CASE WHEN MAX(createdAt) >= CURDATE() - INTERVAL 90 DAY AND MAX(createdAt) < CURDATE() - INTERVAL 31 DAY THEN 1 ELSE 0 END`
					),
					'frequentCustomer'
				],
				[
					Sequelize.literal(
						`CASE WHEN MAX(createdAt) < CURDATE() - INTERVAL 90 DAY THEN 1 ELSE 0 END`
					),
					'inactiveCustomer'
				],
				[
					Sequelize.literal(
						`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = orders.barID and user_consent_venue.userID = user.id limit 1) >= 1 THEN 1 ELSE 0 END`
					),
					'hasConsent'
				],
				// Kids Product only: TRUE if user has no items from non-drinks category (tagID != 2)
				[
					Sequelize.literal(`
						CASE
							WHEN EXISTS (
								SELECT 1
								FROM order_items oi
								INNER JOIN segment_product_tags p ON p.productID = oi.productID
								INNER JOIN orders o2 ON o2.id = oi.orderID
								WHERE o2.userID = orders.userID AND o2.barID = orders.barID
								AND p.tagID = 1
							)
							THEN 1 ELSE 0
						END
					`),
					'isKidsProductOnly'
				],
				// Food only: TRUE if user has no items from non-drinks category (tagID != 2)
				[
					Sequelize.literal(`
						CASE
							WHEN EXISTS (
								SELECT 1
								FROM order_items oi
								INNER JOIN segment_product_tags p ON p.productID = oi.productID
								INNER JOIN orders o2 ON o2.id = oi.orderID
								WHERE o2.userID = orders.userID AND o2.barID = orders.barID
								AND p.tagID = 2
							)
							THEN 1 ELSE 0
						END
					`),
					'isFoodOnly'
				],				
				// Drinks only: TRUE if user has no items from non-food category (tagID != 3)
				[
					Sequelize.literal(`
						CASE
							WHEN EXISTS (
								SELECT 1
								FROM order_items oi
								INNER JOIN segment_product_tags p ON p.productID = oi.productID
								INNER JOIN orders o2 ON o2.id = oi.orderID
								WHERE o2.userID = orders.userID AND o2.barID = orders.barID
								AND p.tagID = 3
							)
							THEN 1 ELSE 0
						END
					`),
					'isDrinksOnly'
				],
				// Alcohol only: TRUE if user has no items from non-food category (tagID != 3)
				[
					Sequelize.literal(`
						CASE
							WHEN EXISTS (
								SELECT 1
								FROM order_items oi
								INNER JOIN segment_product_tags p ON p.productID = oi.productID
								INNER JOIN orders o2 ON o2.id = oi.orderID
								WHERE o2.userID = orders.userID AND o2.barID = orders.barID
								AND p.tagID = 4
							)
							THEN 1 ELSE 0
						END
					`),
					'isAlcoholOnly'
				],
				[
					Sequelize.literal(`
					CASE
						WHEN EXISTS (
							SELECT 1
							FROM order_items oi
							INNER JOIN segment_product_tags p ON p.productID = oi.productID
							INNER JOIN orders o2 ON o2.id = oi.orderID
							WHERE o2.userID = orders.userID AND o2.barID = orders.barID
							AND p.tagID = 2
						)
						AND EXISTS (
							SELECT 1
							FROM order_items oi
							INNER JOIN segment_product_tags p ON p.productID = oi.productID
							INNER JOIN orders o2 ON o2.id = oi.orderID
							WHERE o2.userID = orders.userID AND o2.barID = orders.barID
							AND p.tagID = 3
						)
						THEN 1 ELSE 0
					END
					`),
					'hasFoodAndDrinks'
				]
			],
			group: ['userID', 'barID'],
			include: [
				{
					model: UserModel,
					attributes: [
						'fullName',
						'email',
						'countryCode',
						'mobile',
						'birthday',
						[
							Sequelize.literal(
								`CASE WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 18 AND 27 THEN "18 - 27 (Gen Z)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 28 AND 43 THEN "28 - 43 (Millennial)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 44 AND 59 THEN "44 - 59 (Gen X)" WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 60 AND 78 THEN "60 - 78 (Baby Boomer)" ELSE "79 - 99+ (Silent Generation+)" END`
							),
							'age_range'
						]
					],
					required: true
				}
			],
			orderBy: ['userID']
		});

		let createSegment = [];
		let removeSegment = [];
		let removeUser = 0;
		let skipUser = 0;
		let count = 0;

		// Mapping of age ranges to segment IDs
		const segmentIdMap = {
			'18 - 27 (Gen Z)': '7',
			'28 - 43 (Millennial)': '8',
			'44 - 59 (Gen X)': '9',
			'60 - 78 (Baby Boomer)': '10',
			'79 - 99+ (Silent Generation+)': '11'
		};

		let todayDateTime = moment().tz('Australia/Perth');

		// Fetch all user segments in one go
		const userSegmentLists = await SegmentUserVenueModel.findAll({
			attributes: [
				'userID',
				'barID',
				[Sequelize.fn('GROUP_CONCAT', Sequelize.col('segmentID')), 'segmentIDs']
			],
			group: ['userID', 'barID'],
			raw: true
		});

		const userSegmentsMap = new Map(
			userSegmentLists.map((segment) => [
				`${segment.userID}-${segment.barID}`,
				new Set(segment.segmentIDs.split(','))
			])
		);

		const userSegmentPromises = allUserData.map(async (entity) => {
			const key = `${entity.userID}-${entity.barID}`;
			const userSegmentSet = userSegmentsMap.get(key) || new Set();

			if (!entity.user) {
				if (userSegmentSet.size) {
					removeUser++;
					await SegmentUserVenueModel.destroy({
						where: { userID: entity.userID, barID: entity.barID }
					});
				} else {
					skipUser++;
				}
				return; // Exit early for users without a valid entity
			}

			count++;

			const addSegment = (id) => {
				if (!userSegmentSet.has(id)) {
					createSegment.push({
						userID: entity.userID,
						barID: entity.barID,
						segmentID: id,
						convertedDateTime: todayDateTime
					});
				}
			};

			const conditions = [
				{ id: '1', condition: true }, // All customers
				{ id: '3', condition: entity.dataValues.orderCount == 1 }, // Customers who have purchased once
				{
					id: '4',
					condition:
						entity.dataValues.orderCount > 1 &&
						entity.dataValues.orderCount < 10
				}, // Customers who have purchased more than once
				{ id: '5', condition: entity.dataValues.orderCount >= 10 }, // Customers who have purchased more than 10 times
				{
					id: segmentIdMap[entity.user.dataValues.age_range],
					condition: true
				}, // Customer Age Range
				{ id: '13', condition: entity.dataValues.earlyMorning == '1' }, // Customers who purchase early morning (6am - 9am)
				{ id: '14', condition: entity.dataValues.midMorning == '1' }, // Customers who purchase mid morning (9am - 11am)
				{ id: '15', condition: entity.dataValues.lunch == '1' }, // Customers who purchase at lunch (11pm - 2pm)
				{ id: '16', condition: entity.dataValues.afternoon == '1' }, // Customers who purchase in the afternoon (2pm - 5pm)
				{ id: '17', condition: entity.dataValues.evening == '1' }, // Customers who purchase in the evening (5pm - 9pm)
				{ id: '18', condition: entity.dataValues.lateNight == '1' }, // Customers who purchase late night (9pm - 11:59pm)
				{ id: '24', condition: entity.dataValues.hasPickup == '1' }, // Customers who place pick up/collect orders
				{ id: '25', condition: entity.dataValues.hasTableService == '1' }, // Customers who place table service orders
				{ id: '26', condition: entity.dataValues.hasBoth == '1' }, // Customers who place both pickup/collect & table service orders
				{ id: '31', condition: entity.dataValues.isTop5Customer == '1' }, // Top 5 customers with the highest individual purchase
				{ id: '32', condition: entity.dataValues.isTop10Customer == '1' }, // Top 10 customers with the highest individual purchase
				{ id: '33', condition: entity.dataValues.isTop20Customer == '1' }, // Top 20 customers with the highest individual purchase
				{ id: '34', condition: entity.dataValues.isTop50Customer == '1' }, // Top 50 customers with the highest individual purchase
				{ id: '35', condition: entity.dataValues.isTop100Customer == '1' }, // Top 100 customers with the highest individual purchase
				{ id: '37', condition: entity.dataValues.croissantClub == '1' }, // Croissant Club customers (spent $0 - $49.99)
				{ id: '38', condition: entity.dataValues.caramelClub == '1' }, // Creme Caramel Club customers (spent $50- $199.99)
				{ id: '39', condition: entity.dataValues.caviarClub == '1' }, // Caviar Club customers (spent $200+)
				{ id: '41', condition: entity.dataValues.birthdayToday == '1' }, // Customers who have birthdays today
				{ id: '42', condition: entity.dataValues.birthdayThisWeek == '1' }, // Customers who have birthdays this week
				{ id: '43', condition: entity.dataValues.birthdayThisMonth == '1' }, // Customers who have birthdays this month
				{ id: '45', condition: entity.dataValues.orderedInSummer == '1' }, // Customers who order in Summer (Dec - Feb)
				{ id: '46', condition: entity.dataValues.orderedInAutumn == '1' }, // Customers who order in Autumn (March - May)
				{ id: '47', condition: entity.dataValues.orderedInWinter == '1' }, // Customers who order in Winter (June - Aug)
				{ id: '48', condition: entity.dataValues.orderedInSpring == '1' }, // Customers who order in Spring (Sept - Nov)
				{ id: '50', condition: entity.dataValues.newCustomer == '1' }, // New customers (ordered within the last 30 days)
				{ id: '51', condition: entity.dataValues.frequentCustomer == '1' }, // Frequent customers (ordered within the last 90 day...
				{ id: '52', condition: entity.dataValues.inactiveCustomer == '1' }, // Inactive customers (not ordered for over 90 days)
				{ id: '54', condition: entity.dataValues.hasConsent == '1' }, // Subscribed customers
				{ id: '55', condition: entity.dataValues.hasConsent == '0' }, // Unsubscribed customers
				{ id: '57', condition: entity.dataValues.isFoodOnly == '1' }, // Customers who purchase food only
				{ id: '58', condition: entity.dataValues.isDrinksOnly == '1' }, // Customers who purchase drinks only
				{ id: '59', condition: entity.dataValues.hasFoodAndDrinks == '1' }, // Customers who purchase food & drinks
				{ id: '60', condition: entity.dataValues.isKidsProductOnly == '1' }, // Customers who purchase KidsProduct Only
				{ id: '61', condition: entity.dataValues.isAlcoholOnly == '1' }, // Customers who purchase food & drinks
			];

			conditions.forEach(({ id, condition }) => {
				if (id && condition) {
					addSegment(id);
				} else if (userSegmentSet.has(id)) {
					removeSegment.push({
						userID: entity.userID,
						barID: entity.barID,
						segmentID: id
					});
				}
			});

			// // Prepare for removal of age range segments
			Object.values(segmentIdMap).forEach((segmentId) => {
				if (
					userSegmentSet.has(segmentId) &&
					segmentIdMap[entity.user.dataValues.age_range] != segmentId
				) {
					removeSegment.push({
						userID: entity.userID,
						barID: entity.barID,
						segmentID: segmentId
					});
				}
			});
		});

		await Promise.all(userSegmentPromises);

		// Bulk insert and delete operations
		if (createSegment.length) {
			await SegmentUserVenueModel.bulkCreate(createSegment);
		}

		if (removeSegment.length) {
			await SegmentUserVenueModel.destroy({
				where: {
					[Sequelize.Op.or]: removeSegment.map((segment) => ({
						userID: segment.userID,
						barID: segment.barID,
						segmentID: segment.segmentID
					}))
				}
			});
		}

		const userSegmentsToDelete = await SegmentUserVenueModel.findAll({
			include: [
				{
					model: UserModel,
					attributes: [],
					required: false
				}
			],
			where: {
				'$user.id$': null
			}
		});

		const userIDsToDelete = userSegmentsToDelete.map(
			(userSegment) => userSegment.userID
		);

		if (userIDsToDelete.length > 0) {
			await SegmentUserVenueModel.destroy({
				where: {
					userID: userIDsToDelete
				}
			});
		}
	} catch (error) {
		console.log(error);
		throw error;
	}
}
