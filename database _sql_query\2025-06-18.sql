CREATE TABLE IF NOT EXISTS `segment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `isParent` enum('0','1') NOT NULL DEFAULT '0' COMMENT '0-No , 1-Yes',
  `isActive` enum('0','1') NOT NULL DEFAULT '1' COMMENT '0-Inactive , 1-Active',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `segment`
--

INSERT INTO `segment` (`id`, `name`, `isParent`, `isActive`, `createdAt`, `updatedAt`) VALUES
(1, 'All customers', '0', '1', '2024-10-10 15:51:53', NULL),
(2, 'Customer volume purchasing patterns', '1', '1', '2024-10-10 15:51:53', NULL),
(3, 'Customers who have purchased once', '0', '1', '2024-10-10 15:51:53', NULL),
(4, 'Customers who have made more than one purchase, but fewer than 10', '0', '1', '2024-10-10 15:51:53', NULL),
(5, 'Customers who have purchased more than 10 times', '0', '1', '2024-10-10 15:52:49', NULL),
(6, 'Customer age demographic', '1', '1', '2024-10-10 15:52:49', NULL),
(7, 'Customer Age Range: 18 - 27 (Gen Z)', '0', '1', '2024-10-10 15:53:10', NULL),
(8, 'Customer Age Range: 28 - 43 (Millennial)', '0', '1', '2024-10-10 15:53:10', NULL),
(9, 'Customer Age Range: 44 - 59 (Gen X)', '0', '1', '2024-10-10 15:53:19', NULL),
(10, 'Customer Age Range: 60 - 78 (Baby Boomers)', '0', '1', '2024-10-10 15:53:19', NULL),
(11, 'Customer Age Range: 79 - 99+ (Silent Generation+)', '0', '1', '2024-10-10 15:53:32', NULL),
(12, 'Customer ordering time patterns', '1', '1', '2024-10-10 15:53:32', NULL),
(13, 'Customers who purchase early morning (6am - 9am)', '0', '1', '2024-10-10 15:53:47', NULL),
(14, 'Customers who purchase mid morning (9am - 11am)', '0', '1', '2024-10-10 15:53:47', NULL),
(15, 'Customers who purchase at lunch (11pm - 2pm)', '0', '1', '2024-10-10 15:54:07', NULL),
(16, 'Customers who purchase in the afternoon (2pm - 5pm)', '0', '1', '2024-10-10 15:54:07', NULL),
(17, 'Customers who purchase in the evening (5pm - 9pm)', '0', '1', '2024-10-10 15:54:17', NULL),
(18, 'Customers who purchase late night (9pm - 11:59pm)', '0', '1', '2024-10-10 15:54:17', NULL),
(19, 'Customer menu ordering patterns', '1', '0', '2024-10-10 15:54:35', NULL),
(20, 'Customers who purchase food only', '0', '0', '2024-10-10 15:54:35', NULL),
(21, 'Customers who purchase food only', '0', '0', '2024-10-10 15:54:48', NULL),
(22, 'Customers who purchase food & drinks', '0', '0', '2024-10-10 15:54:48', NULL),
(23, 'Customer service type patterns', '1', '1', '2024-10-10 15:55:09', NULL),
(24, 'Customers who place pick up/collect orders', '0', '1', '2024-10-10 15:55:09', NULL),
(25, 'Customers who place table service orders', '0', '1', '2024-10-10 15:55:18', NULL),
(26, 'Customers who place both pickup/collect & table service orders', '0', '1', '2024-10-10 15:55:18', NULL),
(27, 'Customer family demographics', '1', '0', '2024-10-10 16:10:18', NULL),
(28, 'Customers who most likely order with no kids', '0', '0', '2024-10-10 16:10:18', NULL),
(29, 'Customers who most likely order with kids (a family)', '0', '0', '2024-10-10 16:10:43', NULL),
(30, 'Highest individual purchase customer segments', '1', '1', '2024-10-10 16:10:43', NULL),
(31, 'Top 5 customers with the highest individual purchase', '0', '1', '2024-10-10 16:23:00', NULL),
(32, 'Top 10 customers with the highest individual purchase', '0', '1', '2024-10-10 16:23:00', NULL),
(33, 'Top 20 customers with the highest individual purchase', '0', '1', '2024-10-10 16:29:18', NULL),
(34, 'Top 50 customers with the highest individual purchase', '0', '1', '2024-10-10 16:29:18', NULL),
(35, 'Top 100 customers with the highest individual purchase', '0', '1', '2024-10-10 16:29:30', NULL),
(36, 'Customer total spend segment', '1', '1', '2024-10-10 16:29:30', NULL),
(37, 'Croissant Club customers (spent $0 - $49.99)', '0', '1', '2024-10-10 16:29:45', NULL),
(38, 'Crème Caramel Club customers (spent $50- $199.99)', '0', '1', '2024-10-10 16:29:45', NULL),
(39, 'Caviar Club customers (spent $200+)', '0', '1', '2024-10-10 16:29:59', NULL),
(40, 'Customer birthday segment', '1', '1', '2024-10-10 16:29:59', NULL),
(41, 'Customers who have birthdays today', '0', '1', '2024-10-10 16:30:07', NULL),
(42, 'Customers who have birthdays this week', '0', '1', '2024-10-10 16:30:07', NULL),
(43, 'Customers who have birthdays this month', '0', '1', '2024-10-10 16:30:25', NULL),
(44, 'Customer season segment', '1', '1', '2024-10-10 16:30:25', NULL),
(45, 'Customers who order in Summer (Dec - Feb)', '0', '1', '2024-10-10 16:30:34', NULL),
(46, 'Customers who order in Autumn (March - May)', '0', '1', '2024-10-10 16:30:34', NULL),
(47, 'Customers who order in Winter (June - Aug)', '0', '1', '2024-10-10 16:30:49', NULL),
(48, 'Customers who order in Spring (Sept - Nov)', '0', '1', '2024-10-10 16:30:49', NULL),
(49, 'Customer activity segment ', '1', '1', '2024-10-10 16:31:01', NULL),
(50, 'Active Customers (ordered within the last 30 days)', '0', '1', '2024-10-10 16:31:01', NULL),
(51, 'Recent Customers (ordered within 31-90 days)', '0', '1', '2024-10-10 16:31:14', NULL),
(52, 'Inactive Customers (ordered over 90 days ago)', '0', '1', '2024-10-10 16:31:14', NULL),
(53, 'Customer subscription status', '1', '1', '2024-12-11 11:20:30', '2024-12-11 11:15:01'),
(54, 'Subscribed Customers', '0', '1', '2024-12-11 11:55:17', '2024-12-11 11:40:17'),
(55, 'Unsubscribed Customers', '0', '1', '2024-12-11 11:55:17', '2024-12-11 11:40:17');