const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
    class orderTableNumber extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            orderTableNumber.belongsTo(models.orders, {
                foreignKey: 'orderID'
            });
            models.orders.hasMany(orderTableNumber, { foreignKey: 'orderID' });
        }
    }
    orderTableNumber.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true
            },
            orderID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'orders',
                    key: 'id'
                }
            },
            tableCode: DataTypes.STRING,
            createdAt: DataTypes.DATE
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: 'orderTableNumber',
            timestamps: true
        }
    );
    return orderTableNumber;
};
