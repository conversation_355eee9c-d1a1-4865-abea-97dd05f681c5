/*Messages,status code and services require*/
require('dotenv').config();
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const AdvertiserUserModel = require('../../database/models').advertiser_user;
const AdvertiserUserTempModel = require('../../database/models').advertiser_user_temp;
const models = require('../../database/models');
const generateRandomString = require('../../helper/generalHelper').generateRandomString;
const timezoneModel = require('../../database/models').timezone;
const AdvertiserUserTokenModel = require('../../database/models').advertiser_user_accesstoken;
const Sequelize = require('sequelize');
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const { default: jwtDecode } = require('jwt-decode');
const { status } = require('../../config/status');
const { generateAdvertiserToken } = require('../../helper/authHelper');

const Op = Sequelize.Op;

module.exports = {
    /*Register */
    async register(req, res) {
        try {
            let findUser = await AdvertiserUserModel.findOne({
                where: {
                    email: req.body.email.toLowerCase().trim(),
                    deleted_at: null
                }
            });

            let findTempAdvertiserUser = await AdvertiserUserTempModel.findOne({
                where: {
                    email: req.body.email.toLowerCase().trim(),
                    deleted_at: null
                }
            });
            const timezones = await timezoneModel.findAll({
                attributes: ['id','name'],
                raw: true
            });
            
            const timezoneMap = {};
            timezones.forEach(tz => {
                timezoneMap[tz.id] = tz.name;
            });
            
            // email is exist
            if (findUser) {
                return 0;
            }

            let verified = speakeasy.totp.verify({
                secret: findTempAdvertiserUser.dataValues.mfa_code,
                encoding: 'base32',
                token: req.body.code,
                window: 1
            });

            if (!verified) {
                return 2;
            }

            if (findTempAdvertiserUser) {
                await imageUpload(
                    req.file,
                    constant.AWSS3ADVERTISERQRCODEFOLDER + req.file.originalname
                );

                let userDetails = {
                    contact_name: req.body.contact_name,
                    business_name: req.body.business_name,
                    business_url: req.body.business_url,
                    acn_number: req.body.acn_number,
                    email: req.body.email.toLowerCase().trim(),
                    password: req.body.password,
                    mfa_qr_code: findTempAdvertiserUser.mfa_qr_code,
                    mfa_code: findTempAdvertiserUser.mfa_code,
                    profile_image: req.file.originalname,
                    password_updated_at: new Date(),
                    timezone_id: req.body.timezone,
                    timezone: timezoneMap[req.body.timezone]
                };

                let findAdvertiserUser = await AdvertiserUserModel.create(userDetails);

                findAdvertiserUser = await generateAdvertiserToken(findAdvertiserUser, req);

                await AdvertiserUserTempModel.destroy({
                    where: { email: req.body.email.toLowerCase().trim() },
                    force: true
                });

                let subject = 'Your Advertiser Account Has Been Submitted for Review';
                let mailbody =
                    '<div><p>Hi ' + req.body.contact_name +
                    ',' +
                    '</p><p>Your advertiser account has been successfully created and submitted for review by our team.</p>' +
                    '<p>We’ll notify you by email once your account has been reviewed and approved. Please allow up to 24 hours for this review.</p>' +
                    '<p>If you have any questions in the meantime, feel free to contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>' +
                    '<p>Thank you, <br /> MyTab Advertiser Support <br />' +
                    '<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p></div>';
                mail.sendmail(res, req.body.email, subject, mailbody);

                let subjectAdmin = 'New Advertiser Account Submitted for Review';
                let mailbodyAdmin =
                    '<div>' +
                    '<p>Hi Team,</p>' +
                    '<p>A new advertiser account has been created and submitted for review.</p>' +
                    '<p><strong>Account Details:</strong></p>' +
                    '<ul>' +
                        '<li><strong>Advertiser Name:</strong> ' + req.body.contact_name + '</li>' +
                        '<li><strong>Email:</strong> ' + req.body.email + '</li>' +                        
                    '</ul>' +                    
                    '<p>If you have any questions or need additional information, feel free to contact support at ' +
                    '<a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a>.</p>' +
                    '<p>Thank you,<br />MyTab Advertiser Support<br />' +
                    '<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>' +
                    '</div>';
                mail.sendmail(res, process.env.ADVERTISER_EMAILTO, subjectAdmin, mailbodyAdmin);
                return findAdvertiserUser;
            }
            return 1;
        } catch (err) {
            console.log(err);
            throw err;
        }
    },
    async getTimezones() {
        try {
            return await timezoneModel.findAll({
                attributes: ["id", "name","title"],
				where: {
					status: 'Active'
				},
                order: [['sequence', 'ASC']]
            });
        } catch (error) {
            throw error;
        }
    },
    async sendVerificationCode(req, res) {
        try {
            let findUser = await AdvertiserUserTempModel.findOne({
                where: {
                    email: req.body.email.toLowerCase().trim(),
                    deleted_at: null
                }
            });

            // email is exist
            if (findUser) {
                return 0;
            }
            let otp = helper.generateOtp();
            let userDetails = {
                contact_name: req.body.contact_name,
                email: req.body.email.toLowerCase().trim(),
                connect_email_otp: otp,
                status: 'Inactive',
            };
            let findAdvertiserUser = await AdvertiserUserTempModel.create(userDetails);

            let subject = 'Verify Your Email Address to Complete Your Advertiser Account Setup';
            let mailbody =
                '<div><p>Hi ' + req.body.contact_name +
                ',' +
                '</p><p>To verify your email address and activate your advertiser account on the MyTab Management Portal, please enter the 6-digit code below:</p>' +
                '<p>Your Verification Code: ' +
                '<b>' +
                otp +
                '</b>' +
                ' </p> ' +
                '<p>If you didn’t request this verification, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>' +
                '<p>Thank you, <br /> MyTab Advertiser Support <br />' +
                '<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p></div>';
            mail.sendmail(res, req.body.email, subject, mailbody);
            return 3;
        } catch (error) {
            console.log(error);
            throw error;
        }
    },
    /* login  */
    async login(req, res) {
        try {
            let findAdvertiserUser = await AdvertiserUserModel.findOne({
                where: {
                    email: req.body.email.toLowerCase().trim(),
                    deleted_at: null
                },
                attributes: {
                    exclude: ['createdAt', 'updatedAt', 'deletedAt', 'otp_token']
                }
            });

            //user not found
            if (!findAdvertiserUser) {
                return 0;
            }
            let verified = speakeasy.totp.verify({
                secret: findAdvertiserUser.dataValues.mfa_code,
                encoding: 'base32',
                token: req.body.code,
                window: 1
            });

            if (!verified) {
                return 2;
            }
            // Check Password
            if (
                !Bcryptjs.compareSync(
                    req.body.password,
                    findAdvertiserUser.dataValues.password
                )
            ) {
                return 0; //password is incorrect
            }
            console.log(findAdvertiserUser.account_verified, "findAdvertiserUser")
            if (findAdvertiserUser.account_verified === 'New') {
                return 3; // Account not verified yet
            }

            if (findAdvertiserUser.account_verified === 'Rejected') {
                return 4; // Account rejected by admin
            }

            findAdvertiserUser = await generateAdvertiserToken(findAdvertiserUser, req);

            //send mail if mfa code is null
            if (findAdvertiserUser.mfa_code === null) {
                let secret = speakeasy.generateSecret({
                    length: 20,
                    name: findAdvertiserUser.first_name
                });
                let imageData = await QRCode.toDataURL(secret.otpauth_url);

                let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
                let imageName =
                    findAdvertiserUser.dataValues.id +
                    '_' +
                    generateRandomString(5) +
                    '_' +
                    Date.now() +
                    '.' +
                    extension;

                await QrimageUpload(
                    imageData,
                    constant.AWSS3ADVERTISERQRCODEFOLDER + imageName
                );

                let qrCode = await helper.s3GetImage(
                    constant.AWSS3ADVERTISERQRCODEFOLDER + imageName
                );

                await AdvertiserUserModel.update(
                    { mfa_code: secret.base32, mfa_qr_code: imageName },
                    {
                        where: {
                            id: findAdvertiserUser.dataValues.id,
                            deleted_at: null
                        }
                    }
                );

                let subject = 'Multi-Factor Authentication - ' + constant.APP_NAME;
                let mailbody =
                    '<p>Welcome! The first step to gaining access to the ' +
                    constant.APP_NAME +
                    ' portal is setting up multi-factor authentication, which helps keep ' +
                    ' our community safe. Please follow the below instructions: </p>' +
                    '<p>1. Download the Google Authenticator app<br/> 2. Scan the QR code via the Google Authenticator app <br/> 3. Enter the 6-digit code provided by the Google Authenticator app to log in to your MyTab Venue CMS account </p>' +
                    "<img src='" +
                    qrCode +
                    "' width='200'/>" +
                    '<p>If you did not create a MyTab Venue CMS account, please contact our venue support <NAME_EMAIL> for assistance.</p>';
                mail.sendmail(res, req.body.email, subject, mailbody);
            }
            delete findAdvertiserUser.dataValues.password;
            // delete findAdvertiserUser.dataValues.mfa_qr_code;
            return findAdvertiserUser;
        } catch (err) {
            console.log('err', err);
            throw err;
        }
    },
    async verifyOTP(req) {
        const { email, otp } = req.body;

        if (!email || !otp) {
            return res.status(400).json({
                success: 0,
                message: "Email and OTP are required"
            });
        }

        let findAdvertiserUser = await AdvertiserUserTempModel.findOne({
            where: {
                email,
                connect_email_otp: otp,
                deleted_at: null,
                status: 'Inactive'
            }
        });

        if (findAdvertiserUser) {
            let secret = speakeasy.generateSecret({});
            var url = speakeasy.otpauthURL({
                secret: secret.base32,
                label: req.body.contact_name,
                issuer: 'MyTab Advertiser Portal',
                encoding: 'base32'
            });
            let imageData = await QRCode.toDataURL(url);

            let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
            let imageName = generateRandomString(5) + '_' + Date.now() + '.' + extension;

            await QrimageUpload(
                imageData,
                constant.AWSS3ADVERTISERQRCODEFOLDER + imageName
            );
            await AdvertiserUserTempModel.update(
                { mfa_code: secret.base32, mfa_qr_code: imageName },
                { where: { id: findAdvertiserUser.id } }
            );
            findAdvertiserUser.mfa_code = secret.base32;
            findAdvertiserUser.mfa_qr_code = constant.AWSS3PRIVATEURL + constant.AWSS3ADVERTISERQRCODEFOLDER + imageName;
            return findAdvertiserUser;
        } else {
            return 0;
        }
    },
    //verify MFA
    async verifyMFA(req) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                email: req.body.email,
                id: req.body.user_id,
                deleted_at: null,
                status: 'Active'
            }
        });

        if (findAdvertiserUser) {
            let verified = speakeasy.totp.verify({
                secret: findAdvertiserUser.dataValues.mfa_code,
                encoding: 'base32',
                token: req.body.code,
                window: 1
            });

            if (verified) {
                // generate JWT token with email and admin id
                let jwtString = JwtToken.sign(
                    {
                        email: findAdvertiserUser.dataValues.email,
                        user_id: findAdvertiserUser.dataValues.id
                    },
                    constant.JWTTOKEN.secret,
                    {
                        expiresIn: constant.JWTTOKEN.expiresIn,
                        algorithm: constant.JWTTOKEN.algo
                    }
                ); // default: HS256 encryption

                await AdvertiserUserTokenModel.create({
                    access_token: jwtString,
                    device_token: req.body.device_token,
                    user_id: req.body.user_id,
                    device_type: req.body.device_type,
                    device_name: req.body.device_name,
                    device_location: req.body.device_location
                });

                findAdvertiserUser.dataValues.token = jwtString;
                delete findAdvertiserUser.dataValues.password;
                return findAdvertiserUser;
            }
            return 1;
        } else {
            return 0;
        }
    },

    //verify Password
    async verifyPassword(req) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                id: req.user_id,
                deleted_at: null,
                status: 'Active'
            }
        });
        if (findAdvertiserUser) {
            // Check Password
            if (
                !Bcryptjs.compareSync(
                    req.body.password,
                    findAdvertiserUser.dataValues.password
                )
            ) {
                return 0; //password is incorrect
            } else {
                return 1;
            }
        } else {
            return 0;
        }
    },

    //setup MFA
    async setupMFA(req, res) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                email: req.body.email,
                deleted_at: null
            }
        });
        if (!findAdvertiserUser) {
            return 0;
        }

        if (findAdvertiserUser.dataValues.mfa_qr_code) {
            imageDelete(
                constant.AWSS3VENUEQRCODEFOLDER + findAdvertiserUser.dataValues.mfa_qr_code
            );
        }
        let secret = speakeasy.generateSecret({});
        var url = speakeasy.otpauthURL({
            secret: secret.base32,
            label: findAdvertiserUser.contact_name,
            issuer: 'MyTab Advertiser Portal',
            encoding: 'base32'
        });
        let imageData = await QRCode.toDataURL(url);
        let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
        let imageName =
            findAdvertiserUser.dataValues.id +
            '_' +
            generateRandomString(5) +
            '_' +
            Date.now() +
            '.' +
            extension;

        await QrimageUpload(imageData, constant.AWSS3VENUEQRCODEFOLDER + imageName);

        let qrCode = await helper.s3GetImage(
            constant.AWSS3VENUEQRCODEFOLDER + imageName
        );

        await AdvertiserUserModel.update(
            { mfa_code: secret.base32, mfa_qr_code: imageName },
            { where: { id: findAdvertiserUser.dataValues.id } }
        );

        let subject = 'Set Up 2FA For Your MyTab Advertiser Portal';
        let mailbody =
            '<p>Hello  ' +
            findAdvertiserUser.contact_name +
            ',' +
            '</p>' +
            '<p>Please follow the below steps to set up 2 Factor Authentication for your MyTab Advertiser Portal. To ensure the highest security for your Advertiser Portal and to keep our community safe, 2FA will be required each time you sign in.</p>' +
            '<p>1. Download an authenticator app on your mobile, or if you have an existing authenticator app, open your authenticator app on your mobile and use it to scan the QR code below. </p>' +
            '<p>2. Enter the 6-digit code provided by your authenticator app to log in to your MyTab Advertiser Portal.</p>' +
            "<img src='" +
            qrCode +
            "' width='200'/>" +
            '<p> Enter the below code manually instead </p><p> <b>' +
            secret.base32 +
            '</b> </p>' +
            '<p>If you did not initiate this request, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a></p>' +
            '<p>Thank you, <br /> MyTab Venue Support <br />' +
            '<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>';
        await mail.sendmail(res, req.body.email, subject, mailbody);
        return 1;
    },

    /* Forgot-Password */
    async forgotPassword(req, res) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                email: req.body.email,
                deleted_at: null,
                status: 'Active'
            }
        });

        if (!findAdvertiserUser) {
            return 0; // user not found
        }
        let token = helper.generateExpirationToken(req.body.email);

        //save token
        await AdvertiserUserModel.update(
            { otp_token: token },
            {
                where: {
                    email: req.body.email
                }
            }
        );

        var subject = 'Reset Your MyTab Advertiser Portal Password';
        var mailbody =
            '<div><p>We have received a password reset request for your Advertiser Portal associated with the email <b><a href="mailto:' +
            req.body.email +
            '" style="color:#ff6460">' +
            req.body.email +
            '</a></b>.</p>' +
            '<p> To reset your password for your MyTab Advertiser Portal, click  ' +
            '<a style="color:#ff6460" href="' +
            constant.CMS_URL + //for local testing
            // constant.API_URL +
            // "app/auth/reset-password-page/" +
            '/advertiser/reset-password/' +
            findAdvertiserUser.dataValues.id +
            '_' +
            helper.generateRandomString(8) +
            '~' + //using tilde a its is the only character left which does not need encoding and .,-,_ are used in jwttoken
            token +
            '"  > here</a>' +
            '. Please note, to ensure the highest security this link is valid for only 48 hours.</p>' +
            '<p>For further assistance or enquiries, please reach out to us at <a style="color:#ff6460" href="mailto:<EMAIL>"><EMAIL></a> and our dedicated venue representatives will be happy to help.</p>' +
            '<p>If you did not initiate this password reset request, you can safely ignore this email.</p>' +
            '<p> Thank you <br /> MyTab Venue Support <br />' +
            '<a style="color:#ff6460" href="https://www.mytabinfo.com">www.mytabinfo.com</a><p/>';
        mail.sendmail(res, req.body.email, subject, mailbody);
        return 1;
    },

    /* verify token */
    async verifyToken(req) {
        try {
            const jwtVerify = JwtToken.verify(
                req.body.token,
                constant.JWTTOKEN.secret
            );

            if (jwtVerify) {
                const findToken = await AdvertiserUserModel.findOne({
                    where: {
                        otp_token: req.body.token
                    }
                });

                if (findToken) {
                    return 1; // token valid
                }
            }
            return 0; // token invalid
        } catch (error) {
            return 0;
        }
    },

    //reset password
    async resetPasswsord(req) {
        try {
            let otp_token = req.body.otp_token;

            const JwtDetails = jwtDecode(otp_token);

            let advertiserUserData = await AdvertiserUserModel.findOne({
                where: {
                    email: JwtDetails.email,
                    otp_token: otp_token
                }
            });
            if (!advertiserUserData) {
                return 0;
            }
            //update password and delete token
            await advertiserUserData.update(
                { password: req.body.password, otp_token: null },
                {
                    where: {
                        email: advertiserUserData.dataValues.email
                    }
                }
            );
            return 1;
        } catch (error) {
            console.log(err);
            return error;
        }
    },

    /* Update Profile */
    async updateProfile(req) {
        try {
            let userId = req.user_id;
            let findAdvertiserUser = await AdvertiserUserModel.findOne({
                where: {
                    id: userId,
                    deleted_at: null,
                    status: 'Active'
                },
                attributes: [
                    'contact_name',
                    'business_name',
                    'profile_image',
                    'acn_number',
                    'business_url',
                ]
            });

            if (!findAdvertiserUser) {
                return 0; // user not found
            }

            if (req.file) {
                if (findAdvertiserUser.profile_image) {
                    // delete old image
                    await imageDelete(findAdvertiserUser.profile_image.split('.com/')[1]);
                }
                await imageUpload(
                    req.file,
                    constant.AWSS3ADVERTISERQRCODEFOLDER + req.file.originalname
                );
            }
            //update data
            await AdvertiserUserModel.update(
                {
                    contact_name: req.body.contact_name,
                    business_name: req.body.business_name,
                    acn_number: req.body.acn_number,
                    business_url: req.body.business_url,
                    profile_image: req.file?.originalname,
                },
                {
                    where: {
                        id: userId
                    }
                }
            );
            let updatedUser = await AdvertiserUserModel.findOne({
                where: {
                    id: userId
                },
                attributes: [
                    'contact_name',
                    'business_name',
                    'profile_image',
                    'acn_number',
                    'business_url',
                ]
            });

            return updatedUser;
        } catch (err) {
            console.log('err', err);
            throw err;
        }
    },

    //get profile
    async getProfileDetails(req) {
        try {
            let userId = req.user_id;

            let findAdvertiserUser = await AdvertiserUserModel.findOne({
                where: {
                    id: userId,
                    deleted_at: null,
                    status: 'Active'
                },

                attributes: [
                    'first_name',
                    'last_name',
                    'profile_image',
                    'email',
                    'mobile',
                    'address',
                    'pronouns',
                    'country_code'
                ],
                include: [
                    {
                        model: BarModel,
                        attributes: ['restaurantName', 'avatar', 'id'],
                        through: {
                            attributes: []
                        }
                    },
                    {
                        model: SubscriptionModel,
                        attributes: ['id', 'name', 'price'],
                        through: {
                            where: {
                                status: 1
                            },
                            attributes: []
                        }
                    }
                ]
            });
            if (!findAdvertiserUser) {
                return 0;
            }

            return findAdvertiserUser;
        } catch (error) {
            console.log(error);
        }
    },

    //get my venue details
    async myVenueDetails(req) {
        try {
            let userId = req.user_id;
            let BarList = await VenueUserBarModel.findAll({
                where: {
                    user_id: userId,
                    deleted_at: null
                },
                include: [
                    {
                        model: BarModel,
                        where: {
                            isDeleted: 'No'
                        },
                        attributes: [
                            'restaurantName',
                            'avatar',
                            'id',
                            'docketStatus',
                            'managerName',
                            'posStatus',
                            'venueId',
                            'docketCommission',
                            'posFee',
                            'passcodeStatus',
                            'countryCode',
                            'mobile',
                            [Sequelize.literal('LENGTH(passcode)'), 'passcodeLength']
                        ]
                    }
                ]
            });
            if (!BarList) {
                return 0;
            }

            return BarList;
        } catch (error) {
            console.log(error);
        }
    },

    /*LOGOUT  */
    async logout(req, res) {
        try {
            // delete token
            let Token = await AdvertiserUserTokenModel.destroy({
                where: {
                    access_token: req.headers.authorization,
                    user_id: req.user_id
                }
            });

            //token not present
            if (Token) {
                return 1;
            }
            return 0;
        } catch (err) {
            console.log('err');
            throw err;
        }
    },
    //verify MFA
    async verifyMFAOtp(req) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                id: req.user_id,
                deleted_at: null,
                status: 'Active'
            }
        });

        if (findAdvertiserUser) {
            let verified = speakeasy.totp.verify({
                secret: findAdvertiserUser.dataValues.mfa_code,
                encoding: 'base32',
                token: req.body.code,
                window: 1
            });

            if (verified) {
                delete findAdvertiserUser.dataValues.password;
                return findAdvertiserUser;
            }
            return 1;
        } else {
            return 0;
        }
    },
    //save subscription
    async saveSubscription(req) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                id: req.user_id,
                deleted_at: null,
                status: 'Active'
            }
        });

        if (!findAdvertiserUser) {
            return 0;
        }
        let subscriptionData = await VenueUserSubscriptionModel.create({
            subscription_id: req.body.subscription_id,
            user_id: req.user_id,
            status: '1'
        });

        return subscriptionData;
    },
    //delete venue
    async deleteVenue(req) {
        let findAdvertiserUser = await AdvertiserUserModel.findOne({
            where: {
                id: req.user_id
            }
        });

        if (!findAdvertiserUser) {
            return 0;
        }

        await AdvertiserUserModel.destroy({
            where: {
                id: req.user_id
            }
        });

        return 1;
    }
};
