const express = require('express');
const router = express();
const multer = require('multer');
const orderController = require('../../controllers/venue/orderController');
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;

var upload = multer({});

//order history(filter-order type,data,search)
router.post(
	'/barOrderHistory',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.barOrderHistory
);

router.post(
	'/barOrderListCount',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.barOrderListCount
);

router.post(
	'/barOrderList',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.barOrderList
);

router.post(
	'/orderView',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.orderView
);

router.post(
	'/cancel',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.orderCancel
);

router.post(
	'/orderIntoxicated',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.orderIntoxicated
);

router.post(
	'/updateOrderItemStatus',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.updateOrderItemStatus
);

router.post(
	'/downloadOrderHistory',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.downloadOrderHistory
);

router.post(
	'/updateOrderItemWaitTime',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.updateOrderItemWaitTime
);

router.get(
	'/updateOrderItemNewRefundAmount',
	orderController.updateOrderItemNewRefundAmount
);

router.post(
	'/readyForPickupAlert',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.readyForPickupAlert
);

module.exports = router;
