'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class pos_surcounts extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  pos_surcounts.init({
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    posID: DataTypes.STRING,
    name: DataTypes.STRING,
    amount: DataTypes.NUMBER,
    isDeleted: DataTypes.STRING,
    productID: DataTypes.STRING,
    surcount_type: DataTypes.STRING,
    createdAt: {
      type: DataTypes.DATE,

    },
    updatedAt: DataTypes.DATE,
    isDeleted: DataTypes.ENUM("Yes", "No"),
  }, {
    sequelize,
    modelName: 'pos_surcounts',
  });
  return pos_surcounts;
};