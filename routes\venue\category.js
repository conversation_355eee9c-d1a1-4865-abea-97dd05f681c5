var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require("../../middleware/venueAuth").venueAuthorization;

const IsVenueUserConnectedWithBar =
  require("../../middleware/venueAuth").isVenueUserConnectedWithBar;

/*require for security */
const categoryController = require("../../controllers/venue/categoryController");

/* get category */
router.get("/getCategory", Authorization, categoryController.getCategory);

router.post(
  "/getSubCategory",
  Authorization,
  IsVenueUserConnectedWithBar,
  categoryController.getSubCategory
);
/*User API*/
router.post(
  "/getCategoryList",
  Authorization,
  IsVenueUserConnectedWithBar,
  categoryController.getCategoryList
);

/*bar sub-category sequence */
router.post(
  "/barSubCategorySequence",
  Authorization,
  IsVenueUserConnectedWithBar,
  categoryController.barSubCategorySequence
);

router.post(
  "/getSubCategoryList",
  Authorization,
  IsVenueUserConnectedWithBar,
  categoryController.getSubCategoryList
);

router.post(
  "/getSubCategoryForManageMenu",
  Authorization,
  IsVenueUserConnectedWithBar,
  categoryController.getSubCategoryForManageMenu
);

module.exports = router;
