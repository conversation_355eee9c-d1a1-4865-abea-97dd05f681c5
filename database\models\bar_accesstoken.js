"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class bar_accesstoken extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      bar_accesstoken.belongsTo(models.bar, {
        foreignKey: "barID",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  bar_accesstoken.init(
    {
      id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      barID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
      deviceToken: {
        type: DataTypes.STRING(255),
      },
      accessToken: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      deviceType: DataTypes.ENUM("ios", "android"),
      notificationEnable: DataTypes.ENUM("Yes", "No"),

      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {
      sequelize,
      freezeTableName: true,
      modelName: "bar_accesstoken",
      timestamps: true,
    }
  );
  return bar_accesstoken;
};
