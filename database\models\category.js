"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class category extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // category.belongsTo(models.admin, {
      //   foreignKey: "assigned_to",
      //   targetKey: "id",
      //   onDelete: "CASCADE",
      // });
    }
  }
  category.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      name: DataTypes.STRING(255),
      status: {
        type: DataTypes.ENUM("Active", "Inactive"),
        defaultValue: "Active",
      },
      isDeleted: { type: DataTypes.ENUM("Yes", "No"), defaultValue: "No" },
      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },
    {
      sequelize,
      modelName: "category",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return category;
};
