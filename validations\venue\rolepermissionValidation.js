const Joi = require('joi');

module.exports = {
	async addRolePermissionValidation(req) {
		const schema = Joi.object({
			role: Joi.string().required().messages({
				'string.empty': 'Role name is required/'
			}),
			permissions: Joi.array()
				.items(
					Joi.object({
						venue_user_permission_id: Joi.number().required(),
						view: Joi.boolean().required(),
						add: Joi.boolean().required(),
						edit: Joi.boolean().required(),
						delete: Joi.boolean().required(),
						status: Joi.boolean().required()
					})
				)
				.required()
		});
		return schema.validate(req.body);
	},

	async updateRolePermissionValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'number.base': 'Id is required.'
			}),
			role: Joi.string().required().messages({
				'string.empty': 'Role name is required.'
			}),
			permissions: Joi.array()
				.items(
					Joi.object({
						venue_user_permission_id: Joi.number().required(),
						view: Joi.boolean().required(),
						add: Joi.boolean().required(),
						edit: Joi.boolean().required(),
						delete: Joi.boolean().required(),
						status: Joi.boolean().required()
					})
				)
				.required()
		});
		return schema.validate(req.body);
	},

	async deleteRolePermissionValidation(req) {
		const schema = Joi.object({
			role_id: Joi.number().required().messages({
				'number.base': 'Role id is required.'
			})
		});
		return schema.validate(req.body);
	},

	async changeStatusRolePermissionValidation(req) {
		const schema = Joi.object({
			role_id: Joi.number().required().messages({
				'number.base': 'Role id is required.'
			})
		});
		return schema.validate(req.body);
	}
};
