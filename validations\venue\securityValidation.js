const Joi = require('joi');

module.exports = {
	async logoutDeviceValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'Id is required.',
				'number.empty': 'Id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async changePasswordValidation(req) {
		const schema = Joi.object({
			old_password: Joi.string().required().messages({
				'any.required': 'Old Password is required.',
				'string.empty': 'Old Password is required.'
			}),
			new_password: Joi.string().required().messages({
				'any.required': 'New Password is required.',
				'string.empty': 'New Password is required.'
			})
		});
		return schema.validate(req.body);
	},
	async verifyOtpValidation(req) {
		const schema = Joi.object({
			otp: Joi.string().required().messages({
				'any.required': 'Otp is required.',
				'string.empty': 'Otp is required.'
			})
		});
		return schema.validate(req.body);
	}
};
