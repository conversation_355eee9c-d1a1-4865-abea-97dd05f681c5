const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const pageService = require('../../services/venue/pageService');

module.exports = {
	async terms(req, res) {
		try {
			let content = await pageService.terms(req, res);
			if (content == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					content,
					message.SUCCESS,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async barPrivacyPolicy(req, res) {
		try {
			let content = await pageService.barPrivacyPolicy(req, res);
			if (content == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					content,
					message.SUCCESS,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async faq(req, res) {
		try {
			let content = await pageService.faq(req, res);
			if (content == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					content,
					message.SUCCESS,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
