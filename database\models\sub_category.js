'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class sub_category extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			sub_category.hasOne(models.pickup_location_sub_category, {
				targetKey: 'subCategoryID'
			});
		}
	}
	sub_category.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			categoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'category',
					key: 'id'
				}
			},
			name: DataTypes.STRING(255),
			posID: { type: DataTypes.STRING, defaultValue: null },
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false },
			isDeleted: { type: DataTypes.ENUM('Yes', 'No'), defaultValue: 'NO' }
		},
		{
			sequelize,
			modelName: 'sub_category',
			timestamps: true,
			freezeTableName: true
		}
	);
	return sub_category;
};
