require("dotenv").config();

const ejs = require("ejs");
const nodemailer = require("nodemailer");
const constant = require("../config/constant");
const message = require("../config/cmsMessage").cmsMessage;
const status = require("../config/status").status;

var welcomeMail = async function (res, email, subject, calendlyLink, rdName, rdBio) {
  try {
    var transporter = nodemailer.createTransport({
      service: constant.MAIL_SERVICE,
      host: constant.MAIL_HOST,
      port: constant.MAIL_PORT,

      secureConnection: false,
      auth: {
        user: constant.MAIL_FROM,
        pass: constant.MAIL_PASSWORD,
      },
      tls: {
        ciphers: 'SSLv3'
      }
    });

    let html_data = await ejs.renderFile(__dirname + "/welcomeEmailTemplate.ejs", {
      TITLE: subject,
      CALENDLYLINK: calendlyLink,
      RDNAME: rdName,
      RDBIO: rdBio
    });

    let mailoption = {
      from: '"' + constant.APP_NAME + '" <<EMAIL>>',
      to: email,
      html: html_data,
      subject: subject,
    };

    
    let mailresponse = await transporter.sendMail(mailoption);

    return mailresponse;
  } catch (error) {
    console.log("error-------", error);

    return response(
      res,
      status.INTERNALSERVERERRORSTATUS,
      [],
      message.INTERNALSERVERERROR,
      status.ERROR
    );
  }
};

exports.welcomeMail = welcomeMail;
