const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class user extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			user.hasMany(models.orders, { foreignKey: 'userID' });
		}
	}
	user.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			fullName: DataTypes.TEXT,
			email: DataTypes.TEXT,
			birthday: DataTypes.DATE,
			countryCode: DataTypes.TEXT,
			mobile: DataTypes.TEXT,
			password: DataTypes.TEXT,
			stripeID: DataTypes.TEXT,
			resetPasswordCode: DataTypes.TEXT,
			avatar: {
				type: DataTypes.TEXT
				// get() {
				//     console.log("AVATAR", this.getDataValue("avatar"))
				//     if (this.getDataValue("avatar") != "") {
				//         const contentValue =
				//             env.awsServerURL + "user/original/" + this.getDataValue("avatar");
				//         return contentValue;
				//     } else {
				//         return "";
				//     }
				// },
			},
			mobileVerified: DataTypes.ENUM('Yes', 'No'),
			badge: DataTypes.INTEGER,
			notification: DataTypes.ENUM('Yes', 'No'),
			status: DataTypes.ENUM('Active', 'Inactive'),
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No')
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'user',
			timestamps: true
		}
	);
	return user;
};
