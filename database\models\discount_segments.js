'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class discount_segments extends Model {
		static associate(models) {
			models.segment.hasMany(discount_segments, {
				foreignKey: 'segmentID'
			});
			discount_segments.belongsTo(models.segment, {
				foreignKey: 'segmentID'
			});

			models.discount.hasMany(discount_segments, {
				foreignKey: 'discountID'
			});
			discount_segments.belongsTo(models.discount, {
				foreignKey: 'discountID'
			});

			models.bar.hasMany(discount_segments, {
				foreignKey: 'barID'
			});
			discount_segments.belongsTo(models.bar, {
				foreignKey: 'barID'
			});
		}
	}
	discount_segments.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			segmentID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'segment',
					key: 'id'
				}
			},
			discountID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'discount',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			}
		},
		{
			sequelize,
			modelName: 'discount_segments',
			timestamps: false,
			freezeTableName: true
		}
	);
	return discount_segments;
};
