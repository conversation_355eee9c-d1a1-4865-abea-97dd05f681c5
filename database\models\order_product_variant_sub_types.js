const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
    class order_product_variant_sub_types extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            order_product_variant_sub_types.belongsTo(models.product_variant_sub_types, {
                foreignKey: "productVariantSubTypeID",
            });
            models.product_variant_types.hasOne(order_product_variant_sub_types, {
                foreignKey: "productVariantTypeID",
            });
            order_product_variant_sub_types.belongsTo(models.order_items, {
                foreignKey: "orderItemID",
            });
            models.order_items.hasMany(order_product_variant_sub_types, {
                foreignKey: "orderItemID",
            });
        }
    }
    order_product_variant_sub_types.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            orderItemID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "orderItems",
                    key: "id",
                },
            },
            orderProductVariantTypeID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "order_product_variant_types",
                    key: "id",
                },
            },
            productVariantTypeID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "product_variant_types",
                    key: "id",
                },
            },
            productVariantSubTypeID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "product_variant_sub_types",
                    key: "id",
                },
            },
            price: DataTypes.FLOAT,
            createdAt: DataTypes.DATE,
            updatedAt: DataTypes.DATE,
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: "order_product_variant_sub_types",
            timestamps: true,
        }
    );
    return order_product_variant_sub_types;
};
