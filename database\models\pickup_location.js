'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class pickup_location extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			// pos_conf.belongsTo(models.admin, {
			//   foreignKey: "assigned_to",
			//   targetKey: "id",
			//   onDelete: "CASCADE",
			// });
			pickup_location.hasMany(models.pickup_location_sub_category, {
				targetKey: 'pickupLocationID'
			});
		}
	}
	pickup_location.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			description: DataTypes.TEXT,
			address: DataTypes.STRING,
			latitude: DataTypes.STRING(100),
			longitude: DataTypes.STRING(100),
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			isDefault: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '0'
			},
			isDeleted: { type: DataTypes.ENUM('Yes', 'No'), defaultValue: 'No' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'pickup_location',
			timestamps: true,
			freezeTableName: true
		}
	);
	return pickup_location;
};
