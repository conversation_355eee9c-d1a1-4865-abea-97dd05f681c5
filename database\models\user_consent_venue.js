'use strict';

const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class user_consent_venue extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			user_consent_venue.belongsTo(models.bar, { foreignKey: 'barID' });
			user_consent_venue.belongsTo(models.user, { foreignKey: 'userID' });
		}
	}
	user_consent_venue.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			userID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER(12),
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE
		},
		{
			sequelize,
			paranoid: true,
			underscored: false,
			freezeTableName: true,
			modelName: 'user_consent_venue',
			timestamps: true
		}
	);
	return user_consent_venue;
};
