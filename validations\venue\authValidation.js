const Joi = require('joi');

module.exports = {
	async registerValidation(req) {
		const schema = Joi.object({
			first_name: Joi.string().required().messages({
				'any.required': 'Please enter your first name.',
				'string.empty': 'Please enter your first name.'
			}),
			last_name: Joi.string().required().messages({
				'any.required': 'Please enter your last name.',
				'string.empty': 'Please enter your last name.'
			}),
			email: Joi.string().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			}),
			// address: Joi.string().required().messages({
			// 	'any.required': 'Address is required.',
			// 	'string.empty': 'Address is required.'
			// }),
			password: Joi.string().required().messages({
				'any.required': 'Please enter your password.',
				'string.empty': 'Please enter your password.'
			})
		});

		return schema.validate(req.body);
	},

	/* login validation */
	async loginValidation(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			}),
			password: Joi.string().required().messages({
				'any.required': 'Please enter your password.',
				'string.empty': 'Please enter your password.'
			})
		});
		return schema.validate(req.body);
	},
	async verifyMFAValidation(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			}),
			user_id: Joi.number().required().messages({
				'any.required': 'User id is required.',
				'number.base': 'User id is required.'
			}),
			device_token: Joi.number().optional().allow(''),
			code: Joi.number().required().messages({
				'any.required': 'Please enter your code.',
				'number.base': 'Please enter your code.'
			}),
			device_type: Joi.string().optional().valid('ios', 'android', 'web'),
			device_name: Joi.string().optional().allow(''),
			device_location: Joi.string().optional().allow('')
		});
		return schema.validate(req.body);
	},
	async verifyPasswordValidation(req) {
		const schema = Joi.object({
			password: Joi.string().required().messages({
				'any.required': 'Please enter your password.',
				'string.empty': 'Please enter your password.'
			})
		});
		return schema.validate(req.body);
	},

	//setup mfa
	async setupMFA(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			})
		});
		return schema.validate(req.body);
	},

	//forgot password
	async forgotPasswordValidation(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			})
		});
		return schema.validate(req.body);
	},
	//verify token
	async verifyTokensValidation(req) {
		const schema = Joi.object({
			token: Joi.string().required().messages({
				'any.required': 'Token is required.',
				'string.empty': 'Token is required.'
			})
		});
		return schema.validate(req.body);
	},

	//reset password
	async resetPasswordValidation(req) {
		const schema = Joi.object({
			password: Joi.string().required().messages({
				'any.required': 'Please enter your password.',
				'string.empty': 'Please enter your password.'
			}),
			otp_token: Joi.string().required().messages({
				'any.required': 'Token is required.',
				'string.empty': 'Token is required.'
			})
		});
		return schema.validate(req.body);
	},
	async editProfileValidation(req) {
		const schema = Joi.object({
			first_name: Joi.string().required().messages({
				'any.required': 'Please enter your first name.',
				'string.empty': 'Please enter your first name.'
			}),
			last_name: Joi.string().required().messages({
				'any.required': 'Please enter your last name.',
				'string.empty': 'Please enter your last name.'
			}),
			email: Joi.string().email().required().messages({
				'any.required': 'Please enter your email.',
				'string.email': 'Email you have entered is not valid.',
				'string.empty': 'Please enter your email.'
			}),
			mobile: Joi.string()
				.regex(/^[0-9]{7,15}$/)
				.optional()
				.messages({
					'string.pattern.base': `Mobile number must have 7 to 15 digits.`,
					'string.empty': 'Please enter your mobile number.'
				}),
			country_code: Joi.string().optional().max(5).messages({
				'string.base': 'Country code must be string.',
				'string.max': 'Country code must be less than or equal to 5 characters.'
			}),
			address: Joi.string().optional().allow(''),
			pronouns: Joi.string().optional().allow(''),
			image: Joi.string().optional().allow('')
		});
		return schema.validate(req.body);
	},

	async verifyMFAOtpValidation(req) {
		const schema = Joi.object({
			code: Joi.number().required().messages({
				'any.required': 'Please enter your code.',
				'number.base': 'Please enter your code.'
			})
		});
		return schema.validate(req.body);
	},
	//save subscription
	async saveSubscriptionValidation(req) {
		const schema = Joi.object({
			subscription_id: Joi.number().required().messages({
				'any.required': 'Subscription id is required.',
				'number.base': 'Subscription id is required.'
			})
		});
		return schema.validate(req.body);
	}
};
