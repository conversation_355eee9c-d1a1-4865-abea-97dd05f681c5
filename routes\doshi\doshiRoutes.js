const express = require('express');
const doshiController = require('../../controllers/doshi/doshiVenue');
const { doshiiConnectWebhook } = require('../../services/doshi/doshiVenue');
const Authorization = require('../../middleware/venueAuth').venueAuthorization;
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;
//const Authorization = require('../../middleware/')
const router = express.Router();

router.get('/check-venue-valid', doshiController.isVenueValid);
router.get('/get-menu', doshiController.getDoshiMenuItems);
router.post('/test', doshiController.doshiTestFuntion);
router.post('/connect/', doshiController.getDoshiiReferralLink);
router.post(
	'/connect/webhook',
	doshiController.verifyDoshiiWebhook,
	doshiController.doshiiConnectWebhook
);
router.post(
	'/menu/webhook',
	doshiController.verifyDoshiiWebhook,
	doshiController.doshiiMenuWebhook
);
router.post('/stripe/subscription', doshiController.stripeSubscription);
router.post(
	'/stripe/subscription/complete',
	doshiController.stripeCompleteSubscription
);
router.post(
	'/pos/enable',
	Authorization,
	IsVenueConnectedWithBar,
	doshiController.posSubscriptionsEnable
);
router.post(
	'/pos/disable',
	Authorization,
	IsVenueConnectedWithBar,
	doshiController.posSubscriptionDisable
);
// router.post('/stripe/subscription/webhook', doshiController.stripeWebhook);
router.post(
	'/order/webhook',
	doshiController.verifyDoshiiWebhook,
	doshiController.doshiiOrderWebhook
);
// router.post('/create-order', doshiController.createOrderInDoshii);
router.post('/getMenuFromDoshii', doshiController.getMenuFromDoshii);

module.exports = router;
