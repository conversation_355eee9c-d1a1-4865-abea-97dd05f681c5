var express = require("express");
var router = express.Router();

/* require for Authentication */
const Authorization = require('../../middleware/advertiserAuth').advertiserAuthorization;

/*require for security */
const SecurityController = require("../../controllers/advertiser/securityController");

/* Security API */
router.post(
  "/change-password",
  Authorization,
  SecurityController.changePassword
);

router.get(
  "/change-password-date",
  Authorization,
  SecurityController.changePasswordDate
);

router.get("/device-list", Authorization, SecurityController.getDevicelist);

router.post("/logout-device", Authorization, SecurityController.logoutDevice);

router.post("/verify-otp", Authorization, SecurityController.verifyOtp);
router.post('/contactUs', Authorization, SecurityController.contactUs);
module.exports = router;
