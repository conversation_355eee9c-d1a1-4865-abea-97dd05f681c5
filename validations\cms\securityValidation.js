const Joi = require("joi");

module.exports = {
  async logoutDeviceValidation(req) {
    const schema = Joi.object({
      id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.empty": "id is required",
      }),
    });
    return schema.validate(req.body);
  },
  async changePasswordValidation(req) {
    const schema = Joi.object({
      old_password: Joi.string().required().messages({
        "any.required": "Current Password is required",
        "string.empty": "Current Password is required",
      }),
      new_password: Joi.string().required().messages({
        "any.required": "New Password is required",
        "string.empty": "New Password is required",
      }),
    });
    return schema.validate(req.body);
  },
};
