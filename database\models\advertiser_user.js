'use strict';

const constant = require('../../config/constant');
const imageGet = require('../../middleware/multerAwsGet');
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
module.exports = (sequelize, DataTypes) => {
	class advertiser_user extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			advertiser_user.belongsToMany(models.bar, {
				through: models.venue_user_bar,
				foreignKey: 'user_id'
			});
			advertiser_user.belongsToMany(models.subscription, {
				through: models.venue_user_subscription,
				foreignKey: 'user_id'
			});			
		}
	}
	advertiser_user.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			profile_image: {
				type: DataTypes.STRING(555),
				get() {
					if (
						this.getDataValue('profile_image') != '' &&
						this.getDataValue('profile_image') != null
					) {						
						return (
							constant.AWSS3PRIVATEURL +
							constant.AWSS3ADVERTISERQRCODEFOLDER +
							this.getDataValue('profile_image')
						);
					} else {
						return '';
					}
				},
				defaultValue: null
			},
			business_name: DataTypes.STRING(255),
			business_url: DataTypes.STRING(255),
			acn_number: DataTypes.STRING(255),
			contact_name: DataTypes.STRING(255),
			email: DataTypes.STRING(255),
			password: DataTypes.STRING(255),
			password_updated_at: DataTypes.DATE,			
			mfa_code: DataTypes.STRING(200),
			mfa_qr_code: {
				type: DataTypes.STRING(200),
				get() {
					if (
						this.getDataValue('mfa_qr_code') != '' &&
						this.getDataValue('mfa_qr_code') != null
					) {
						return (
							constant.AWSS3PRIVATEURL +
							constant.AWSS3ADVERTISERQRCODEFOLDER +
							this.getDataValue('mfa_qr_code')
						);
					} else {
						return '';
					}
				}
			},
			notification: {
				type: DataTypes.ENUM('Yes', 'No'),
				defaultValue: 'Yes'
			},
			account_verified: {
				type: DataTypes.ENUM('New', 'Approved', 'Rejected'),
				defaultValue: 'New'
			},
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			timezone_id: DataTypes.INTEGER,
			timezone: DataTypes.STRING(255),
			otp_token: DataTypes.STRING(555)
		},
		{
			hooks: {
				beforeCreate: function (user, options) {
					if (user.password) {
						return bcrypt
							.hash(user.password, 10)
							.then((hash) => {
								user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				beforeUpdate: function (advertiser_user, options) {
					if (advertiser_user.password) {
						return bcrypt
							.hash(advertiser_user.password, 10)
							.then((hash) => {
								advertiser_user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				}
			},

			sequelize,
			paranoid: true,
			underscored: true,
			freezeTableName: true, 
			modelName: 'advertiser_user',
			timestamps: true
		}
	);
	return advertiser_user;
};
