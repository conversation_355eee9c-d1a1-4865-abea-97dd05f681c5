const dashboardService = require('../../services/venue/dashboardService');
const dashboardValidation = require('../../validations/venue/dashboardValidation');
const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;

module.exports = {
	async getVenueStatistics(req, res) {
		let data = await dashboardService.getVenueStatistics(req, res);

		if (data) {
			return response(
				res,
				status.SUCCESSSTATUS,
				data,
				message.DATAFETCHSUCESSFULLY,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getVenueStatisticsNew(req, res) {
		let list = await dashboardService.getVenueStatisticsNew(req, res);

		if (list) {
			return response(
				res,
				status.SUCCESSSTATUS,
				{ list },
				message.DATAFETCHSUCESSFULLY,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async serviceTypeByPercentage(req, res) {
		let list = await dashboardService.serviceTypeByPercentage(req, res);

		if (list) {
			return response(
				res,
				status.SUCCESSSTATUS,
				{ list },
				message.DATAFETCHSUCESSFULLY,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async ordersPerHours(req, res) {
		let list = await dashboardService.ordersPerHours(req, res);

		if (list) {
			return response(
				res,
				status.SUCCESSSTATUS,
				{ list },
				message.DATAFETCHSUCESSFULLY,
				status.SUCCESS
			);
		} else {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async serviceTypeByRevenue(req, res) {
		try {
			let list = await dashboardService.serviceTypeByRevenue(req, res);

			if (list) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{ list },
					message.DATAFETCHSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.BADREQUESTCODE,
					[],
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (err) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async customerCountByTime(req, res) {
		try {
			let list = await dashboardService.customerCountByTime(req, res);

			if (list) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{ list },
					message.DATAFETCHSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.BADREQUESTCODE,
					[],
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (err) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async mostOrderdItems(req, res) {
		try {
			let list = await dashboardService.mostOrderdItems(req, res);

			if (list) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{ list },
					message.DATAFETCHSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.BADREQUESTCODE,
					[],
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (err) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async totalRevenue(req, res) {
		try {
			let totalRevenue = await dashboardService.totalRevenue(req, res);

			if (totalRevenue) {
				return response(
					res,
					status.SUCCESSSTATUS,
					totalRevenue,
					message.DATAFETCHSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.BADREQUESTCODE,
					[],
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (err) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
