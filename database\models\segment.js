'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class segment extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			segment.hasMany(models.segment_user_venue, { foreignKey: 'segmentID' });
		}
	}
	segment.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			name: DataTypes.STRING(255),
			isParent: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '0'
			},
			isActive: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '1'
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'segment',
			timestamps: true,
			freezeTableName: true
		}
	);
	return segment;
};
