/*Messages,status code and services require*/
require('dotenv').config();
const moment = require('moment');
var apn = require('apn');
const JwtToken = require('jsonwebtoken');
const Sequelize = require('sequelize');
var FCM = require('fcm-notification');
const Op = Sequelize.Op;
const constant = require('../config/constant');
const UserModel = require('../database/models').user;
const BarOpeningHoursUTCModel =
	require('../database/models').bar_opening_hours_utc;
const OrderModel = require('../database/models').orders;
const UserAccessTokenModel = require('../database/models').user_accesstoken;
var pathFCM = __dirname + '/../assets/' + constant.FCM_FILE;
var fcm = new FCM(pathFCM);

const options = {
	token: {
		key: __dirname + '/../assets/AuthKey_778HQ6BCC2.p8', // optionally: fs.readFileSync('./certs/key.p8')
		keyId: constant.PUSHIOSKEYID,
		teamId: constant.PUSHIOSTEAMID
	},
	production: constant.PUSHIOSPRODUCTION == 'true' ? true : false // true for APN production environment, false for APN sandbox environment,
};

module.exports.checkBarIsOpen = (
	barHours,
	activeHours = null,
	inActiveHours = null,
	weekDay = null
) => {
	let isOpen;
	if (!barHours.length) return 0;

	if (activeHours && inActiveHours) {
		let lastDay = weekDay - 1;
		if (weekDay == 0) {
			lastDay = 6;
		}

		let todayData = barHours.find(
			(operating_hour) => operating_hour.weekDay == weekDay
		);
		let previousDayData = barHours.find(
			(operating_hour) => operating_hour.weekDay == lastDay
		);

		if (!todayData) {
			return 0;
		}
		if (
			todayData.openingHours <= activeHours &&
			todayData.closingHours >= activeHours &&
			todayData.openingHours <= inActiveHours &&
			todayData.closingHours >= inActiveHours
		) {
			isOpen = 1;
		} else if (
			todayData.openingHours >= todayData.closingHours &&
			todayData.openingHours <= activeHours &&
			todayData.closingHours <= inActiveHours
		) {
			isOpen = 1;
		} else if (
			previousDayData &&
			previousDayData.openingHours >= previousDayData.closingHours &&
			previousDayData.closingHours >= activeHours &&
			previousDayData.closingHours >= inActiveHours
		) {
			isOpen = 1;
		} else {
			isOpen = 0;
		}
	} else {
		let today = moment().tz('Australia/Perth').isoWeekday() - 1;
		let lastDay = today - 1;
		if (today == 0) {
			lastDay = 6;
		}

		let currentTime = moment().tz('Australia/Perth').format('HH:mm:ss');
		let todayData = barHours.find(
			(operating_hour) => operating_hour.weekDay === today
		);
		let previousDayData = barHours.find(
			(operating_hour) => operating_hour.weekDay === lastDay
		);

		if (!todayData) {
			return 0;
		}
		if (
			todayData.openingHours < currentTime &&
			todayData.closingHours > currentTime
		) {
			isOpen = 1;
		} else if (
			todayData.openingHours > todayData.closingHours &&
			todayData.openingHours < currentTime &&
			todayData.closingHours < currentTime
		) {
			isOpen = 1;
		} else if (
			previousDayData &&
			previousDayData.openingHours > previousDayData.closingHours &&
			previousDayData.closingHours > currentTime
		) {
			isOpen = 1;
		} else {
			isOpen = 0;
		}
	}
	return isOpen;
};

module.exports.checkBarIsOpenV2 = async (barID) => {
	try {
		const currentDateTimeUTC = moment().utc();
		const [currentDay, currentTime] = [
			moment(currentDateTimeUTC).isoWeekday() - 1,
			moment(currentDateTimeUTC).format('HH:mm:ss')
		];

		const openingHoursCount = await BarOpeningHoursUTCModel.count({
			attributes: [],
			where: {
				isClosed: 0,
				weekDay: currentDay,
				openingHours: { [Op.lte]: currentTime },
				closingHours: { [Op.gte]: currentTime },
				barID: barID
			}
		});

		return openingHoursCount > 0 ? 1 : 0;
	} catch (error) {
		console.log(error);
	}
};

// Validate ABN number
module.exports.validateABN = (abn) => {
	if (abn.length != 11 || isNaN(parseInt(abn))) return false;

	let weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
	let firstDigitProcessed = parseInt(abn.charAt(0)) - 1;
	let weighted = firstDigitProcessed * weighting[0];

	for (var i = 1; i < abn.length; i++) {
		weighted += parseInt(abn.charAt(i)) * weighting[i];
	}

	return weighted % 89 == 0;
};

// Validate ACN number
module.exports.validateACN = (acn) => {
	if (acn.length != 9 || isNaN(parseInt(acn))) return false;

	var weighting = [8, 7, 6, 5, 4, 3, 2, 1];
	var weighted = 0;
	for (var i = 0; i < weighting.length; i++) {
		weighted += parseInt(acn.charAt(i)) * weighting[i];
	}
	let checkDigit = 10 - (weighted % 10);
	checkDigit = checkDigit == 10 ? 0 : checkDigit;
	return checkDigit == acn[8];
};

module.exports.getDoshiAuthToken = async () => {
	let jwtString = JwtToken.sign(
		{
			clientId: constant.DOSHI_APP_CLIENT_ID,
			timestamp: moment().unix()
		},
		env.DOSHI_APP_CLIENT_SECRET
	); // default: HS256 encryption

	return jwtString;
};
//convert hour utc to austrellian hours
module.exports.convertHours = (hours) => {
	let convertedHours = 0;
	if (hours >= 16) {
		let difference = 24 - hours;
		while (difference > 0) {
			convertedHours++;
			difference--;
		}
		return convertedHours;
	}
	if (hours < 16) {
		return hours + 8;
	}
};
// Split time in specified size and return array
module.exports.timeChunkArray = (startTime, endTime, chunkSize) => {
	const result = [];
	let format = 'HH:mm';
	let prevTime = moment(startTime, format);
	let time = moment(startTime, format).add(chunkSize, 'minutes');

	while (
		moment(time).isBetween(moment(startTime, format), moment(endTime, format))
	) {
		result.push({
			startTime: moment(prevTime, format).format('HH:mm'),
			endTime: moment(time, format).format('HH:mm')
		});
		prevTime = moment(time, format);
		time = moment(time, format).add(chunkSize, 'minutes');
	}

	if (
		moment(time, format).format('HH:mm') ===
		moment(endTime, format).format('HH:mm')
	) {
		result.push({
			startTime: moment(prevTime, format).format('HH:mm'),
			endTime: moment(endTime, format).format('HH:mm')
		});
	} else if (moment(time, format) > moment(endTime, format)) {
		result.push({
			startTime: moment(prevTime, format).format('HH:mm'),
			endTime: moment(endTime, format).format('HH:mm')
		});
	}

	return result;
};

module.exports.orderStatusNotificationToUser = async (
	userID,
	orderID,
	orderStatus,
	message,
	notificationTitle = null
) => {
	let notificationData = await getUserDetails(userID);
	if (notificationData) {
		let orderData = await getOrderDetails(orderID);
		if (orderData) {
			orderData = JSON.parse(JSON.stringify(orderData));
			notificationData = JSON.parse(JSON.stringify(notificationData));
			notificationData.message = message;
			notificationData.title = notificationTitle
				? notificationTitle
				: 'Order Update';
			notificationData.orderID = orderID;
			notificationData.type = orderStatus;
			if (
				orderStatus == 'orderReady_pickupAlert' ||
				orderStatus == 'orderReady' ||
				orderStatus == 'orderReady_waiTime'
			) {
				notificationData.sound = 'myTab_notification_sound.wav';
			} else {
				notificationData.sound = 'default';
			}
			notificationData.orderStatus =
				orderData.refundStatus == 'No'
					? orderData.orderStatus
					: orderData.refundStatus;

			let androidUserDeviceTokens = await getAndroidUserDeviceTokens(userID);
			if (androidUserDeviceTokens.length > 0) {
				await sendPushToAllAndroidDevices(
					androidUserDeviceTokens,
					notificationData
				);
			}
			let iosUserDeviceTokens = await getIosUserDeviceTokens(userID);
			if (iosUserDeviceTokens.length > 0) {
				await sendFCMPusToAllIosDevices(iosUserDeviceTokens, notificationData);
			}
		}
	}
};

async function getUserDetails(userID) {
	try {
		const result = await UserModel.findOne({
			attributes: ['id', 'fullName', 'email', 'avatar', 'notification'],
			where: {
				id: userID,
				isDeleted: 'No',
				status: 'Active'
			}
		});
		return result;
	} catch (error) {
		return error;
	}
}

async function getOrderDetails(orderID) {
	try {
		const result = await OrderModel.findOne({
			attributes: [
				'id',
				'orderNo',
				'pickupCode',
				[
					Sequelize.literal(
						`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`
					),
					'tableCode'
				],
				// [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
				'orderStatus',
				'refundStatus'
			],
			where: {
				id: orderID
			}
		});
		return result;
	} catch (error) {
		return error;
	}
}

async function getAndroidUserDeviceTokens(userID) {
	try {
		const result = await UserAccessTokenModel.findAll({
			attributes: [
				[Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
				'accessToken',
				'userID',
				'deviceType'
			],
			where: {
				userID: userID,
				deviceType: 'android',
				deviceToken: {
					[Op.ne]: ''
				}
			},
			group: ['deviceToken']
		});
		return result;
	} catch (error) {
		return error;
	}
}

async function getIosUserDeviceTokens(userID) {
	try {
		const result = await UserAccessTokenModel.findAll({
			attributes: [
				[Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
				'accessToken',
				'userID',
				'deviceType'
			],
			include: [
				{
					model: UserModel
				}
			],
			where: {
				userID: userID,
				deviceType: 'ios',
				deviceToken: {
					[Op.ne]: ''
				}
			},
			group: ['deviceToken']
		});
		return result;
	} catch (error) {
		return error;
	}
}

async function sendPushToAllDevices(
	registrationIds,
	notificationData,
	deviceType = constant.PUSHIOSBUNDLEIDS
) {
	new Promise(async (resolve, reject) => {
		try {
			var apnProvider = new apn.Provider(options);
			for (const element of registrationIds) {
				var data = notificationData;
				var note = new apn.Notification();
				note.expiry = Math.floor(Date.now() / 1000) + 3600; // Expires 1 hour from now.
				// note.badge = element.user.badge + 1
				note.body = data.message;
				if (
					notificationData.type == 'orderReady_pickupAlert' ||
					notificationData.type == 'orderReady' ||
					notificationData.type == 'orderReady_waiTime'
				) {
					note.sound = 'myTab_notification_sound.wav';
				} else {
					note.sound = 'default';
				}
				note.title = data.type == 'chat' ? data.name : '';
				note.topic = deviceType;
				note.mutableContent = true;
				note.contentAvailable = 1;
				note.aps.data = data;
				if (data.type === 'docketPrint') {
					note.body = '';
					note.sound = '';
				}
				let result = await apnProvider.send(note, element.deviceToken);
				if (result.sent.length > 0) {
					try {
						// await user.update(
						//   {
						//     badge: element.user.badge + 1
						//   },
						//   {
						//     where: {
						//       userID: element.user.userID
						//     }
						//   }
						// )
					} catch (e) {
						console.log(e);
					}
				}
			}
			apnProvider.shutdown();
			resolve();
		} catch (error) {
			console.log(error);
		}
	});
}

async function sendFCMPusToAllIosDevices(registrationIds, notificationData) {
	new Promise((resolve, reject) => {
		try {
			let dataToSend = JSON.parse(JSON.stringify(notificationData));
			let sound = '';
			if (notificationData.sound == 'silent') {
				sound = '';
			} else if (notificationData.sound) {
				sound = notificationData.sound;
			} else {
				sound = 'default';
			}

			let title = '';
			if (notificationData.type == 'chat') {
				title = notificationData.name;
			} else {
				title = '';
			}

			var message;

			if (
				notificationData.type != 'docketPrint' &&
				notificationData.type != 'tableNoChanged'
			) {
				message = {
					apns: {
						payload: {
							aps: {
								sound: sound,
								contentAvailable: 1
							}
						}
					},
					notification: {
						title: title,
						body: notificationData.message
					},
					data: { content: JSON.stringify(dataToSend) }
				};
			} else {
				message = {
					apns: {
						headers: {
							'apns-priority': '5',
							'apns-push-type': 'background'
						},
						payload: {
							aps: {
								sound: '',
								contentAvailable: 1
							}
						}
					},
					notification: {
						title: title,
						body: ''
					},
					data: { content: JSON.stringify(dataToSend) }
				};
			}
			var tokens = [];
			registrationIds.forEach((token) => {
				tokens.push(token.deviceToken);
			});
			fcm.sendToMultipleToken(message, tokens, function (err, response) {
				if (err) {
					console.log('Something has gone wrong for Android!');
					console.log('err');
					reject(err);
				} else {
					resolve(response);
				}
			});
		} catch (error) {
			reject(error);
			console.log('Error Notification');
			console.log(error);
		}
	});
}

async function sendPushToAllAndroidDevices(registrationIds, data) {
	new Promise((resolve, reject) => {
		try {
			let dataToSend = JSON.parse(JSON.stringify(data));
			var message = {
				data: { content: JSON.stringify(dataToSend) }
			};
			var tokens = [];
			registrationIds.forEach((token) => {
				tokens.push(token.deviceToken);
			});

			fcm.sendToMultipleToken(message, tokens, function (err, response) {
				if (err) {
					console.log('Something has gone wrong for Android!');
					console.log('err');
					reject(err);
				} else {
					// console.log(response)
					resolve(response);
				}
			});
		} catch (error) {
			reject(error);
			console.log(error);
		}
	});
}

module.exports.convertOpeningHours = (timeSlot, timezone, weekDay) => {
	try {
		const localWeekDay = parseInt(weekDay, 10);
		const utcEntries = [];
		const localOpen = moment
			.tz(`${localWeekDay} ${timeSlot.openingHours}`, 'e HH:mm', timezone)
			.utc();
		const localClose = moment
			.tz(`${localWeekDay} ${timeSlot.closingHours}`, 'e HH:mm', timezone)
			.utc();
		const utcOpenDay = localOpen.isoWeekday() % 7;
		const utcCloseDay = localClose.isoWeekday() % 7;

		if (utcOpenDay === utcCloseDay) {
			utcEntries.push({
				openingHours: localOpen.format('HH:mm:ss'),
				closingHours: localClose.format('HH:mm:ss'),
				weekDay: utcOpenDay
			});
		} else {
			utcEntries.push(
				{
					openingHours: localOpen.format('HH:mm:ss'),
					closingHours: '23:59:59',
					weekDay: utcOpenDay
				},
				{
					openingHours: '00:00:00',
					closingHours: localClose.format('HH:mm:ss'),
					weekDay: utcCloseDay
				}
			);
		}

		return utcEntries;
	} catch (error) {
		console.error('Error in converting opening hours:', error);
		return [];
	}
};
