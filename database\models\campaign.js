'use strict';

const constant = require('../../config/constant');
const imageGet = require('../../middleware/multerAwsGet');
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
module.exports = (sequelize, DataTypes) => {
	class campaign extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			campaign.belongsToMany(models.bar, {
				through: models.venue_user_bar,
				foreignKey: 'user_id'
			});
			campaign.belongsToMany(models.subscription, {
				through: models.venue_user_subscription,
				foreignKey: 'user_id'
			});
			campaign.hasMany(models.ads, {
                foreignKey: 'campaign_id'
            });
		}
	}
	campaign.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			name: DataTypes.STRING(255),			
			created_by_type: {
				type: DataTypes.ENUM('venue', 'advertiser', 'cms')
			},
			created_by_id: {
				type: DataTypes.INTEGER,
				defaultValue: null,
			},
		},
		{
			hooks: {
				beforeCreate: function (user, options) {
					if (user.password) {
						return bcrypt
							.hash(user.password, 10)
							.then((hash) => {
								user.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				},
				beforeUpdate: function (campaign, options) {
					if (campaign.password) {
						return bcrypt
							.hash(campaign.password, 10)
							.then((hash) => {
								campaign.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				}
			},

			sequelize,
			paranoid: true,
			underscored: true,
			freezeTableName: true, 
			tableName: 'campaigns',
			modelName: 'campaign',
			timestamps: true
		}
	);
	return campaign;
};
