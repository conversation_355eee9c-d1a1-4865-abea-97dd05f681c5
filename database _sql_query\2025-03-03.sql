CREATE TABLE
  discount (
    `id` INT NOT NULL AUTO_INCREMENT,
    `barID` INT NOT NULL,
    `code` VARCHAR(150) NOT NULL,
    `type` ENUM ('manual', 'automatic') NOT NULL,
    `discountType` ENUM ('percentage', 'fixed') NOT NULL,
    `discountValue` DECIMAL(10, 2) NOT NULL,
    `startDate` DATETIME NOT NULL,
    `endDate` DATETIME NULL DEFAULT NULL,
    `isActive` ENUM ('0', '1') NOT NULL DEFAULT '1',
    `totalUsageLimit` INT NULL DEFAULT NULL,
    `perUserLimit` INT NULL DEFAULT NULL,
    `eligibilityType` ENUM ('all_users', 'segment_group', 'individual_users') NOT NULL,
    `combinedEligibility` ENUM ('0', '1') NOT NULL DEFAULT '0',
    `createdAt` DATETIME NOT NULL,
    `updatedAt` DATETIME NOT NULL,
    `deletedAt` DATETIME NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
  );

CREATE TABLE
  discount_segments (
    `id` INT NOT NULL AUTO_INCREMENT,
    `segmentID` INT NOT NULL,
    `discountID` INT NOT NULL,
    `barID` INT NOT NULL,
    PRIMARY KEY (`id`)
  );

CREATE TABLE
  discount_users (
    `id` INT NOT NULL AUTO_INCREMENT,
    `userID` INT NOT NULL,
    `discountID` INT NOT NULL,
    `barID` INT NOT NULL,
    PRIMARY KEY (`id`)
  );