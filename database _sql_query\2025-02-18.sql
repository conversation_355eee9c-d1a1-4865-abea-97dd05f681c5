ALTER TABLE `bar` ADD `pauseOrderStartTime` DATETIME NULL DEFAULT NULL AFTER `readPopup`, ADD `pauseOrderLimit` VARCHAR(255) NULL DEFAULT NULL AFTER `pauseOrderStartTime`;

ALTER TABLE `bar` ADD `timezone` VARCHAR(255) NOT NULL DEFAULT 'Australia/Perth' AFTER `pauseOrderLimit`;
ALTER TABLE `bar` ADD `matchCategoryOpeningHours` ENUM('Yes','No') NOT NULL DEFAULT 'Yes' AFTER `timezone`;
UPDATE `bar` SET `matchCategoryOpeningHours` = 'No';
CREATE TABLE IF NOT EXISTS `bar_opening_hours` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barID` int NOT NULL,
  `weekDay` smallint NOT NULL DEFAULT '0',
  `openingHours` time NOT NULL,
  `closingHours` time NOT NULL,
  `isClosed` tinyint NOT NULL DEFAULT '0',
  `timeZone` varchar(255) DEFAULT 'AWST',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `bar_opening_hours_utc` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barOpeningHoursID` int NOT NULL,
  `barID` int NOT NULL,
  `weekDay` smallint NOT NULL DEFAULT '0',
  `openingHours` time NOT NULL,
  `closingHours` time NOT NULL,
  `isClosed` tinyint NOT NULL DEFAULT '0',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `bar_sub_category_opening_hours` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barID` int NOT NULL,
  `subCategoryID` int NOT NULL,
  `weekDay` smallint NOT NULL DEFAULT '0',
  `openingHours` time NOT NULL,
  `closingHours` time NOT NULL,
  `isClosed` tinyint NOT NULL DEFAULT '0',
  `timeZone` varchar(255) DEFAULT 'AWST',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `bar_sub_category_opening_hours_utc` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barSubCategoryOpeningHoursID` int NOT NULL,
  `barID` int NOT NULL,
  `subCategoryID` int NOT NULL,
  `weekDay` smallint NOT NULL DEFAULT '0',
  `openingHours` time NOT NULL,
  `closingHours` time NOT NULL,
  `isClosed` tinyint NOT NULL DEFAULT '0',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);