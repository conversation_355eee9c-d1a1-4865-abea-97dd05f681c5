const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const customerService = require('../../services/venue/customerService.js');
const customerValidation = require('../../validations/venue/customerValidation.js');

module.exports = {
    /* Get Customer List */
    async list(req, res) {
        try {
            const valid = await customerValidation.getCustomerListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let CustomerList = await customerService.getCustomerList(req);

            if (CustomerList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Get Customer Details */
    async details(req, res) {
        try {
            const valid = await customerValidation.getCustomerDetailsValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let CustomerData = await customerService.getCustomerDetails(req);

            if (CustomerData) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerData,
                    message.CUSTOMERDETAILSFETCHED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Get Customer Order List */
    async orderList(req, res) {
        try {
            const valid = await customerValidation.getOrderListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let OrderList = await customerService.getOrderList(req);

            if (OrderList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    OrderList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Get Customer Segment List */
    async segmentList(req, res) {
        try {
            console.log("first")
            const valid = await customerValidation.getSegmentListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let OrderList = await customerService.getSegmentList(req);

            if (OrderList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    OrderList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    /* Get All Customer Segment List (bar_id optional) */
    async allSegmentList(req, res) {
        try {
            const valid = await customerValidation.getAllSegmentListValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            let OrderList = await customerService.getAllSegmentList(req);

            if (OrderList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    OrderList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Download Customers List */
    async download(req, res) {
        try {
            const valid = await customerValidation.downloadCustomerValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let CustomerList = await customerService.downloadCustomerList(req);

            if (CustomerList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerList,
                    message.DATA_EXPORTED_SUCCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            console.log(error);
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /* Customer Cron */
    async cron(req, res) {
        try {
            let CustomerList = await customerService.cron(req);

            if (CustomerList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    CustomerList,
                    message.LISTFETCHEDSUCESSFULLY,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    }
};
