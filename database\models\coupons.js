const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
    class coupons extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            coupons.belongsTo(models.bar, { foreignKey: "barID" });
            models.bar.hasMany(coupons, { foreignKey: "barID" });
            coupons.belongsTo(models.sub_category, { foreignKey: "subcategoryID" }); // 9. Promo codes - Subheading specific....
            models.sub_category.hasMany(coupons, { foreignKey: "subcategoryID" }); // 9. Promo codes - Subheading specific....
        }
    }
    coupons.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            barID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "bar",
                    key: "id",
                },
            },
            code: DataTypes.TEXT,
            name: DataTypes.TEXT,
            description: DataTypes.TEXT,
            max_uses: DataTypes.INTEGER,
            max_uses_user: DataTypes.INTEGER,
            discount_amount: DataTypes.FLOAT,
            is_fixed: DataTypes.ENUM("Yes", "No"),
            startsAt: DataTypes.DATEONLY,
            expiresAt: {
                type: DataTypes.DATEONLY,
                allowNull: true,
            },
            status: DataTypes.ENUM("Active", "Inactive"),
            createdAt: DataTypes.DATE,
            updatedAt: DataTypes.DATE,
            isDeleted: DataTypes.ENUM("Yes", "No"),
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: "coupons",
            timestamps: true,
        }
    );
    return coupons;
};
