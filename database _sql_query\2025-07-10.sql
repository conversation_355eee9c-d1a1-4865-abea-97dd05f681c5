ALTER TABLE `timezones` ADD `title` VARCHAR(50) NOT NULL AFTER `name`, ADD `status` ENUM('Active','Inactive') NOT NULL DEFAULT 'Active' AFTER `title`;
ALTER TABLE `timezones` ADD `sequence` INT NOT NULL AFTER `status`;
ALTER TABLE `ads` ADD `status` ENUM('Active','Inactive') NOT NULL DEFAULT 'Active' AFTER `ad_status`;
ALTER TABLE `ads` ADD `pause_by_admin` ENUM('Yes','No') NULL DEFAULT NULL AFTER `pause_status`;
ALTER TABLE `ads` ADD `deleted_by_admin` ENUM('Yes','No') NULL DEFAULT NULL AFTER `deleted_at`;
ALTER TABLE `ads` ADD `invoice_no` VARCHAR(100) NOT NULL AFTER `id`;