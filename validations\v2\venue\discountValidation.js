const Joi = require('joi');

module.exports = {
	async addDiscountValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Bar ID must be a number',
				'number.integer': 'Bar ID must be an integer',
				'number.positive': 'Bar ID must be positive',
				'any.required': 'Bar ID is required'
			}),
			code: Joi.string().trim().min(2).max(50).required().messages({
				'string.base': 'Code must be a string',
				'string.empty': 'Code cannot be empty',
				'string.min': 'Code must be at least {#limit} characters',
				'string.max': 'Code cannot exceed {#limit} characters',
				'any.required': 'Code is required'
			}),
			type: Joi.string().valid('manual', 'automatic').required().messages({
				'string.base': 'Type must be a string',
				'any.only': 'Type must be either manual or automatic',
				'any.required': 'Type is required'
			}),
			discount_type: Joi.string()
				.valid('percentage', 'fixed')
				.required()
				.messages({
					'string.base': 'Discount type must be a string',
					'any.only': 'Discount type must be either percentage or fixed',
					'any.required': 'Discount type is required'
				}),
			discount_value: Joi.number().positive().required().messages({
				'number.base': 'Discount value must be a number',
				'number.positive': 'Discount value must be positive',
				'any.required': 'Discount value is required'
			}),

			start_date: Joi.date().iso().required().messages({
				'date.base': 'Start date must be a valid date in yyyy-mm-dd format',
				'any.required': 'Start date is required'
			}),
			end_date: Joi.date().iso().min(Joi.ref('start_date')).messages({
				'date.base': 'End date must be a valid date in yyyy-mm-dd format',
				'date.min': 'End date must be greater than or equal to start date'
			}),
			total_usage_limit: Joi.number().integer().min(1),
			per_user_limit: Joi.string().valid('0', '1').required().messages({
				'any.only': 'per user limit must be either "0" or "1"',
				'any.required': 'per user limit is required'
			}),
			eligibility_type: Joi.string()
				.valid('all_users', 'segment_group', 'individual_users')
				.required()
				.messages({
					'string.base': 'Eligibility type must be a string',
					'any.only':
						'Eligibility type must be one of all_users, segment_group, or individual_users',
					'any.required': 'Eligibility type is required'
				}),
			segment_ids: Joi.when('eligibility_type', {
				is: 'segment_group',
				then: Joi.array()
					.items(Joi.number().integer().positive())
					.min(1)
					.required()
					.messages({
						'array.base': 'Segment IDs must be an array',
						'array.min': 'At least one segment ID is required',
						'any.required':
							'Segment IDs are required for segment_group eligibility type'
					}),
				otherwise: Joi.alternatives().try(
					Joi.array().items(Joi.number()),
					Joi.valid(null)
				)
			}),
			user_ids: Joi.when('eligibility_type', {
				is: 'individual_users',
				then: Joi.array()
					.items(Joi.number().integer().positive())
					.min(1)
					.required()
					.messages({
						'array.base': 'User IDs must be an array',
						'array.min': 'At least one user ID is required',
						'any.required':
							'User IDs are required for individual_users eligibility type'
					}),
				otherwise: Joi.alternatives().try(
					Joi.array().items(Joi.number()),
					Joi.valid(null)
				)
			}),
			combined_eligibility: Joi.string().valid('0', '1').required().messages({
				'any.only': 'combined eligibility must be either "0" or "1"',
				'any.required': 'combined eligibility is required'
			}),
			is_combined_discount: Joi.string().valid('0', '1').required().messages({
				'any.only': 'is combined discount must be either "0" or "1"',
				'any.required': 'is combined discount is required'
			})
		});
		return schema.validate(req.body);
	},

	async updateDiscountValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Bar ID must be a number',
				'number.integer': 'Bar ID must be an integer',
				'number.positive': 'Bar ID must be positive',
				'any.required': 'Bar ID is required'
			}),
			id: Joi.number().integer().positive().required().messages({
				'number.base': 'Discount ID must be a number',
				'number.integer': 'Discount ID must be an integer',
				'number.positive': 'Discount ID must be positive',
				'any.required': 'Discount ID is required'
			}),
			is_active: Joi.string().valid('0', '1').required().messages({
				'any.only': 'Is Active must be either "0" or "1"',
				'any.required': 'Is Active is required'
			})
		});
		return schema.validate(req.body);
	},

	async deleteDiscountValidation(req) {
		const schema = Joi.object({
			id: Joi.number().integer().positive().required().messages({
				'number.base': 'Discount ID must be a number',
				'number.integer': 'Discount ID must be an integer',
				'number.positive': 'Discount ID must be positive',
				'any.required': 'Discount ID is required'
			}),
			bar_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Bar ID must be a number',
				'number.integer': 'Bar ID must be an integer',
				'number.positive': 'Bar ID must be positive',
				'any.required': 'Bar ID is required'
			})
		});

		return schema.validate(req.body);
	},

	async getDiscountValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Bar ID must be a number',
				'number.integer': 'Bar ID must be an integer',
				'number.positive': 'Bar ID must be positive',
				'any.required': 'Bar ID is required'
			}),
			page: Joi.number().integer().min(1).messages({
				'number.base': 'Page must be a number',
				'number.integer': 'Page must be an integer',
				'number.min': 'Page must be at least 1'
			}),
			limit: Joi.number().integer().min(1).messages({
				'number.base': 'Limit must be a number',
				'number.integer': 'Limit must be an integer',
				'number.min': 'Limit must be at least 1'
			}),
			search: Joi.string().optional().allow(''),
			sort_by: Joi.string()
				.valid(
					'newest',
					'oldest',
					'alphabeticAsc',
					'alphabeticDesc',
					'most_used',
					'least_used'
				)
				.default('newest')
				.messages({
					'string.base': 'Sort by must be a string',
					'any.only':
						'Sort by must be one of newest, oldest, alphabeticAsc, alphabeticDesc, most_used, or least_used'
				}),
			otherwise: Joi.alternatives().try(Joi.string(), Joi.valid(null))
		});
		return schema.validate(req.body);
	},

	async getDiscountDetailsValidation(req) {
		const schema = Joi.object({
			discount_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Discount ID must be a number',
				'number.integer': 'Discount ID must be an integer',
				'number.positive': 'Discount ID must be positive',
				'any.required': 'Discount ID is required'
			}),
			bar_id: Joi.number().integer().positive().required().messages({
				'number.base': 'Bar ID must be a number',
				'number.integer': 'Bar ID must be an integer',
				'number.positive': 'Bar ID must be positive',
				'any.required': 'Bar ID is required'
			})
		});
		return schema.validate(req.body);
	}
};
