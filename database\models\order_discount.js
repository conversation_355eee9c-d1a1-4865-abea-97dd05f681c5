'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class order_discount extends Model {
		static associate(models) {
			models.orders.hasMany(order_discount, {
				foreignKey: 'orderID'
			});
			order_discount.belongsTo(models.orders, {
				foreignKey: 'orderID'
			});

			//add new order table relation
		}
	}
	order_discount.init(
		{
			id: {
				type: DataTypes.BIGINT,
				primaryKey: true,
				autoIncrement: true
			},
			orderID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'order',
					key: 'id'
				}
			},
			discountID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'discount',
					key: 'id'
				}
			},
			discountCode: {
				type: DataTypes.STRING(255),
				allowNull: false
			},
			discountType: {
				type: DataTypes.ENUM('percentage', 'fixed'),
				allowNull: false
			},
			type: {
				type: DataTypes.ENUM('manual', 'automatic'),
				allowNull: false
			},
			discountValue: {
				type: DataTypes.DECIMAL(10, 2),
				allowNull: false
			},
			discountAmount: {
				type: DataTypes.DECIMAL(10, 2),
				allowNull: false
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE }
		},
		{
			sequelize,
			modelName: 'order_discount',
			timestamps: false,
			freezeTableName: true
		}
	);
	return order_discount;
};
