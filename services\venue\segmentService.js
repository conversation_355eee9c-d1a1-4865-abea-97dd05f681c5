/*Messages,status code and services require*/
require('dotenv').config();
const Sequelize = require('sequelize');
const UserModel = require('../../database/models').user;
const SegmentModel = require('../../database/models').segment;
const SegmentUserVenueModel =
	require('../../database/models').segment_user_venue;
const { Parser } = require('json2csv');

module.exports = {
	async getSegmentList(req) {
		try {
			let barID = req.body.bar_id;
			let search = req.body.search;
			// Fetch all segments that are active
			const segmentList = await SegmentModel.findAll({
				where: {
					isActive: '1',
					...(search && { name: { [Sequelize.Op.like]: `%${search}%` } })
				}
			});

			// Fetch the total customer count and customer count per segment in one query using group by segmentID
			const segmentCounts = await SegmentUserVenueModel.findAll({
				attributes: [
					'segmentID',
					[Sequelize.fn('COUNT', Sequelize.col('segmentID')), 'customerCount']
				],
				where: { barID },
				group: ['segmentID'],
				raw: true
			});

			// Create a map of segmentID -> customerCount for quick lookup
			const segmentCountMap = segmentCounts.reduce((acc, row) => {
				acc[row.segmentID] = row.customerCount;
				return acc;
			}, {});

			// Get the total customer count for the specific bar
			const totalCustomerCount = segmentCountMap[1] || 0; // Assuming 1 is the parent segment ID.

			// Update the segment list with customer count and percentage in a single loop
			const updatedSegmentList = segmentList.map((segment) => {
				if (segment.dataValues.isParent == 0) {
					const customerCount = segmentCountMap[segment.id] || 0;
					const customerPercentage =
						totalCustomerCount > 0
							? ((customerCount / totalCustomerCount) * 100).toFixed(2)
							: '0.00';

					segment.dataValues.customerCount = customerCount;
					segment.dataValues.customerPercentage = customerPercentage;
				}
				return segment;
			});

			return { list: updatedSegmentList };
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
    async getAllSegmentList(req) {
        try {
            // let barID = req.body.bar_id;
            let search = req.body.search;
            // Fetch all segments that are active
            const segmentList = await SegmentModel.findAll({
                where: {
                    isActive: '1',
                    ...(search && { name: { [Sequelize.Op.like]: `%${search}%` } })
                }
            });

            // Fetch the total customer count and customer count per segment in one query using group by segmentID
            const segmentCounts = await SegmentUserVenueModel.findAll({
                attributes: [
                    'segmentID',
                    [Sequelize.fn('COUNT', Sequelize.col('segmentID')), 'customerCount']
                ],
                group: ['segmentID'],
                raw: true
            });

            // Create a map of segmentID -> customerCount for quick lookup
            const segmentCountMap = segmentCounts.reduce((acc, row) => {
                acc[row.segmentID] = row.customerCount;
                return acc;
            }, {});

            // Get the total customer count for the specific bar
            const totalCustomerCount = segmentCountMap[1] || 0; // Assuming 1 is the parent segment ID.

            // Update the segment list with customer count and percentage in a single loop
            const updatedSegmentList = segmentList.map((segment) => {
                if (segment.dataValues.isParent == 0) {
                    const customerCount = segmentCountMap[segment.id] || 0;
                    const customerPercentage =
                        totalCustomerCount > 0
                            ? ((customerCount / totalCustomerCount) * 100).toFixed(2)
                            : '0.00';

                    segment.dataValues.customerCount = customerCount;
                    segment.dataValues.customerPercentage = customerPercentage;
                }
                return segment;
            });

            return { list: updatedSegmentList };
        } catch (error) {
            console.log(error);
            throw error;
        }
    },
	async exportSegmentList(req) {
		try {
			const barID = req.body.bar_id;

			// Fetch active segments that are not parents
			const segmentList = await SegmentModel.findAll({
				where: { isActive: '1', isParent: '0' }
			});

			// Fetch customer counts for all segments at once using `group by` to avoid multiple `count()` queries
			const segmentCounts = await SegmentUserVenueModel.findAll({
				attributes: [
					'segmentID',
					[Sequelize.fn('COUNT', Sequelize.col('segmentID')), 'customerCount']
				],
				where: { barID },
				group: ['segmentID'],
				raw: true
			});

			// Create a map of segmentID -> customerCount for fast lookup
			const segmentCountMap = segmentCounts.reduce((acc, row) => {
				acc[row.segmentID] = row.customerCount;
				return acc;
			}, {});

			// Get the total customer count for the bar (usually from segmentID = 1)
			const totalCustomerCount = segmentCountMap[1] || 0; // Assuming 1 is the parent segment ID.

			// Prepare the data for CSV export
			const csvFormattedData = segmentList.map((segment) => {
				const customerCount = segmentCountMap[segment.id] || 0;
				const customerPercentage =
					totalCustomerCount > 0
						? ((customerCount / totalCustomerCount) * 100).toFixed(2)
						: '0.00';

				return {
					Name: segment.name,
					'Number of customers': customerCount,
					'% of total customers': customerPercentage
				};
			});

			// Define CSV fields
			const fields = ['Name', 'Number of customers', '% of total customers'];
			const json2csvParser = new Parser({
				fields,
				defaultValue: 'NA',
				includeEmptyRows: true
			});

			// Convert data to CSV and return the result
			return json2csvParser.parse(csvFormattedData);
		} catch (error) {
			console.error(error);
			throw error;
		}
	},
	async getSegmentDetails(req) {
		try {
			const barID = req.body.bar_id;
			const segmentID = req.body.segment_id;

			const segmentRecord = await SegmentModel.findOne({
				where: { id: segmentID }
			});

			const customerCounts = await SegmentUserVenueModel.findAll({
				attributes: [
					'segmentID',
					[Sequelize.fn('COUNT', Sequelize.col('segmentID')), 'customerCount']
				],
				where: { barID, segmentID: [1, segmentID] },
				group: ['segmentID'],
				raw: true
			});

			const customerCountMap = customerCounts.reduce((acc, row) => {
				acc[row.segmentID] = row.customerCount;
				return acc;
			}, {});

			const totalCustomerCount = customerCountMap[1] || 0;

			const segmentCustomerCount = customerCountMap[segmentID] || 0;

			segmentRecord.dataValues.segmentCustomerCount = segmentCustomerCount;
			segmentRecord.dataValues.customerPercentage =
				totalCustomerCount > 0
					? Number((segmentCustomerCount / totalCustomerCount) * 100).toFixed(2)
					: '0.00';

			return segmentRecord;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getCustomerList(req) {
		try {
			const {
				bar_id: barID,
				segment_id: segmentID,
				page = 1,
				sortBy,
				search = ''
			} = req.body;
			const perPage = 10;

			// Define the where clause for filtering users and venues
			let whereClause = { barID, segmentID };

			// Define the order by clause based on the sortBy parameter
			let orderByClause = [];
			switch (sortBy) {
				case 'newToOld':
					orderByClause = [[Sequelize.literal('createdAt DESC')]];
					break;
				case 'oldToNew':
					orderByClause = [[Sequelize.literal('createdAt ASC')]];
					break;
				case 'highestTotalOrder':
					orderByClause = [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'DESC'
						]
					];
					break;
				case 'lowestTotalOrder':
					orderByClause = [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'ASC'
						]
					];
					break;
				case 'alphabeticDesc':
					orderByClause = [
						[Sequelize.fn('TRIM', Sequelize.col('user.fullName')), 'DESC']
					];
					break;
				case 'alphabeticAsc':
					orderByClause = [
						[Sequelize.fn('TRIM', Sequelize.col('user.fullName')), 'ASC']
					];
					break;
				default:
					orderByClause = [];
					break;
			}

			// Construct the condition for searching fullName, if search is provided
			let userSearchCondition = {};
			if (search) {
				userSearchCondition = {
					fullName: {
						[Sequelize.Op.like]: `%${search}%` // Case-insensitive search
					}
				};
			}

			// Perform the query using Sequelize's findAndCountAll
			const result = await SegmentUserVenueModel.findAndCountAll({
				attributes: {
					// Include total order count as a virtual attribute
					include: [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'totalOrderCount'
						],
						[
							Sequelize.literal(
								'(SELECT ROUND(SUM(total),2) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'totalOrderAmount'
						]
					]
				},
				include: [
					{
						where: userSearchCondition,
						model: UserModel,
						attributes: ['id', 'fullName', 'mobile', 'email']
					}
				],
				where: whereClause, // Apply the barID and segmentID filters to SegmentUserVenueModel
				offset: (page - 1) * perPage,
				limit: perPage,
				order: orderByClause
			});

			// Return the count and rows
			return { count: result.count, rows: result.rows };
		} catch (error) {
			console.error('Error fetching customer list:', error);
			throw error;
		}
	},
	async exportCustomerList(req) {
		try {
			const {
				bar_id: barID,
				segment_id: segmentID,
				sortBy,
				search = ''
			} = req.body;

			// Define the where clause for filtering users and venues
			let whereClause = { barID, segmentID };

			// Define the order by clause based on the sortBy parameter
			let orderByClause = [];
			switch (sortBy) {
				case 'newToOld':
					orderByClause = [[Sequelize.literal('createdAt DESC')]];
					break;
				case 'oldToNew':
					orderByClause = [[Sequelize.literal('createdAt ASC')]];
					break;
				case 'highestTotalOrder':
					orderByClause = [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'DESC'
						]
					];
					break;
				case 'lowestTotalOrder':
					orderByClause = [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'ASC'
						]
					];
					break;
				case 'alphabeticDesc':
					orderByClause = [
						[Sequelize.fn('TRIM', Sequelize.col('user.fullName')), 'DESC']
					];
					break;
				case 'alphabeticAsc':
					orderByClause = [
						[Sequelize.fn('TRIM', Sequelize.col('user.fullName')), 'ASC']
					];
					break;
				default:
					orderByClause = [];
					break;
			}

			// Construct the condition for searching fullName, if search is provided
			let userSearchCondition = {};
			if (search) {
				userSearchCondition = {
					fullName: {
						[Sequelize.Op.like]: `%${search}%` // Case-insensitive search
					}
				};
			}

			// Perform the query using Sequelize's findAndCountAll
			const result = await SegmentUserVenueModel.findAll({
				attributes: {
					// Include total order count as a virtual attribute
					include: [
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'totalOrderCount'
						],
						[
							Sequelize.literal(
								'(SELECT ROUND(SUM(total),2) FROM orders WHERE orders.userId = segment_user_venue.userId and orders.barID = segment_user_venue.barID and orders.isCanceled = "No" and refundStatus != "Refunded")'
							),
							'totalOrderAmount'
						]
					]
				},
				include: [
					{
						where: userSearchCondition,
						model: UserModel,
						attributes: ['id', 'fullName', 'mobile', 'email']
					}
				],
				where: whereClause, // Apply the barID and segmentID filters to SegmentUserVenueModel
				order: orderByClause
			});
			const csvFormattedData = result.map((segmentUser) => {
				return {
					Name: segmentUser.user.fullName,
					Order: segmentUser.dataValues.totalOrderCount,
					'Total spent': segmentUser.dataValues.totalOrderAmount
				};
			});
			const fields = ['Name', 'Order', 'Total spent'];
			const json2csvParser = new Parser({
				fields,
				defaultValue: 'NA',
				includeEmptyRows: true
			});

			return json2csvParser.parse(csvFormattedData);
		} catch (error) {
			console.error('Error fetching customer list:', error);
			throw error;
		}
	}
};
