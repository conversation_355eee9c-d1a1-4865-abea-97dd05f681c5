const { Model } = require("sequelize");

module.exports = (sequelize, DataTypes) => {
    class order_product_variant_types extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            order_product_variant_types.belongsTo(models.product_variant_types, {
                foreignKey: "productVariantTypeID",
            });
            order_product_variant_types.belongsTo(models.order_items, {
                foreignKey: "orderItemID",
            });
            models.order_items.hasMany(order_product_variant_types, {
                foreignKey: "orderItemID",
            });
            order_product_variant_types.hasOne(
                models.order_product_variant_sub_types,
                {
                    foreignKey: "id",
                }
            );
        }
    }
    order_product_variant_types.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            orderItemID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "orderItems",
                    key: "id",
                },
            },
            productVariantTypeID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "product_variant_types",
                    key: "id",
                },
            },
            createdAt: DataTypes.DATE,
            updatedAt: DataTypes.DATE,
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: "order_product_variant_types",
            timestamps: true,
        }
    );
    return order_product_variant_types;
};
