'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class bar_pos_subscriptions extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			bar_pos_subscriptions.belongsTo(models.bar, {
				foreignKey: 'barID',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
		}
	}
	bar_pos_subscriptions.init(
		{
			id: {
				type: DataTypes.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subscription_id: DataTypes.STRING(255),
			payment_intent_id: DataTypes.STRING(255),
			payment_intent_client_secret: DataTypes.STRING(255),
			payment_status: DataTypes.STRING(255),
			subscription_status: DataTypes.STRING(255),
			start_date: {
				type: DataTypes.DATE
			},
			end_date: {
				type: DataTypes.DATE
			},
			createdAt: {
				type: DataTypes.DATE
			},
			updatedAt: {
				type: DataTypes.DATE,
				allowNull: false
			}
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'bar_pos_subscriptions',
			timestamps: true
		}
	);
	return bar_pos_subscriptions;
};
