const Joi = require("joi");

module.exports = {
  async getSubCategoryValidation(req) {
    const schema = Joi.object({
      category_id: Joi.string().optional().messages({}),
      bar_id: Joi.number().required().messages({
        "any.required": "bar id is required",
        "number.empty": "bar id is required",
      }),
    });
    return schema.validate(req.body);
  },
  async barSubCategorySequenceValidation(req) {
    const schema = Joi.object({
      sub_category_ids: Joi.array().items(Joi.number()).required().messages({
        "any.required": "subcategoryIds is required",
        "number.empty": "subcategoryIds is required",
      }),
      bar_id: Joi.number().required().messages({
        "any.required": "bar id is required",
        "number.empty": "bar id is required",
      }),
    });
    return schema.validate(req.body);
  },
  async getCategoryListValidation(req) {
    const schema = Joi.object({
      bar_id: Joi.number().required().messages({
        "any.required": "bar id is required",
        "number.empty": "bar id is required",
      }),
    });
    return schema.validate(req.body);
  },
  async getSubCategoryListValidation(req) {
    const schema = Joi.object({
      bar_id: Joi.number().required().messages({
        "any.required": "bar id is required",
        "number.empty": "bar id is required",
      }),
    });
    return schema.validate(req.body);
  },

  async getSubCategoryForManageMenuValidation(req) {
    const schema = Joi.object({
      bar_id: Joi.number().required().messages({
        'any.required': 'bar_id is required.',
        'number.empty': 'bar_id is required.'
      }),
      serviceType: Joi.string()
        .valid('PICKUP', 'TABLE', 'BOTH')
        .required()
        .messages({
          'string.empty': 'Service Type is required.',
          'any.only': 'Service Type must be [Takeaway,Table service,Both].'
        }),
    });
    return schema.validate(req.body);
  },
};
