const Joi = require('joi');

module.exports = {
	async getSegmentListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow('')
		});
		return schema.validate(req.body);
	},
    async getAllSegmentListValidation(req) {
        const schema = Joi.object({
            search: Joi.string()
                .messages({
                    'string.base': 'Search must be a string'
                })
                .allow('')
        });
        return schema.validate(req.body);
    },
	async getSegmentDetailsValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			segment_id: Joi.number().required().messages({
				'any.required': 'Segment id is required',
				'number.base': 'Segment id must be number'
			})
		});
		return schema.validate(req.body);
	},
	async getCustomerListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			segment_id: Joi.number().required().messages({
				'any.required': 'Segment id is required',
				'number.base': 'Segment id must be number'
			}),
			sortBy: Joi.string()
				.trim()
				.optional()
				.valid(
					'newToOld',
					'oldToNew',
					'highestTotalOrder',
					'lowestTotalOrder',
					'alphabeticAsc',
					'alphabeticDesc'
				)
				.messages({
					'string.base': 'Sort by must be a string'
				}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow(''),
			page: Joi.number().required().messages({
				'any.required': 'Page no is required',
				'number.base': 'Page no must be number'
			})
		});
		return schema.validate(req.body);
	},
	async exportCustomerListValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			segment_id: Joi.number().required().messages({
				'any.required': 'Segment id is required',
				'number.base': 'Segment id must be number'
			}),
			sortBy: Joi.string()
				.trim()
				.optional()
				.valid(
					'newToOld',
					'oldToNew',
					'highestTotalOrder',
					'lowestTotalOrder',
					'alphabeticAsc',
					'alphabeticDesc'
				)
				.messages({
					'string.base': 'Sort by must be a string'
				}),
			search: Joi.string()
				.messages({
					'any.required': 'Search is required',
					'string.base': 'Search must be a string'
				})
				.allow('')
		});
		return schema.validate(req.body);
	}
};
