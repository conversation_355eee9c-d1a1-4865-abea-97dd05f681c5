const orderService = require('../../services/venue/orderService');
const orderValidation = require('../../validations/venue/orderValidation');

const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;

module.exports = {
	/* Bar Order history */
	async barOrderListCount(req, res) {
		try {
			let barOrderListCount = await orderService.barOrderListCount(req, res);

			return response(
				res,
				status.SUCCESSSTATUS,
				barOrderListCount,
				message.ORDER_HISTORY_LIST_SUCCESSFULL,
				status.SUCCESS
			);
		} catch (error) {
			//response on internal server error
			console.log(error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Bar Order history */
	async barOrderHistory(req, res) {
		try {
			// validation
			const valid = await orderValidation.barOrderHistoryValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let orderHistoryList = await orderService.barOrderHistory(req, res);

			return response(
				res,
				status.SUCCESSSTATUS,
				orderHistoryList,
				message.ORDER_HISTORY_LIST_SUCCESSFULL,
				status.SUCCESS
			);
		} catch (error) {
			//response on internal server error
			console.log(error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Bar Order list */
	async barOrderList(req, res) {
		try {
			// validation
			const valid = await orderValidation.barOrderListValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}

			let orderList = await orderService.barOrderList(req, res);

			return response(
				res,
				status.SUCCESSSTATUS,
				orderList,
				message.ORDER_HISTORY_LIST_SUCCESSFULL,
				status.SUCCESS
			);
		} catch (error) {
			//response on internal server error
			console.log(error);
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* View order details */
	async orderView(req, res) {
		try {
			// validation
			const valid = await orderValidation.orderViewValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.orderView(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ORDERNOTFOUND,
					status.ERROR
				);
			} else if (orderDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					orderDetails,
					message.ORDER_DETAILS_FETCHED_SUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* order cancel */
	async orderCancel(req, res) {
		try {
			// validation
			const valid = await orderValidation.orderCancelValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.orderCancel(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ORDERNOTFOUND,
					status.ERROR
				);
			} else if (orderDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					orderDetails,
					message.ORDER_REFUNDED_SUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* update order item status update */
	async updateOrderItemStatus(req, res) {
		try {
			// validation
			const valid = await orderValidation.updateOrderItemStatusValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.updateOrderItemStatus(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ORDERNOTFOUND,
					status.ERROR
				);
			} else if (orderDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					orderDetails,
					message.ORDER_ITEM_STATUS_UPDATED_SUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* update order intoxicated */
	async orderIntoxicated(req, res) {
		try {
			// validation
			const valid = await orderValidation.orderIntoxicatedValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.orderIntoxicated(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ORDERNOTFOUND,
					status.ERROR
				);
			} else if (orderDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					orderDetails,
					message.ORDER_INTOXICATED_SUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* update order item refund amount */
	async updateOrderItemNewRefundAmount(req, res) {
		try {
			await orderService.updateOrderItemNewRefundAmount(req, res);
			return response(
				res,
				status.SUCCESSSTATUS,
				'Records updated successfully',
				status.SUCCESS
			);
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* update Order Item WaitTime */
	async updateOrderItemWaitTime(req, res) {
		try {
			// validation
			const valid = await orderValidation.updateOrderItemWaitTimeValidation(
				req
			);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.updateOrderItemWaitTime(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ORDERNOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.UPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* download order history*/
	async downloadOrderHistory(req, res) {
		try {
			// validation
			const valid = await orderValidation.downloadOrderHistoryValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.downloadOrderHistory(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			} else if (orderDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					orderDetails,
					message.ORDER_DATA_EXPORTED_SUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* ready for order pickup alert */
	async readyForPickupAlert(req, res) {
		try {
			// validation
			const valid = await orderValidation.readyForPickupAlertValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let orderDetails = await orderService.readyForPickupAlert(req, res);
			if (orderDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.READYFORPICKUPALERTSENT,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
