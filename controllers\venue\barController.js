const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const barService = require('../../services/venue/barService');
const barValidation = require('../../validations/venue/barValidation');

module.exports = {
	/* Change Password */
	async addVenueOperatingHours(req, res) {
		try {
			// validation
			const valid = await barValidation.addVenueOperatingHoursValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updated = await barService.addVenueOperatingHours(req, res);

			if (updated == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OPERATINGHOURSUPDATESUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async getVenueOperatingHours(req, res) {
		try {
			// validation
			const valid = await barValidation.getVenueOperatingHoursValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await barService.getVenueOperatingHours(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.HOURSLISTFETCHEDSUCESSFULLY,
					status.SUCCESS
				);
			} else if (data == 0) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.MISSINGHOURS,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async connectVenue(req, res) {
		try {
			// validation
			const valid = await barValidation.connectVenueValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await barService.connectVenue(req, res);
			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEALREADYCONNECTED,
					status.ERROR
				);
			} else if (data == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INCORRECTPASSWORD,
					status.ERROR
				);
			} else if (data == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else if (data == 3) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.EMAILSENTSUCCESSFULLY,
					status.SUCCESS
				);
			} else if (data == 4) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CONNECTVENUENOTALLOWED,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async venueOtpVerify(req, res) {
		try {
			// validation
			const valid = await barValidation.venueOtpVerifyValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let verificationData = await barService.venueOtpVerify(req, res);

			if (verificationData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else if (verificationData == 1) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEALREADYCONNECTED,
					status.ERROR
				);
			} else if (verificationData == 2) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OTPDIDNOTMATCH,
					status.ERROR
				);
			} else if (verificationData == 3) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.ACCOUNTLINKSUCCESS,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async sendVenueOtp(req, res) {
		try {
			// validation
			const valid = await barValidation.sendVenueOtpValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let otpSentData = await barService.sendVenueOtp(req, res);

			if (otpSentData == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.EMAILSENTSUCCESSFULLY,
					status.SUCCESS
				);
			} else if (otpSentData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async venueOtpVerifyOnly(req, res) {
		try {
			// validation
			const valid = await barValidation.venueOtpVerifyOnlyValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let verificationData = await barService.venueOtpVerifyOnly(req, res);

			if (verificationData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else if (verificationData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OTPDIDNOTMATCH,
					status.ERROR
				);
			} else if (verificationData == 3) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OTPVERIFIED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//get venue details
	async getVenueDetails(req, res) {
		try {
			// validation
			const valid = await barValidation.getVenueDetailsValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let venueData = await barService.getVenueDetails(req, res);

			if (venueData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else if (venueData) {
				return response(
					res,
					status.SUCCESSSTATUS,
					venueData,
					message.VENUEDATAFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async getTimezones(req, res) {
		try {
			const timezones = await barService.getTimezones();
	  
			if (timezones.length === 0) {				
				return response(
					res,
					status.INTERNALSERVERERRORSTATUS,
					[],
					"No timezones found",
					status.ERROR
				);
			}

			return response(
				res,
				status.SUCCESSSTATUS,
				timezones,
				message.TIMEZONEFETCHSUCCESSFULLY,
				status.SUCCESS
			);
		} catch (error) {
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				error.message,
				status.ERROR
			);
		}
	},

	//get venue details
	async updateVenueDetails(req, res) {
		try {
			// validation
			const valid = await barValidation.updateVenueDetailsValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let venueData = await barService.updateVenueDetails(req, res);

			if (venueData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INVALIDABNACNNUMBER,
					status.ERROR
				);
			} else if (venueData == 3) {
				return response(
					res,
					status.INTERNALSERVERERRORSTATUS,
					{ venueOpenFlag: 1 },
					message.VENUE_ISOPEN,
					status.CUSTOMERROR
				);
			} else if (venueData?.confirmChangeFlag) {
				let { switchedCount, serviceType, requestServiceType } = venueData;
				return response(
					res,
					status.SUCCESSSTATUS,
					{ popUpFlag: venueData?.popUpFlag },
					`You have ${switchedCount} orders in ${serviceType.toLowerCase()} service, these order will not be visible after you switch over to ${requestServiceType.toLowerCase()} service.`,
					status.SUCCESS
				);
			} else if (venueData == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEUPDATEDSUCESSFULLY,
					status.SUCCESS
				);
			} else if (venueData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.EMAILALEREADYEXIST,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//get venue mobile
	async updateVenueMobile(req, res) {
		try {
			// validation
			const valid = await barValidation.updateVenueMobileValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let venueData = await barService.updateVenueMobile(req, res);

			if (venueData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INVALIDMOBILENUMBER,
					status.ERROR
				);
			} else if (venueData == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEMOBILEUPDATEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//create venue details
	async createVenue(req, res) {
		try {
			// validation
			const valid = await barValidation.createVenueValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let venueData = await barService.createVenue(req, res);
			if (venueData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INVALIDABNACNNUMBER,
					status.ERROR
				);
			} else if (venueData == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEIMAGEREQUIRED,
					status.ERROR
				);
			} else if (venueData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEEMAILALEREADYEXIST,
					status.ERROR
				);
			} else if (venueData == 4) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CONNECTVENUENOTALLOWED,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					venueData,
					message.VENUEADDEDSUCESSFULLY,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	//contact us
	async contactUs(req, res) {
		try {
			// validation
			const valid = await barValidation.contactUsValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let isMailSent = await barService.contactUs(req, res);
			if (isMailSent == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CONTACTUSMAILSENTSUCESSFULLY,
					status.SUCCESS
				);
			} else if (isMailSent == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.EMAILFAILED,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* delete venue association */
	async delete(req, res) {
		try {
			// validation
			const valid = await barValidation.deleteValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let isVenueDeleted = await barService.delete(req, res);
			if (isVenueDeleted == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEDELETEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* delete venue */
	async deleteVenue(req, res) {
		try {
			// validation
			const valid = await barValidation.deleteValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let isVenueDeleted = await barService.deleteVenue(req, res);
			if (isVenueDeleted == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEDELETEDSUCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Get ServiceType */
	async getServiceType(req, res) {
		try {
			// validation
			const valid = await barValidation.getServiceTypeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let serviceType = await barService.getServiceType(req, res);
			if (serviceType == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else if (serviceType) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{ serviceType },
					message.SERVICETYPEFETCHEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Update ServiceType */
	async updateServiceType(req, res) {
		try {
			// validation
			const valid = await barValidation.updateServiceTypeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updateServiceType = await barService.updateServiceType(req, res);
			if (updateServiceType == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else if (updateServiceType == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					`Service Type is already ${serviceType}.`,
					status.SUCCESS
				);
			} else if (updateServiceType?.confirmChangeFlag) {
				let { switchedCount, serviceType, requestServiceType } =
					updateServiceType;
				return response(
					res,
					status.SUCCESSSTATUS,
					{ popUpFlag: updateServiceType?.popUpFlag },
					`You have ${switchedCount} orders in ${serviceType.toLowerCase()} service, these order will not be visible after you switch over to ${requestServiceType.toLowerCase()} service.`,
					status.SUCCESS
				);
			} else if (updateServiceType == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Get WaitTime ServiceType */
	async getWaitTimeServiceType(req, res) {
		try {
			// validation
			const valid = await barValidation.getWaitTimeServiceTypeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let waitTimeServiceType = await barService.getWaitTimeServiceType(
				req,
				res
			);
			if (waitTimeServiceType === 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					waitTimeServiceType,
					message.SERVICETYPEFETCHEDSUCCESSFULLY,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Update WaitTime ServiceType */
	async updateWaitTimeServiceType(req, res) {
		try {
			// validation
			const valid = await barValidation.updateWaitTimeServiceTypeValidation(
				req
			);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updateWaitTimeServiceType =
				await barService.updateWaitTimeServiceType(req, res);
			if (updateWaitTimeServiceType == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else if (updateWaitTimeServiceType == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Get sub heading wait time */
	async getSubHeadingWaitTime(req, res) {
		try {
			// validation
			const valid = await barValidation.getSubHeadingWaitTimeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let getSubHeadingWaitTime = await barService.getSubHeadingWaitTime(
				req,
				res
			);
			if (getSubHeadingWaitTime == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOWAITTIMEFOUND,
					status.ERROR
				);
			} else if (getSubHeadingWaitTime) {
				return response(
					res,
					status.SUCCESSSTATUS,
					getSubHeadingWaitTime,
					message.WAITTIMEGETSUCCESSFULLY,
					status.SUCCESS
				);
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Update sub heading wait time */
	async updateSubHeadingWaitTime(req, res) {
		try {
			// validation
			const valid = await barValidation.updateSubHeadingWaitTimeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updateServiceType = await barService.updateSubHeadingWaitTime(
				req,
				res
			);
			if (updateServiceType == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.WAITTIMEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOWAITTIMEFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Change Password */
	async changePassword(req, res) {
		try {
			// validation
			const valid = await barValidation.changePasswordValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let changePassword = await barService.changePassword(req, res);

			if (changePassword == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PASSWORDCHANGED,
					status.SUCCESS
				);
			} else if (changePassword == 2) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INCORRECTOLDPASSWORD,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Change PassCode */
	async changePassCode(req, res) {
		try {
			// validation
			const valid = await barValidation.changePassCodeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let changePassCode = await barService.changePassCode(req, res);

			if (changePassCode == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PASSCODECHANGED,
					status.SUCCESS
				);
			} else if (changePassCode == 2) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.INCORRECTOLDPASSCODE,
					status.ERROR
				);
			} else if (changePassCode == 3) {
				//response on passcode status mismatch
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CHANGEPASSCODESTATUSFIRST,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Update PassCode Status */
	async updatePassCodeStatus(req, res) {
		try {
			// validation
			const valid = await barValidation.updatePassCodeStatusValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updatePassCodeStatus = await barService.updatePassCodeStatus(
				req,
				res
			);

			if (updatePassCodeStatus == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PASSCODESTATUSCHANGED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Set PassCode */
	async setPassCode(req, res) {
		try {
			// validation
			const valid = await barValidation.setPassCodeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let setPassCode = await barService.setPassCode(req, res);

			if (setPassCode == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PASSCODECHANGED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/* Verify PassCode */
	async verifyPassCode(req, res) {
		try {
			// validation
			const valid = await barValidation.verifyPassCodeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let verifyPassCode = await barService.verifyPassCode(req, res);

			if (verifyPassCode == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.PASSCODEVERIFY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VALIDPASSCODE,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async venueOtpSent(req, res) {
		try {
			// validation
			const valid = await barValidation.venueOtpSentValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await barService.venueOtpSent(req, res);
			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUEEMAILALEREADYEXIST,
					status.ERROR
				);
			} else if (data == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.EMAILSENTSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async createVenueOtpVerify(req, res) {
		try {
			// validation
			const valid = await barValidation.venueOtpVerifyValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let verificationData = await barService.createVenueOtpVerify(req, res);

			if (verificationData == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.VENUENOTFOUND,
					status.ERROR
				);
			} else if (verificationData == 1) {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OTPDIDNOTMATCH,
					status.ERROR
				);
			} else if (verificationData == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.OTPVERIFIED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	async checkStripeIntegration(req, res) {
		try {
			// validation
			const valid = await barValidation.checkStripeIntegrationValidation(req);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await barService.checkStripeIntegration(req, res);
			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else if (data == -1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			} else {
				if (data?.isRegister == 1) {
					return response(
						res,
						status.SUCCESSSTATUS,
						data,
						message.CREATESTRIPEACCOUNT,
						status.SUCCESS
					);
				} else if (data?.isRegister == 0) {
					return response(
						res,
						status.SUCCESSSTATUS,
						data,
						message.STRIPEACCOUNTINFORMATION,
						status.SUCCESS
					);
				} else {
					return response(
						res,
						status.SUCCESSSTATUS,
						{},
						message.SOMETHINGWENTWRONG,
						status.ERROR
					);
				}
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	async completeStripeIntegration(req, res) {
		try {
			// validation
			const valid = await barValidation.completeStripeIntegrationValidation(
				req
			);

			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await barService.completeStripeIntegration(req, res);
			if (data == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.BARNOTFOUND,
					status.ERROR
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.COMPLETESTRIPEACCOUNT,
					status.SUCCESS
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
