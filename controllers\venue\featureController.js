const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const featureService = require('../../services/venue/featureService');
const featureValidation = require('../../validations/venue/featureValidation');

module.exports = {
	async changeStatus(req, res) {
		try {
			let isChageStatus = await featureService.changeStatus(req, res);
			if (isChageStatus) {
				return response(
					res,
					status.SUCCESSSTATUS,
					isChageStatus,
					isChageStatus.currentStatus == 0
						? message.DOCKETFEATUREDEACTIVATED
						: message.DOCKETFEATUREACTIVATED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.ERROR,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
