const Joi = require('joi');

module.exports = {
	async addVenueOperatingHoursValidation(req) {
		const schema = Joi.object({
			operating_hours: Joi.array()
				.length(7)
				.required()
				.items(
					Joi.object({
						week_day: Joi.number()
							.valid(0, 1, 2, 3, 4, 5, 6)
							.required()
							.messages({
								'any.required': 'Weekday is required.',
								'string.empty': 'Weekday is required.',
								'any.only': 'Weekday number must be between 0 to 6.'
							}),
						opening_hours: Joi.string().required().messages({
							'any.required': 'Opening Hours is required.',
							'string.empty': 'Opening Hours is required.'
						}),

						closing_hours: Joi.string().required().messages({
							'any.required': 'Closing Hours is required.',
							'string.empty': 'Closing Hours is required.'
						}),
						is_closed: Joi.boolean().required().messages({
							'any.required': 'Is Closed is required.',
							'boolean.empty': 'Is Closed is required.'
						})
					})
				),
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getVenueOperatingHoursValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async connectVenueValidation(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			}),
			password: Joi.string().required().messages({
				'any.required': 'Password is required.',
				'string.empty': 'Password is required.'
			})
		});
		return schema.validate(req.body);
	},
	async venueOtpVerifyValidation(req) {
		const schema = Joi.object({
			otp: Joi.number().required().messages({
				'any.required': 'Otp is required.',
				'number.empty': 'Otp is required.'
			}),
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			})
		});
		return schema.validate(req.body);
	},
	async sendVenueOtpValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async venueOtpVerifyOnlyValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			otp: Joi.number().required().messages({
				'any.required': 'Otp is required.',
				'number.empty': 'Otp is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getVenueDetailsValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async updateVenueDetailsValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			venue_name: Joi.string().optional().messages({}),
			latitude: Joi.string().optional().messages({}),
			longitude: Joi.string().optional().messages({}),
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			}),
			image: Joi.string().optional().messages({}),
			manager_name: Joi.string().optional().messages({}),
			venue_address: Joi.string().optional().messages({}),
			country_code: Joi.string().required().max(5).messages({
				'any.required': 'Please select country code.',
				'string.base': 'Country code must be string.',
				'string.max': 'Country code must be less than or equal to 5 characters.'
			}),
			mobile: Joi.string()
				.regex(/^[0-9]{7,15}$/)
				.required()
				.messages({
					'string.pattern.base': `Mobile number must have 7 to 15 digits.`,
					'string.empty': 'Mobile number is required.'
				}),
			business_register_id: Joi.string().optional().messages({
				'any.required': 'ABN/ACN number is required',
				'string.empty': 'ABN/ACN number is required'
			}),
			is_venue_serve_alcohol: Joi.string()
				.valid('Yes', 'No')
				.optional()
				.messages({
					'string.empty': 'isVenueServeAlcohol is required.',
					'any.only': 'isVenueServeAlcohol must be Yes or No.'
				}),
			liquor_license_number: Joi.string().optional().messages({
				'string.empty': 'liquor License Number is required.'
			}),
			password: Joi.string().optional().messages({
				'string.empty': 'Password is required.'
			}),
			timezone: Joi.string().optional().messages({
				'string.empty': 'Timezone is required.'
			}),
			operating_hours: Joi.array()
				.optional()
				.items(
					Joi.object({
						id: Joi.number().required().messages({
							'any.required': 'Id is required.',
							'number.empty': 'Id is required.'
						}),
						opening_hours: Joi.string().required().messages({
							'any.required': 'Opening Hours is required.',
							'string.empty': 'Opening Hours is required.'
						}),
						closing_hours: Joi.string().required().messages({
							'any.required': 'Closing Hours is required.',
							'string.empty': 'Closing Hours is required.'
						}),
						is_closed: Joi.boolean().required().messages({
							'any.required': 'isClosed is required.',
							'boolean.empty': 'isClosed is required.'
						})
					})
				),
			service_type: Joi.string()
				.valid('TABLE', 'PICKUP', 'BOTH')
				.required()
				.messages({
					'any.required': 'Service Type is required.',
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be Takeaway, Table Service or Both.'
				}),
			confirm_change_flag: Joi.number().required().messages({
				'any.required': 'Confirm Change Flag is required.',
				'number.empty': 'Confirm Change Flag is required.'
			})
		});
		return schema.validate(req.body);
	},
	async updateVenueMobileValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			old_country_code: Joi.string().required().max(5).messages({
				'any.required': 'Please select country code.',
				'string.base': 'Country code must be string.',
				'string.max': 'Country code must be less than or equal to 5 characters.'
			}),
			old_mobile: Joi.string()
				.regex(/^[0-9]{7,15}$/)
				.required()
				.messages({
					'string.pattern.base': `Mobile number must have 7 to 15 digits.`,
					'string.empty': 'Mobile number is required.'
				}),
			country_code: Joi.string().required().max(5).messages({
				'any.required': 'Please select country code.',
				'string.base': 'Country code must be string.',
				'string.max': 'Country code must be less than or equal to 5 characters.'
			}),
			mobile: Joi.string()
				.regex(/^[0-9]{7,15}$/)
				.required()
				.messages({
					'string.pattern.base': `Mobile number must have 7 to 15 digits.`,
					'string.empty': 'Mobile number is required.'
				})
		});
		return schema.validate(req.body);
	},
	async createVenueValidation(req) {
		const schema = Joi.object({
			venue_name: Joi.string().required().messages({
				'any.required': 'Venue name is required.',
				'string.empty': 'Venue name is required.'
			}),
			manager_name: Joi.string().required().messages({
				'any.required': 'Manager name is required.',
				'string.empty': 'Manager name is required.'
			}),
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			}),
			venue_address: Joi.string().required().messages({
				'any.required': 'Venue address is required.',
				'string.empty': 'Venue address is required.'
			}),
			latitude: Joi.string().optional().messages({}),
			longitude: Joi.string().optional().messages({}),
			country_code: Joi.string().required().max(5).messages({
				'any.required': 'Please select country code.',
				'string.base': 'Country code must be string.',
				'string.max': 'Country code must be less than or equal to 5 characters.'
			}),
			mobile: Joi.string()
				.regex(/^[0-9]{8,15}$/)
				.required()
				.messages({
					'any.required': 'Mobile number is required.',
					'string.pattern.base': `Mobile number must have 8 to 15 digits.`,
					'string.empty': 'Mobile number is required.'
				}),
			business_register_id: Joi.string().required().messages({
				'any.required': 'ABN/ACN number is required',
				'string.empty': 'ABN/ACN number is required'
			}),
			is_venue_serve_alcohol: Joi.string()
				.valid('Yes', 'No')
				.required()
				.messages({
					'any.required': 'isVenueServeAlcohol is required.',
					'string.empty': 'isVenueServeAlcohol is required.',
					'any.only': 'isVenueServeAlcohol must be Yes or No.'
				}),
			liquor_license_number: Joi.string().optional().messages({
				'string.empty': 'Liquor License Number is required.'
			}),
			password: Joi.string().required().messages({
				'any.required': 'Password is required.',
				'string.empty': 'Password is required.'
			}),
			timezone: Joi.string().optional().messages({
				'string.empty': 'Timezone is required.'
			}),
			service_type: Joi.string()
				.valid('PICKUP', 'TABLE', 'BOTH')
				.required()
				.messages({
					'any.required': 'Service type is required.',
					'string.empty': 'Service type is required.',
					'any.only': 'Service type must be [PICKUP,TABLE,BOTH].'
				}),
			operating_hours: Joi.array()
				.length(7)
				.required()
				.items(
					Joi.object({
						week_day: Joi.number()
							.valid(0, 1, 2, 3, 4, 5, 6)
							.required()
							.messages({
								'any.required': 'Weekday is required.',
								'string.empty': 'Weekday is required.',
								'any.only': 'Weekday number must be between 0 to 6.'
							}),
						opening_hours: Joi.string().required().messages({
							'any.required': 'Opening Hours is required.',
							'string.empty': 'Opening Hours is required.'
						}),

						closing_hours: Joi.string().required().messages({
							'any.required': 'Closing Hours is required.',
							'string.empty': 'Closing Hours is required.'
						}),

						is_closed: Joi.boolean().required().messages({
							'any.required': 'isClosed is required.',
							'boolean.empty': 'isClosed is required.'
						})
					})
				)
		});
		return schema.validate(req.body);
	},
	async contactUsValidation(req) {
		const schema = Joi.object({
			name: Joi.string().required().messages({
				'any.required': 'Name is required.',
				'string.empty': 'Name is required.'
			}),
			message: Joi.string().required().messages({
				'any.required': 'Message is required.',
				'string.empty': 'Message is required.'
			}),
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar id is required.'
			}),
			mobile: Joi.string().optional()
		});
		return schema.validate(req.body);
	},
	async deleteValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getServiceTypeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async updateServiceTypeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			service_type: Joi.string()
				.valid('TABLE', 'PICKUP', 'BOTH')
				.required()
				.messages({
					'any.required': 'Service Type is required.',
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be Takeaway, Table Service or Both.'
				}),
			confirm_change_flag: Joi.number().required().messages({
				'any.required': 'Confirm Change Flag is required.',
				'number.empty': 'Confirm Change Flag is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getWaitTimeServiceTypeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async updateWaitTimeServiceTypeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			}),
			waitTimeServiceType: Joi.string()
				.valid('TABLE', 'PICKUP', 'BOTH', '')
				.required()
				.messages({
					'any.required': 'Service Type is required.',
					'string.empty': 'Service Type is required.',
					'any.only': 'Service Type must be Takeaway, Table Service or Both.'
				})
		});
		return schema.validate(req.body);
	},
	async getSubHeadingWaitTimeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			sub_category_id: Joi.number().required().messages({
				'any.required': 'Sub Category id is required.',
				'number.empty': 'Sub Category id must be number.'
			})
		});
		return schema.validate(req.body);
	},
	async updateSubHeadingWaitTimeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			sub_category_id: Joi.number().required().messages({
				'any.required': 'Sub Category id is required.',
				'number.empty': 'Sub Category id must be number.'
			}),
			type: Joi.number().required().messages({
				'any.required': 'Type id is required.',
				'number.empty': 'Type id must be number.'
			}),
			data: Joi.required().messages({
				'any.required': 'Data is required.'
			})
		});
		return schema.validate(req.body);
	},
	async changePasswordValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			old_password: Joi.string().required().messages({
				'any.required': 'Old Password is required.',
				'string.empty': 'Old Password is required.'
			}),
			new_password: Joi.string().required().messages({
				'any.required': 'New Password is required.',
				'string.empty': 'New Password is required.'
			})
		});
		return schema.validate(req.body);
	},
	async changePassCodeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			old_passcode: Joi.string().required().messages({
				'any.required': 'Old Passcode is required.',
				'string.empty': 'Old Passcode is required.'
			}),
			passcode: Joi.string().required().messages({
				'any.required': 'Passcode is required.',
				'string.empty': 'Passcode is required.'
			})
		});
		return schema.validate(req.body);
	},
	async updatePassCodeStatusValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			passCodeStatus: Joi.string().required().messages({
				'any.required': 'PassCode status is required.',
				'string.empty': 'PassCode Status must be string.'
			})
		});
		return schema.validate(req.body);
	},
	async setPassCodeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			passcode: Joi.string().required().messages({
				'any.required': 'Passcode is required.',
				'string.empty': 'Passcode is required.'
			})
		});
		return schema.validate(req.body);
	},
	async verifyPassCodeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			passcode: Joi.string().required().messages({
				'any.required': 'Passcode is required.',
				'string.empty': 'Passcode is required.'
			})
		});
		return schema.validate(req.body);
	},
	async venueOtpSentValidation(req) {
		const schema = Joi.object({
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			}),
			manager_name: Joi.string().required().messages({
				'any.required': 'Manager name is required.',
				'string.empty': 'Manager name is required.'
			})
		});
		return schema.validate(req.body);
	},
	async checkStripeIntegrationValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			})
		});
		return schema.validate(req.body);
	},
	async completeStripeIntegrationValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			})
		});
		return schema.validate(req.body);
	}
};
