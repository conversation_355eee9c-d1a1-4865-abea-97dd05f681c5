"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class venue_user_role_permission extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      venue_user_role_permission.hasOne(models.venue_user_permission, {
        foreignKey: "id",
        targetKey: "venue_user_permission_id",
        onDelete: "CASCADE",
      });
    }
  }
  venue_user_role_permission.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      venue_user_role_id: DataTypes.INTEGER,
      venue_user_permission_id: DataTypes.INTEGER,
      view: DataTypes.BOOLEAN,
      add: DataTypes.BOOLEAN,
      edit: DataTypes.BOOLEAN,
      delete: DataTypes.BOOLEAN,
      status: DataTypes.BOOLEAN,
    },
    {
      sequelize,
      paranoid: true,
      timestamps: true,
      modelName: "venue_user_role_permission",
      underscored: true,
      freezeTableName: true,
    }
  );
  return venue_user_role_permission;
};
