const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;
const rolePermissionService = require("../../services/venue/rolespermissService");
const rolePermissionValidation = require("../../validations/venue/rolepermissionValidation");

module.exports = {
  async listPermission(req, res) {
    let listPermissions = await rolePermissionService.listPermission(req);
    try {
      return response(
        res,
        status.SUCCESSSTATUS,
        listPermissions,
        message.PERMISSIONLIST,
        status.SUCCESS
      );
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  async listRolePermission(req, res) {
    let listRolePermissions = await rolePermissionService.listRolePermission(
      req
    );
    try {
      return response(
        res,
        status.SUCCESSSTATUS,
        listRolePermissions,
        message.ROLEPERMISSIONLIST,
        status.SUCCESS
      );
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  async addRolePermission(req, res) {
    const valid = await rolePermissionValidation.addRolePermissionValidation(
      req
    );
    if (valid.error) {
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        valid.error.message,
        status.ERROR
      );
    }
    let addRolePermissions = await rolePermissionService.addRolePermission(req);
    try {
      return response(
        res,
        status.SUCCESSSTATUS,
        addRolePermissions,
        message.ROLECREATED,
        status.SUCCESS
      );
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  async updateRolePermission(req, res) {
    const valid = await rolePermissionValidation.updateRolePermissionValidation(
      req
    );
    if (valid.error) {
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        valid.error.message,
        status.ERROR
      );
    }
    await rolePermissionService.updateRolePermission(req);
    try {
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        message.ROLEUPDATED,
        status.SUCCESS
      );
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  async deleteRolePermission(req, res) {
    const valid = await rolePermissionValidation.deleteRolePermissionValidation(
      req
    );
    if (valid.error) {
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        valid.error.message,
        status.ERROR
      );
    }
    let rolePermission = await rolePermissionService.deleteRolePermission(req);
    try {
      if (rolePermission === 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.ROLENOTFOUND,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.ROLEDELETED,
          status.SUCCESS
        );
      }
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },

  async changeStatusRolePermission(req, res) {
    const valid =
      await rolePermissionValidation.changeStatusRolePermissionValidation(req);
    if (valid.error) {
      return response(
        res,
        status.SUCCESSSTATUS,
        {},
        valid.error.message,
        status.ERROR
      );
    }
    let rolePermission = await rolePermissionService.changeStatusRolePermission(
      req
    );
    try {
      if (rolePermission === 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.ROLENOTFOUND,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.ROLESTATUSCHANGE,
          status.SUCCESS
        );
      }
    } catch (error) {
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        {},
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
};
