const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;
const todoService = require("../../services/cms/todoService");
const todoValidation = require("../../validations/cms/todoValidation");

module.exports = {
  /* addTodo */
  async add(req, res) {
    try {
      // validation
      const valid = await todoValidation.addValidation(req);

      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      let addTodo = await todoService.add(req, res);
      if (addTodo == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TASKCREATED,
          status.SUCCESS
        );
      } else {
        //response on old password mis-match
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*deleteTodo */
  // async deleteTodo(req, res) {
  //   try {
  //     // validation
  //     const valid = await todoValidation.deleteTodovalidation(req);

  //     if (valid.error) {
  //       return response(
  //         res,
  //         status.BADREQUESTCODE,
  //         {},
  //         valid.error.details[0].message,
  //         status.ERROR
  //       );
  //     }

  //     let deleteTodo = await todoService.deleteTodo(req, res);
  //     if (deleteTodo == 0) {
  //       return response(
  //         res,
  //         status.SUCCESSSTATUS,
  //         {},
  //         message.TASKNOTEXIST,
  //         status.SUCCESS
  //       );
  //     } else if (deleteTodo == 1) {
  //       return response(
  //         res,
  //         status.SUCCESSSTATUS,
  //         {},
  //         message.TASKDELETED,
  //         status.SUCCESS
  //       );
  //     } else {
  //       return response(
  //         res,
  //         status.SUCCESSSTATUS,
  //         {},
  //         message.SOMETHINGWENTWRONG,
  //         status.ERROR
  //       );
  //     }
  //   } catch (error) {
  //     //response on internal server error
  //     return response(
  //       res,
  //       status.INTERNALSERVERERRORSTATUS,
  //       [],
  //       message.INTERNALSERVERERROR,
  //       status.ERROR
  //     );
  //   }
  // },
  /*Update todo */
  async update(req, res) {
    try {
      // validation
      const valid = await todoValidation.updateValidation(req);

      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      let updateTodo = await todoService.update(req, res);
      if (updateTodo == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TASKNOTEXIST,
          status.SUCCESS
        );
      } else if (updateTodo == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TASKUPDATED,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*list todo */
  async list(req, res) {
    try {
      // validation
      const valid = await todoValidation.listValidation(req);

      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      let listTodo = await todoService.list(req, res);

      if (listTodo) {
        return response(
          res,
          status.SUCCESSSTATUS,
          { listTodo },
          message.TASKLISTFETCHSUCCESSFULLY,
          status.SUCCESS
        );
      } else if (listTodo) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*Update todo */
  async updateStatus(req, res) {
    try {
      // validation
      const valid = await todoValidation.updateStatusValidation(req);

      if (valid.error) {
        return response(
          res,
          status.BADREQUESTCODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }

      let updateTodo = await todoService.updateStatus(req, res);
      if (updateTodo == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TASKNOTEXIST,
          status.SUCCESS
        );
      } else if (updateTodo == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TASKUPDATED,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
};
