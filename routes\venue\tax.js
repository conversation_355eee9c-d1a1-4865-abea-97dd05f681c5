var express = require('express');
var router = express.Router();

/* require for Authentication */
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;

/*require for pickuplocation  response*/
const TaxController = require('../../controllers/venue/taxController');

/* Tax add API */
router.post(
	'/add',
	venueAuthorization,
	IsVenueConnectedWithBar,
	TaxController.add
);

/* Tax list API */
router.post(
	'/list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	TaxController.list
);

/* Tax edit API */
router.put(
	'/edit',
	venueAuthorization,
	IsVenueConnectedWithBar,
	TaxController.edit
);

/* Tax delete API */
router.delete(
	'/delete',
	venueAuthorization,
	IsVenueConnectedWithBar,
	TaxController.delete
);

module.exports = router;
