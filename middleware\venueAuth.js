/*
 * Summary:     auth middleware user file.
 * Author:      Openxcell
 */
const VenueUserToken = require('../database/models').venue_user_accesstoken; //include venue user token model
const commonFunction = require('../common/commonFunction');
const OperatingHoursModel = require('../database/models').operating_hour; //include venue user token model
const VenueUserBarModel = require('../database/models').venue_user_bar;
const JWT = require('jsonwebtoken');
const constant = require('../config/constant');
const message = require('../config/cmsMessage');
const status = require('../config/status').status;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

exports.venueAuthorization = async (req, res, next) => {
	if (req.headers.authorization) {
		try {
			let jwtGetVenueUserDetail = await JWT.verify(
				req.headers.authorization,
				constant.JWTTOKEN.secret,
				{
					algorithm: constant.JWTTOKEN.algo,
					expiresIn: constant.JWTTOKEN.expiresIn
				}
			);
			let getVenueUserAuthDetails = await VenueUserToken.findOne({
				where: {
					access_token: req.headers.authorization,
					user_id: jwtGetVenueUserDetail.user_id
				}
			});

			if (getVenueUserAuthDetails) {
				req.user_id = getVenueUserAuthDetails.dataValues.user_id;
				req.email = jwtGetVenueUserDetail.email;
				next();
			} else {
				let resData = {
					data: '',
					status: status.UNAUTHORIZEDUSER
				};
				return res.status(401).send(resData);
			}
		} catch (error) {
			console.log('TCL: exports.authenticationApi -> error', error);
			let resData = {
				data: '',
				status: status.UNAUTHORIZEDUSER
			};
			return res.status(401).send(resData);
		}
	} else {
		return response(
			res,
			status.UNAUTHORIZEDUSER,
			{},
			message.cmsMessage.TOKENREQUIRED,
			status.ERROR
		);
	}
};
exports.isOperatingHoursValid = async (req, res, next) => {
	try {
		const openrationgHoursList = await OperatingHoursModel.findAll({
			attributes: ['weekDay', 'openingHours', 'closingHours', 'isClosed'],
			where: {
				barID: req.body.bar_id,
				isClosed: 0
			}
		});
		let isVenueClosed = commonFunction.checkBarIsOpen(
			openrationgHoursList,
			req.body.active_hours,
			req.body.in_active_hours,
			req.body.week_day
		);

		if (!isVenueClosed) {
			return res.status(200).json({
				success: -2,
				message:
					'The selected time is outside of your venues operating hours, please try again.'
			});
		} else {
			next();
		}
	} catch (error) {
		console.log(error);
		return res.status(500).json({
			success: 0,
			message: 'Something went wrong, please try after sometime.',
			data: {}
		});
	}
};
exports.isVenueUpdatableController = async (req, res, next) => {
	try {
		const operatingHoursList = await OperatingHoursModel.findAll({
			attributes: ['weekDay', 'openingHours', 'closingHours', 'isClosed'],
			where: {
				barID: req.body.bar_id,
				isClosed: 0
			}
		});

		let isVenueUpdatable = commonFunction.checkBarIsOpen(operatingHoursList);
		if (isVenueUpdatable) {
			return res.status(500).json({
				success: -2,
				message:
					"Your venue is still open, updating your service type is only available outside your venue's opening hours.",
				data: { venueOpenFlag: 1 }
			});
		} else {
			next();
		}
	} catch (error) {
		console.log(error);
		return res.status(500).json({
			success: 0,
			message: 'Something went wrong, please try after sometime.',
			data: {}
		});
	}
};
exports.isVenueUserConnectedWithBar = async (req, res, next) => {
	try {
		if (!req.body.bar_id) {
			return res.status(200).json({
				success: -2,
				message: 'barId is required'
			});
		}
		let isVenueConnectedWithBar = await VenueUserBarModel.findOne({
			where: { user_id: req.user_id, bar_id: req.body.bar_id }
		});
		if (!isVenueConnectedWithBar) {
			return res.status(200).json({
				success: -2,
				message: 'Your Account is not connected with this venue'
			});
		}
		next();
	} catch (error) {
		console.log(error);
		return res.status(500).json({
			success: 0,
			message: 'Something went wrong, please try after sometime.',
			data: {}
		});
	}
};
