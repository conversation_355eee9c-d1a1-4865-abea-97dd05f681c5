"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class advertiser_user_accesstoken extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      advertiser_user_accesstoken.belongsTo(models.advertiser_user, {
        foreignKey: "user_id",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  advertiser_user_accesstoken.init(
    {
      id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: "advertiser_user",
          key: "id",
        },
      },
      device_token: {
        type: DataTypes.STRING(255),
      },
      access_token: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      device_type: DataTypes.ENUM("ios", "android", "web"),

      device_location: {
        type: DataTypes.STRING(255),
      },
      device_name: {
        type: DataTypes.STRING(255),
      },
    },
    {
      sequelize,
      underscored: true,
      freezeTableName: true,
      modelName: "advertiser_user_accesstoken",
      timestamps: true,
    }
  );
  return advertiser_user_accesstoken;
};
