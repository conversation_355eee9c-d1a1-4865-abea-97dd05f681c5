const reportService = require("../../services/venue/reportService");
const reportValidation = require("../../validations/venue/reportValidation");

const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;

module.exports = {

    /*item summary report */
    async itemsummaryreport(req, res) {
        try {
            // validation
            const valid = await reportValidation.itemsummaryreportValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let orderDetails = await reportService.itemsummaryreport(req, res);
            if (orderDetails) {

                return response(
                    res,
                    status.SUCCESSSTATUS,
                    orderDetails,
                    message.ITEMSUMMARYREPORTFETCHEDSUCCESSFULLY,
                    status.SUCCESS
                );
            }
            else {
                return response(
                    res,
                    status.INTERNALSERVERERRORSTATUS,
                    [],
                    message.INTERNALSERVERERROR,
                    status.ERROR
                );
            }

        } catch (error) {
            console.log("errror", error)
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    /*item summary report */
    async downloaditemsummaryreport(req, res) {
        try {
            // validation
            const valid = await reportValidation.downloaditemsummaryreportValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            let orderDetails = await reportService.downloaditemsummaryreport(req, res);

            if (orderDetails) {
                res.attachment("Item_Summary_Report.csv");
                res.send(orderDetails);

            }
            else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );

            }

        } catch (error) {
            console.log("err", error)
            //response on internal server error
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

    async ListCategory(req, res) {
        let list = await reportService.ListCategory(req, res);
        if (list) {
            return response(
                res,
                status.SUCCESSSTATUS,
                { list },
                message.SUBCATEGORY_LIST,
                status.SUCCESS
            );

        }
        else {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }


    }
};
