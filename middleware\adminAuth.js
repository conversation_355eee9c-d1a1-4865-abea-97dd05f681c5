/*
 * Summary:     auth middleware user file.
 * Author:      Openxcell
 */

const AdminToken = require("../database/models").admin_login_session; //include admin token model
const Admin = require("../database/models").admin; //include user model

const JWT = require("jsonwebtoken");
const constant = require("../config/constant");
const message = require("../config/cmsMessage");
const status = require("../config/status").status;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;

exports.adminAuthorization = async (req, res, next) => {

  if (req.headers.authorization) {
    try {
      let jwtGetUserDetail =  JWT.verify(
        req.headers.authorization,
        constant.JWTTOKEN.secret,
        {
          algorithm: constant.JWTTOKEN.algo,
          expiresIn: constant.JWTTOKEN.expiresIn,
        }
      );
      let getUserAuthDetails = await AdminToken.findOne({
        where: {
          access_token: req.headers.authorization,
          admin_id: jwtGetUserDetail.admin_id,
          // "$admin.deleted_at$": null,
          // "$admin.status$": {
          //   [Op.in]: [
          //     constant.ACTIVE_NEW_CLIENT,
          //     constant.ACTIVE_NOT_NEW_CLIENT,
          //   ],
          // },
          // "$admin.is_block$": constant.INACTIVE,
          // "$admin.is_approved$": constant.APPROVED,
        },
        include: [
          {
            model: Admin,
          },
        ],
      });

      if (getUserAuthDetails) {
        req.admin_id = getUserAuthDetails.admin_id;
        req.token = getUserAuthDetails.token;
        next();
      } else {
        return response(
          res,
          status.UNAUTHORIZEDUSER,
          {},
          message.cmsMessage.UNAUTHORIZEDUSER,
          status.ERROR
        );
      }
    } catch (error) {
      console.log("TCL: exports.authenticationApi -> error", error);
      return response(                          
        res,
        status.UNAUTHORIZEDUSER,
        {},
        message.cmsMessage.UNAUTHORIZEDUSER,
        status.ERROR
      );
    }
  } else {
    return response(
      res,
      status.UNAUTHORIZEDUSER,
      {},
      message.cmsMessage.TOKENREQUIRED,
      status.ERROR
    );
  }
};
