/*
 * Summary:     index.js file for handling all routes, request and response for admin panel - (CMS related actions).
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require('express');
var router = express.Router();

/* require for Authentication */
const AuthRouter = require('./auth');
const SecurityRouter = require('./security');
const CampaignRouter = require('./campaign');
const AdsRouter = require('./ads');
const reportsRouter = require('./reports');

/* Routes of auth*/
router.use('/auth', AuthRouter);

/* Routes of security*/
router.use('/security', SecurityRouter);
router.use('/campaign', CampaignRouter);
router.use('/ads/', AdsRouter);
router.use('/reports/', reportsRouter);

module.exports = router;
