'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class discount extends Model {
		static associate(models) {
			models.bar.hasMany(discount, {
				foreignKey: 'barID'
			});
			discount.belongsTo(models.bar, {
				foreignKey: 'barID'
			});

			discount.hasMany(models.discount_segments, {
				foreignKey: 'discountID',
				as: 'segments'
			});

			discount.hasMany(models.discount_users, {
				foreignKey: 'discountID',
				as: 'eligibleUsers'
			});
			//add new order table relation
		}
	}
	discount.init(
		{
			id: {
				type: DataTypes.BIGINT,
				primaryKey: true,
				autoIncrement: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			code: {
				type: DataTypes.STRING(255),
				allowNull: false
			},
			type: {
				type: DataTypes.ENUM('manual', 'automatic'),
				allowNull: false
			},
			discountType: {
				type: DataTypes.ENUM('percentage', 'fixed'),
				allowNull: false
			},
			discountValue: {
				type: DataTypes.DECIMAL(10, 2), //CHANGE IT
				allowNull: false
			},
			startDate: {
				type: DataTypes.DATE,
				allowNull: false
			},
			endDate: {
				type: DataTypes.DATE,
				allowNull: true
			},
			isActive: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '1'
			},
			is_combined_discount: {
				type: DataTypes.ENUM('0', '1'),
				defaultValue: '0'
			},
			totalUsageLimit: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			perUserLimit: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			eligibilityType: {
				type: DataTypes.ENUM('all_users', 'segment_group', 'individual_users'),
				allowNull: false
			},
			combinedEligibility: {
				type: DataTypes.ENUM('0', '1'),
				allowNull: true,
				defaultValue: '0'
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE },
			deletedAt: { type: DataTypes.DATE }
		},
		{
			sequelize,
			modelName: 'discount',
			timestamps: true,
			paranoid: true,
			freezeTableName: true
		}
	);
	return discount;
};
