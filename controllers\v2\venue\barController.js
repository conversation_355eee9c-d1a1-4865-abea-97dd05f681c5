const message = require('../../../config/cmsMessage').cmsMessage;
const status = require('../../../config/status').status;
const barService = require('../../../services/v2/venue/barService');
const barValidation = require('../../../validations/v2/venue/barValidation');

module.exports = {
	//get account delete form
	async getAccountDeleteForm(req, res) {
		try {
			//validation
			const valid = await barValidation.getAccountDeleteFormValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let formDetails = await barService.getAccountDeleteForm(req, res);
			if (formDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			} else if (formDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					formDetails,
					message.ACCOUNTDELETEFORMFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	//get product tax
	async getProductTax(req, res) {
		try {
			let taxDetails = await barService.getProductTax(req, res);
			if (taxDetails == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NODATAFOUND,
					status.ERROR
				);
			} else if (taxDetails) {
				return response(
					res,
					status.SUCCESSSTATUS,
					taxDetails,
					message.PRODUCTTAXFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	/* Get sub heading wait time */
	async getSubHeadingWaitTime(req, res) {
		try {
			// validation
			const valid = await barValidation.getSubHeadingWaitTimeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let getSubHeadingWaitTime = await barService.getSubHeadingWaitTime(
				req,
				res
			);
			if (getSubHeadingWaitTime == 0) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOWAITTIMEFOUND,
					status.ERROR
				);
			} else if (getSubHeadingWaitTime) {
				return response(
					res,
					status.SUCCESSSTATUS,
					getSubHeadingWaitTime,
					message.WAITTIMEGETSUCCESSFULLY,
					status.SUCCESS
				);
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			console.log(error);
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},

	/* Update sub heading wait time */
	async updateSubHeadingWaitTime(req, res) {
		try {
			// validation
			const valid = await barValidation.updateSubHeadingWaitTimeValidation(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let updateServiceType = await barService.updateSubHeadingWaitTime(
				req,
				res
			);
			if (updateServiceType == 1) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.WAITTIMEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SERVICETYPEUPDATEDSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.NOWAITTIMEFOUND,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
