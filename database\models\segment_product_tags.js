'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class segment_product_tags extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			models.product.hasMany(segment_product_tags, {
				foreignKey: 'productID'
			});
			segment_product_tags.belongsTo(models.product, {
				foreignKey: 'productID'
			});
			models.segment_tags.hasMany(segment_product_tags, {
				foreignKey: 'tagID'
			});
			segment_product_tags.belongsTo(models.segment_tags, {
				foreignKey: 'tagID'
			});
		}
	}
	segment_product_tags.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			productID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'product',
					key: 'id'
				}
			},
			tagID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'segment_tags',
					key: 'id'
				}
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE },
			deletedAt: { type: DataTypes.DATE }
		},

		{
			sequelize,
			modelName: 'segment_product_tags',
			timestamps: true,
			freezeTableName: true,
			paranoid: true
		}
	);
	return segment_product_tags;
};
