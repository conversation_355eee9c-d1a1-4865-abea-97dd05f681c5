/*Messages,status code and services require*/
require('dotenv').config();
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const AdvertiserUserModel = require('../../database/models').advertiser_user;
const CampaignModel = require('../../database/models').campaign;
const AdsModel = require('../../database/models').ads;
const AdvertiserUserTempModel = require('../../database/models').advertiser_user_temp;
const models = require('../../database/models');
const generateRandomString = require('../../helper/generalHelper').generateRandomString;
const AdvertiserUserTokenModel = require('../../database/models').advertiser_user_accesstoken;
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const { default: jwtDecode } = require('jwt-decode');
const { status } = require('../../config/status');
const { generateAdvertiserToken } = require('../../helper/authHelper');
const moment = require('moment');
const UserModel = require('../../database/models').user;

module.exports = {
    /*add */
    async add(req, res) {
        try {
            let findCampaign = await CampaignModel.findOne({
                where: {
                    name: req.body.name,
                    created_by_type: req.body.created_by_type,
                    created_by_id: req.body.created_by_id,
                    deleted_at: null
                }
            });

            // campaign is exist
            if (findCampaign) {
                return 0;
            }

            if (!findCampaign) {
                let campaignDetails = {
                    name: req.body.name,
                    created_by_type: req.body.created_by_type,
                    created_by_id: req.body.created_by_id,
                };

                let findcampaignDetails = await CampaignModel.create(campaignDetails);

                return findcampaignDetails;
            }
            return 1;
        } catch (err) {
            console.log(err);
            throw err;
        }
    },

    async getCampaignList(req) {
        try {
            const { page = 1, limit = 100, created_by_id, type, sort_by, search } = req.body;
            const offset = (page - 1) * limit;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            switch (sort_by) {
                case 'newest': order = [['createdAt', 'DESC']]; break;
                case 'oldest': order = [['createdAt', 'ASC']]; break;
                case 'alphabeticAsc': order = [['name', 'ASC']]; break;
                case 'alphabeticDesc': order = [['name', 'DESC']]; break;
                default: order = [['createdAt', 'DESC']];
            }
            const useractivecnt = await UserModel.count({
                where: {
                    isDeleted: 'No',
                    status: 'Active'
                }
            });

            const { rows: campaigns, count: totalCount } = await CampaignModel.findAndCountAll({
                attributes: ['id', 'name', 'created_by_type', 'created_by_id', 'createdAt', 'updatedAt'],
                where: whereClause,
                include: [{
                    model: AdsModel,
                    attributes: ['id', 'campaign_id','ad_title','ad_description','call_to_action_url','eligibility_type','state','city','suburb','objective','call_to_action', 'media_url', 'created_by_type', 'ad_status', 'pause_status', 'pause_by_admin',
                         'objective', 'daily_budget', 'status', 
                        [Sequelize.literal(`CONCAT(DATE_FORMAT(start_date, '%Y-%m-%d'))`), 'start_date'],
                        [Sequelize.literal(`CONCAT(DATE_FORMAT(end_date, '%Y-%m-%d'))`), 'end_date'],
                        [Sequelize.literal(`CONCAT(DATE_FORMAT(start_date, '%Y-%m-%d'), ' ', TIME_FORMAT(start_time, '%H:%i:%s'))`), 'start_date_time'],
                        [Sequelize.literal(`CONCAT(DATE_FORMAT(end_date, '%Y-%m-%d'), ' ', TIME_FORMAT(end_time, '%H:%i:%s'))`), 'end_date_time'],
                    ]
                }],
                order,
                limit: parseInt(limit),
                offset
            });

            const allAdIds = campaigns.flatMap(c => c.ads.map(ad => ad.id));
            const adAnalytics = await helper.getAdAnalytics(allAdIds);

            const campaignsData = await Promise.all(campaigns.map(async (data) => {
                let campaignTotalImpressions = 0;
                let campaignTotalClicks = 0;
                const campaignAdIds = data.ads.map(ad => ad.id);
                const campaignReach = await helper.getCampaignReach(campaignAdIds);

                const ads = data.ads.map((ad) => {
                    const { display_date, status } = helper.getAdStatusAndDisplay(ad);
                    const analytics = adAnalytics[ad.id] || { impressions: 0, clicks: 0, reach: 0 };

                    campaignTotalImpressions += analytics.impressions;
                    campaignTotalClicks += analytics.clicks;

                    return {
                        ...ad.toJSON(),
                        display_date,
                        status,
                        impressions: helper.formatNumber(analytics.impressions),
                        reach: helper.formatNumber(analytics.reach),
                        click: helper.formatNumber(analytics.clicks)
                    };
                });

                return {
                    id: data.id,
                    title: data.name,
                    impressions: helper.formatNumber(campaignTotalImpressions),
                    reach: helper.formatNumber(campaignReach),
                    click: helper.formatNumber(campaignTotalClicks),
                    created_at: data.createdAt,
                    updated_at: data.updatedAt,
                    ads_count: data.ads.length,
                    ads
                };
            }));

            return {
                active_user_count: useractivecnt,
                campaigns: campaignsData,
                pagination: {
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                },
                message: 'Campaign retrieved successfully.'
            };

        } catch (error) {
            console.log('Get Campaign list error:', error);
            throw error;
        }
    },
    async getAllCampaignList(req) {
        try {
            const { search } = req.body;

            const whereClause = {};

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            // No type filtering here - we want all campaigns
            const { rows: campaign, count: totalCount } = await CampaignModel.findAndCountAll({
                attributes: [
                    'id',
                    'name',
                    'created_by_type',
                    'created_by_id',
                    'createdAt',
                    'updatedAt'
                ],
                where: whereClause,
                order: [['createdAt', 'DESC']],
                raw: true
            });

            const campaignsData = campaign.map((data) => {
                return {
                    id: data.id,
                    title: data.name,
                    impressions: '400,000',
                    reach: '289,987',
                    click: '70,987',
                    created_at: data.createdAt,
                    updated_at: data.updatedAt,
                };
            });

            return {
                campaigns: campaignsData,
                total: totalCount,
                message: 'Campaign retrieved successfully'
            };
        } catch (error) {
            console.log('Get All Campaign list error:', error);
            throw error;
        }
    },
};
