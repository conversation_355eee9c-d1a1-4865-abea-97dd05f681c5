const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class order_tax extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			order_tax.belongsTo(models.orders, { foreignKey: 'orderID' });
			models.orders.hasMany(order_tax, { foreignKey: 'orderID' });
		}
	}
	order_tax.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			orderID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'order',
					key: 'id'
				}
			},
			taxID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'tax',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			amount: DataTypes.FLOAT,
			name: DataTypes.TEXT,
			percentage: DataTypes.FLOAT,
			createdAt: DataTypes.DATE
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'order_tax'
		}
	);
	return order_tax;
};
