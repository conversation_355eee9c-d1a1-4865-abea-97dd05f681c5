'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class product_tax extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
		}
	}
	product_tax.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			name: DataTypes.STRING,
			value: DataTypes.FLOAT
		},
		{
			sequelize,
			modelName: 'product_tax',
			timestamps: true,
			freezeTableName: true
		}
	);
	return product_tax;
};
