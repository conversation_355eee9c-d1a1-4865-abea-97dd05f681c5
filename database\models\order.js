const { Model } = require('sequelize');
let moment = require('moment-timezone');
module.exports = (sequelize, DataTypes) => {
	class orders extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			orders.belongsTo(models.user, { foreignKey: 'userID' });
			orders.belongsTo(models.bar, { foreignKey: 'barID' });
			orders.belongsTo(models.coupons, { foreignKey: 'promocode_id' });
			orders.hasMany(orders, {
				as: 'relatedOrders',
				foreignKey: 'id'
			});
			models.bar.hasMany(orders, { foreignKey: 'barID' });
		}
	}
	orders.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			orderNo: DataTypes.TEXT,
			posOrderId: {
				type: DataTypes.STRING,
				allowNull: true
			},
			posCheckInId: {
				type: DataTypes.STRING,
				allowNull: true
			},
			pickupCode: DataTypes.TEXT,
			subTotal: DataTypes.FLOAT,
			transactionFee: DataTypes.FLOAT,
			// refundTransactionFee: DataTypes.ENUM('Yes', 'No'),
			stripeFee: DataTypes.FLOAT,
			cardType: DataTypes.TEXT,
			cardNumber: DataTypes.TEXT,
			promocode_id: DataTypes.INTEGER,
			promocode_amount: DataTypes.FLOAT,
			promocode_discount: DataTypes.FLOAT,
			tax: DataTypes.FLOAT,
			total: DataTypes.FLOAT,
			orderDate: DataTypes.DATEONLY,
			convertedOrderDate: DataTypes.DATEONLY,
			convertedOrderDateTime: DataTypes.DATE,
			orderStatus: DataTypes.ENUM(
				'New',
				'Preparing',
				'Pickup',
				'Pickedup',
				'NotPickedup',
				'Intoxicated'
			),
			isPosOrder: DataTypes.ENUM('0', '1'), // 0 - No, 1 - Yes
			posOrderStatus: DataTypes.STRING,
			posOrderVersion: DataTypes.STRING,
			posTransactionId: DataTypes.STRING,
			posTransactionVersion: DataTypes.STRING,
			PreparingStartTime: DataTypes.DATE,
			ReadyTime: DataTypes.DATE,
			PickedupTime: DataTypes.DATE,
			paymentStatus: DataTypes.ENUM('notReceived', 'received', 'failed'),
			orderServiceType: DataTypes.ENUM('TABLE', 'PICKUP'),
			userID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'user',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			transactionID: DataTypes.TEXT,
			transferID: DataTypes.TEXT,
			refundTransactionID: DataTypes.TEXT,
			reversalsTransactionID: DataTypes.TEXT,
			refundStatus: DataTypes.ENUM('No', 'Refunded', 'PartialRefunded'),
			isCanceled: DataTypes.ENUM('Yes', 'No'),
			intoxicatedDate: DataTypes.DATE,
			refundedDate: DataTypes.DATE,
			createdAt: {
				type: DataTypes.DATE
			},
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No')
			// docketPrintingStatus: {
			//     type: DataTypes.BOOLEAN,
			//     allowNull: false,
			//     defaultValue: false,
			// },
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'orders',
			timestamps: true
		}
	);
	return orders;
};
