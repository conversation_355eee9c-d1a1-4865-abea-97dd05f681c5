'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class product_variant_types extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			product_variant_types.hasMany(models.product_variant_sub_types, {
				foreignKey: 'productVariantTypeID',
				as: 'productVariantSubTypes'
			});
			// product_variant_types.hasOne(models.cartProductVariantSubTypes, {
			//   foreignKey: "productVariantTypeID",
			// });
			// cartProductVariantSubTypes.belongsTo(models.product_variant_types, {
			//   foreignKey: "productVariantTypeID",
			// });
		}
	}
	product_variant_types.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			productID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'product',
					key: 'id'
				}
			},
			label: DataTypes.STRING,
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			serviceType: {
				type: DataTypes.ENUM('PICKUP', 'TABLE', 'BOTH'),
				defaultValue: null
			},
			isDeleted: { type: DataTypes.ENUM('Yes', 'No'), defaultValue: 'No' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false },
			posID: { type: DataTypes.STRING, allowNull: true }
		},
		{
			sequelize,
			modelName: 'product_variant_types',
			timestamps: true,
			freezeTableName: true
		}
	);
	return product_variant_types;
};
