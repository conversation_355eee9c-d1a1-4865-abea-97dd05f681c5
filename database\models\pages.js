'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class pages extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			pages.belongsTo(models.admin, {
				foreignKey: 'assigned_to',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
		}
	}
	pages.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			title: DataTypes.TEXT,
			content: DataTypes.TEXT,
			status: DataTypes.ENUM('Active', 'Inactive'),
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false },
			deletedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'pages',
			timestamps: true,
			freezeTableName: true
		}
	);
	return pages;
};
