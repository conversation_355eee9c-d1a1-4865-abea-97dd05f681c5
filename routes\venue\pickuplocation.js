var express = require('express');
var router = express.Router();

/* require for Authentication */
const Authorization = require('../../middleware/venueAuth').venueAuthorization;

/*require for pickuplocation  response*/
const PickupLocationController = require('../../controllers/venue/pickupLocationController');

/* pickuplocation add API */
router.post('/add', Authorization, PickupLocationController.add);

/* pickuplocation list API */
router.post('/list', Authorization, PickupLocationController.list);

/* pickuplocation edit API */
router.post('/edit', Authorization, PickupLocationController.edit);

/* pickuplocation delete API */
router.delete('/delete', Authorization, PickupLocationController.delete);

module.exports = router;
