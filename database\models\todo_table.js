"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class todo_table extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      todo_table.belongsTo(models.admin, {
        foreignKey: "assigned_to",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  todo_table.init(
    {
      //1 pending 2 Inprogress 3 completed 4 hold
      priority: DataTypes.ENUM("low", "medium", "high"),
      task: DataTypes.TEXT,
      status: DataTypes.INTEGER,
      due_date: DataTypes.DATE,
      assigned_to: DataTypes.INTEGER,
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      sequelize,
      paranoid: true,
      modelName: "todo_table",
      timestamps: true,
    }
  );
  return todo_table;
};
