const Joi = require('joi');

module.exports = {
	async add(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			operating_bar_tax: Joi.array()
				.required()
				.items(
					Joi.object({
						week_day: Joi.number().required().messages({
							'any.required': 'weekDay is required',
							'string.empty': 'weekDay is required'
						}),
						is_closed: Joi.number().required().messages({
							'any.required': 'isClosed is required',
							'string.empty': 'isClosed is required'
						})
					})
				),

			name: Joi.string().required().messages({
				'any.required': 'name is required',
				'string.empty': 'name is required'
			}),
			percentage: Joi.number().required().messages({
				'any.required': 'percentage is required',
				'string.empty': 'percentage is required'
			}),
			status: Joi.string().valid('Active', 'Inactive').optional({})
		});
		return schema.validate(req.body);
	},
	async list(req) {
		const schema = Joi.object({
			bar_id: Joi.string().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			})
		});
		return schema.validate(req.body);
	},
	async edit(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			}),
			operating_bar_tax: Joi.array()
				.required()
				.items(
					Joi.object({
						week_day: Joi.number().required().messages({
							'any.required': 'weekDay is required',
							'string.empty': 'weekDay is required'
						}),
						is_closed: Joi.number().required().messages({
							'any.required': 'isClosed is required',
							'string.empty': 'isClosed is required'
						})
					})
				),

			name: Joi.string().required().messages({
				'any.required': 'name is required',
				'string.empty': 'name is required'
			}),
			percentage: Joi.number().required().messages({
				'any.required': 'percentage is required',
				'string.empty': 'percentage is required'
			}),
			status: Joi.string().valid('Active', 'Inactive').optional({})
		});
		return schema.validate(req.body);
	},
	async delete(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'id is required',
				'string.empty': 'id is required'
			}),
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required',
				'string.empty': 'bar_id is required'
			})
		});
		return schema.validate(req.body);
	}
};
