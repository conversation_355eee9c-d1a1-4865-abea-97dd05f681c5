// retry.js
async function retryWithJitter(fn, options = {}) {
    const {
        retries = 5,
        baseDelay = 100, // in ms
        onRetry = () => { },
    } = options;

    let attempt = 0;

    while (attempt < retries) {
        try {
            return await fn();
        } catch (err) {
            attempt++;
            if (attempt >= retries) throw err;

            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 100;
            onRetry(err, attempt, delay);
            await new Promise(res => setTimeout(res, delay));
        }
    }
}

module.exports = { retryWithJitter };

