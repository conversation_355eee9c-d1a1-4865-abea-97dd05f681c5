    const Joi = require('joi');

    module.exports = {
        /**
         * Validation for adding a new ad
         * @param {Object} req - Request object
         * @returns {Object} - Validation result
         */
        addAdsValidation: async (req) => {
            try {
                const schema = Joi.object({
                    id: Joi.number().allow(null),
                    renew_flag: Joi.boolean().allow('', null).default(false),
                    invoice_no:Joi.string().allow('', null).default(false),
                    campaign_id: Joi.number().required().messages({
                        'number.base': 'Campaign ID must be a number',
                        'any.required': 'Please enter campaign ID'
                    }),
                    objective: Joi.string().valid('CPM', 'CPC', 'CPA').default('CPM').messages({
                        'string.base': 'Objective must be a string',
                        'any.only': 'Please select a valid objective: CPM, CPC, CPA'
                    }),
                    ad_title: Joi.string().required().messages({
                        'string.base': 'Ad title must be a string',
                        'any.required': 'Please enter ad title'
                    }),
                    ad_description: Joi.string().allow('', null).default(''),
                    media_url: Joi.string().allow('', null),
                    call_to_action: Joi.string().valid('learn_more', 'order_now', 'buy_tickets', 'view_menu', 'visit_website').default('learn_more').messages({
                        'string.base': 'Call to action must be a string',
                        'any.only': 'Please select a valid call to action: learn_more, order_now, buy_tickets, view_menu, visit_website'
                    }),
                    call_to_action_url: Joi.when('call_to_action', {
                        is: 'view_menu',
                        then: Joi.string().allow('', null),
                        otherwise: Joi.string().required().messages({
                            'string.base': 'Call to action URL must be a string',
                            'any.required': 'Please enter call to action URL'
                        })
                    }),
                    eligibility_type: Joi.string().valid('all_mytab_customers', 'mytab_customer_segments', 'your_venues_customers_segment').default('all_mytab_customers').messages({
                        'string.base': 'Eligibility type must be a string',
                        'any.only': 'Please select a valid eligibility type: all_mytab_customers, mytab_customer_segments, your_venues_customers_segment'
                    }),
                    eligibilityType: Joi.string().valid('all_mytab_customers', 'mytab_customer_segments', 'your_venues_customers_segment').default('all_mytab_customers').messages({
                        'string.base': 'Eligibility type must be a string',
                        'any.only': 'Please select a valid eligibility type: all_mytab_customers, mytab_customer_segments, your_venues_customers_segment'
                    }),
                    combined_eligibility: Joi.when('eligibility_type', {
                        is: Joi.valid('mytab_customer_segments', 'your_venues_customers_segment'),
                        then: Joi.boolean().required().messages({
                            'boolean.base': 'Combined eligibility must be a boolean',
                            'any.required': 'Please specify combined eligibility for the selected eligibility type'
                        }),
                        otherwise: Joi.boolean().default(false)
                    }).messages({
                        'boolean.base': 'Combined eligibility must be a boolean'
                    }),
                    state: Joi.string().required().messages({
                        'string.base': 'State must be a string',
                        'any.required': 'Please enter state'
                    }),
                    city: Joi.string().required().messages({
                        'string.base': 'City must be a string',
                        'any.required': 'Please enter city'
                    }),
                    segment_ids: Joi.alternatives().try(
                        Joi.array().items(Joi.number().allow('', null)),
                        Joi.array().items(Joi.string().allow('', null)),
                        Joi.string().allow('', null)
                    ),
                    user_ids: Joi.alternatives().try(
                        Joi.array().items(Joi.number().allow('', null)),
                        Joi.array().items(Joi.string().allow('', null)),
                        Joi.string().allow('', null)
                    ),
                    bar_id: Joi.alternatives().try(
                        Joi.number(),
                        Joi.string().pattern(/^\d+$/)
                    ).allow(null, ''),
                    created_by_type: Joi.string().valid('venue', 'advertiser', 'cms').required().messages({
                        'string.base': 'Created by type must be a string',
                        'any.only': 'Please select a valid created by type: venue, advertiser, cms',
                        'any.required': 'Please enter created by type'
                    }),
                    start_date: Joi.date().allow('', null).default(new Date()),
                    end_date: Joi.date().allow('', null).default(() => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
                    start_time: Joi.string().pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/).allow('', null).messages({
                                        'string.pattern.base': 'Start time must be in format HH:MM AM/PM (e.g., 09:30 AM)'
                    }),                   
                    end_time: Joi.string().pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/).allow('', null).messages({
                                        'string.pattern.base': 'End time must be in format HH:MM AM/PM (e.g., 11:59 PM)'
                                    }),                    daily_budget: Joi.number().allow('', null).default(0),
                    stripe_payment_id: Joi.string().allow('', null).default(''),
                    saved_card_id: Joi.string().allow('', null),
                    customer_id: Joi.string().allow('', null),
                    created_by_id: Joi.number().required().messages({
                        'number.base': 'Created by ID must be a number',
                        'any.required': 'Please enter created by ID'
                    }),
                    type: Joi.string().valid('venue', 'advertiser').default('advertiser').messages({
                        'string.base': 'Type must be a string',
                        'any.only': 'Please select a valid type: venue, advertiser'
                    }),
                    save_card: Joi.alternatives().try(
                        Joi.boolean(),
                        Joi.string().valid('true', 'false')
                    ).default(false),
                    stripe_customer_id: Joi.string().allow('', null),
                    stripe_card_id: Joi.string().allow('', null),
                    card_last4: Joi.string().allow('', null),
                    card_brand: Joi.string().allow('', null),
                    payment_method_type: Joi.string().allow('', null),
                    payment_status: Joi.string().allow('', null),
                    payment_method_id: Joi.string().allow('', null)
                });

                const { error, value } = schema.validate(req.body, { abortEarly: false });

                if (error) {
                    console.log("Validation error:", error.details);
                    return { error };
                }

                return { value };
            } catch (error) {
                console.error("Error in adsValidation.addAdsValidation:", error);
                throw error;
            }
        },

        addCardValidation: async (req) => {
            const schema = Joi.object({
                token: Joi.string().required().messages({
                    'any.required': 'Card token is required',
                    'string.empty': 'Card token is required'
                }),
                created_by_id: Joi.number().required().messages({
                    'any.required': 'Created by ID is required',
                    'number.empty': 'Created by ID is required'
                }),
                created_by_type: Joi.string().required().messages({
                    'any.required': 'Created by type is required',
                    'string.empty': 'Created by type is required'
                }),
                save_card: Joi.boolean().default(false),
                type: Joi.string().valid('venue', 'advertiser').default('advertiser')
            });
            return schema.validate(req.body);
        }
    };
