'use strict';

const constant = require('../../config/constant');
//const helper = require("../helper/generalHelper");
const imageGet = require('../../middleware/multerAwsGet');

const bcrypt = require('bcryptjs');
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class bar extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			bar.belongsToMany(models.venue_user, {
				through: models.venue_user_bar,
				foreignKey: 'bar_id'
			});

			bar.hasMany(models.operating_hour, {
				targetKey: 'barID'
			});

			bar.hasMany(models.itemActiveHours, {
				targetKey: 'barID'
			});

			bar.hasMany(models.bar_opening_hours, {
				foreignKey: 'barID',
				targetKey: 'id'
			});

			bar.hasMany(models.pickup_location, {
				foreignKey: 'barID'
			});
		}
	}
	bar.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			restaurantName: DataTypes.STRING(255),
			managerName: DataTypes.STRING(255),
			email: DataTypes.STRING(255),
			countryCode: DataTypes.STRING(25),
			mobile: DataTypes.STRING(25),
			password: DataTypes.STRING(255),
			address: DataTypes.STRING(250),
			latitude: DataTypes.STRING(100),
			longitude: DataTypes.STRING(100),
			passcode: DataTypes.STRING(50),
			passcodeStatus: DataTypes.ENUM('Active', 'Inactive'),
			stripeID: DataTypes.STRING(255),
			stripeCustomerID: DataTypes.STRING(255),
			oneTimePassword: DataTypes.INTEGER,
			resetPasswordCode: DataTypes.STRING(11),
			avatar: {
				type: DataTypes.STRING,
				get() {
					if (
						this.getDataValue('avatar') != '' &&
						this.getDataValue('avatar') != null
					) {
						return (
							constant.AWSS3PUBLICURL +
							constant.AWSBARFOLDER +
							this.getDataValue('avatar')
						);
						// const contentValue = imageGet(
						// 	constant.AWSBARFOLDER + this.getDataValue('avatar')
						// );
						// return contentValue;
					} else {
						return '';
					}
				}
			},
			mobileVerified: DataTypes.ENUM('Yes', 'No'),
			badge: DataTypes.BIGINT,
			notification: DataTypes.ENUM('Yes', 'No'),
			accountVerified: DataTypes.ENUM('New', 'Approved', 'Rejected'),
			posStatus: DataTypes.ENUM('-1', '0', '1'),
			posReferralLink: DataTypes.STRING(255),
			venueId: DataTypes.STRING(255),
			isVenueServeAlcohol: DataTypes.ENUM('Yes', 'No'),
			liquorLicenseNumber: DataTypes.TEXT,
			status: DataTypes.ENUM('Active', 'Inactive'),
			waitTimeDrink: DataTypes.INTEGER,
			waitTimeFood: DataTypes.INTEGER,
			serviceType: DataTypes.ENUM('PICKUP', 'TABLE', 'BOTH'),
			waitTimeServiceType: DataTypes.ENUM('PICKUP', 'TABLE', 'BOTH'),
			attachedPosConfig: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'barPOSMigration',
					key: 'id'
				}
			},
			docketSubscribed: DataTypes.ENUM('0', '1'),
			docketStatus: DataTypes.ENUM('0', '1'),
			// otp_token: DataTypes.STRING(555),
			docketSubscribed: DataTypes.ENUM('0', '1'),
			docketCommission: {
				type: DataTypes.INTEGER,
				defaultValue: 0
			},
			posFee: {
				type: DataTypes.INTEGER,
				defaultValue: 1.5
			},
			businessRegisterId: DataTypes.STRING,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No'),
			readPopup: DataTypes.ENUM('Yes', 'No'),
			connectVenueOtp: {
				type: DataTypes.INTEGER
			},
			readPopup: DataTypes.ENUM('Yes', 'No'),
			matchCategoryOpeningHours: {
				type: DataTypes.ENUM('Yes', 'No'),
				defaultValue: 'Yes'
			},
			timezone: {
				type: DataTypes.STRING,
				defaultValue: 'Australia/Perth'
			}
		},
		{
			hooks: {
				beforeCreate: function (bar, options) {
					if (bar.password) {
						return bcrypt
							.hash(bar.password, 10)
							.then((hash) => {
								bar.password = hash;
							})
							.catch((err) => {
								throw new Error();
							});
					}
				}
				// beforeUpdate: function (bar, options) {
				// 	if (bar.password) {
				// 		return bcrypt
				// 			.hash(bar.password, 10)
				// 			.then((hash) => {
				// 				bar.password = hash;
				// 			})
				// 			.catch((err) => {
				// 				throw new Error();
				// 			});
				// 	}
				// }
			},
			sequelize,
			freezeTableName: true,
			modelName: 'bar',
			timestamps: true
		}
	);
	return bar;
};
