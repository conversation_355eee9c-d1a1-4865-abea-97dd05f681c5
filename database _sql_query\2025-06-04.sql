CREATE TABLE IF NOT EXISTS `ads` (
  `id` int NOT NULL AUTO_INCREMENT,
  `campaign_id` int NOT NULL,
  `bar_id` int DEFAULT NULL,
  `state` text,
  `city` text,
  `suburb` text,
  `objective` enum('CPM','CPC','CPA') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `ad_title` varchar(255) DEFAULT NULL,
  `ad_description` text,
  `media_url` varchar(500) DEFAULT NULL,
  `call_to_action` enum('learn_more','order_now','buy_tickets','view_menu','visit_website') DEFAULT NULL,
  `call_to_action_url` text,
  `eligibility_type` enum('all_mytab_customers','mytab_customer_segments','your_venues_customers','your_venues_customers_segment') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_by_type` enum('venue','advertiser','cms') DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `daily_budget` decimal(10,2) DEFAULT NULL,
  `ad_status` enum('New','Approved','Rejected') CHARACTER SET tis620 COLLATE tis620_bin NOT NULL DEFAULT 'New',
  `pause_status` tinyint(1) NOT NULL DEFAULT '0',
  `save_card` tinyint(1) DEFAULT '0',
  `created_by_id` int DEFAULT NULL,
  `combined_eligibility` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `campaign_id` (`campaign_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `ads_segment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `segmentID` int NOT NULL,
  `adsID` int NOT NULL,
  `barID` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `ads_users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `userID` int NOT NULL,
  `adsID` int NOT NULL,
  `barID` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE IF NOT EXISTS `user_ads_analytic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `adsID` int NOT NULL,
  `userID` int NOT NULL,
  `impressions` int NOT NULL DEFAULT '0',
  `clicks` int NOT NULL DEFAULT '0',
  `last_impression_at` date DEFAULT NULL,
  `last_click_at` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `user_ads_save` (
  `id` int NOT NULL AUTO_INCREMENT,
  `adsID` int NOT NULL,
  `userID` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;