const Joi = require('joi');

module.exports = {
	async getAccountDeleteFormValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getSubHeadingWaitTimeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			sub_category_id: Joi.number().required().messages({
				'any.required': 'Sub Category id is required.',
				'number.empty': 'Sub Category id must be number.'
			})
		});
		return schema.validate(req.body);
	},
	async updateSubHeadingWaitTimeValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'Bar id is required.',
				'number.empty': 'Bar id must be number.'
			}),
			sub_category_id: Joi.number().required().messages({
				'any.required': 'Sub Category id is required.',
				'number.empty': 'Sub Category id must be number.'
			}),
			type: Joi.number().required().messages({
				'any.required': 'Type id is required.',
				'number.empty': 'Type id must be number.'
			}),
			data: Joi.required().messages({
				'any.required': 'Data is required.'
			})
		});
		return schema.validate(req.body);
	}
};
