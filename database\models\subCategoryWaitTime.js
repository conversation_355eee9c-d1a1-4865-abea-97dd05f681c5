'use strict';
const { Model } = require('sequelize');
var sub_category = require('./sub_category');
var bar = require('./bar');

module.exports = (sequelize, DataTypes) => {
	class sub_category_wait_time extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			sub_category_wait_time.belongsTo(models.sub_category, {
				foreignKey: 'subCategoryID'
			});
			models.sub_category.hasMany(sub_category_wait_time, {
				foreignKey: 'subCategoryID'
			});

			sub_category_wait_time.belongsTo(models.bar, { foreignKey: 'barID' });
			models.bar.hasMany(sub_category_wait_time, { foreignKey: 'barID' });
			// define association here
		}
	}
	sub_category_wait_time.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			itemActiveHoursID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'itemActiveHours',
					key: 'id'
				}
			},
			waitTimeType: DataTypes.ENUM('1', '2'),
			weekDay: DataTypes.SMALLINT,
			startTime: DataTypes.TIME,
			endTime: DataTypes.TIME,
			waitTime: DataTypes.TIME
		},
		{
			sequelize,
			modelName: 'sub_category_wait_time',
			timestamps: true,
			freezeTableName: true
		}
	);
	return sub_category_wait_time;
};
