'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	class user_ads_analytic extends Model {
		static associate(models) {

		}
	}

	user_ads_analytic.init(
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true
			},
			adsID: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			userID: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			impressions: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
			},
			clicks: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
			},
			last_impression_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			last_click_at: {
				type: DataTypes.DATE,
				allowNull: true,
			}
		},
		{
			sequelize,
			modelName: 'user_ads_analytic',
			timestamps: false,
			freezeTableName: true
		}
	);

	return user_ads_analytic;
};