'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class stripe_webhook_event extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
		}
	}
	stripe_webhook_event.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			eventID: {
				type: DataTypes.STRING(255),
				allowNull: false
			},
			subscriptionID: DataTypes.STRING(255),
			type: DataTypes.STRING(255),
			data: DataTypes.TEXT('long'),
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE }
		},
		{
			sequelize,
			modelName: 'stripe_webhook_event',
			timestamps: true,
			freezeTableName: true
		}
	);
	return stripe_webhook_event;
};
