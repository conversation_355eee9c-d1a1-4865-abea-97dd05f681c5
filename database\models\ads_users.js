'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class ads_users extends Model {
        static associate(models) {
            // define association here
            models.user.hasMany(ads_users, {
                foreignKey: 'ads'
            });
            ads_users.belongsTo(models.ads, {
                foreignKey: 'adsID'
            });

            models.ads.hasMany(ads_users, {
                foreignKey: 'userID'
            });
            ads_users.belongsTo(models.user, {
                foreignKey: 'userID'
            });

            models.bar.hasMany(ads_users, {
                foreignKey: 'barID'
            });
            ads_users.belongsTo(models.bar, {
                foreignKey: 'barID'
            });
        }
    }

    ads_users.init(
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true
            },
            userID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'user',
                    key: 'id'
                }
            },
            adsID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'ads',
                    key: 'id'
                }
            },
            barID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'bar',
                    key: 'id'
                }
            }
        },
        {
            sequelize,
            modelName: 'ads_users',
            timestamps: false,
            freezeTableName: true
        }
    );

    return ads_users;
};