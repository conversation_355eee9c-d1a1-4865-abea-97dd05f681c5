"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class bar_product_sequence extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      bar_product_sequence.belongsTo(models.category, {
        foreignKey: "categoryId",
      });
      models.category.hasMany(bar_product_sequence, {
        foreignKey: "categoryId",
        as: "bar_product_sequence",
      });

      bar_product_sequence.belongsTo(models.sub_category, {
        foreignKey: "subCategoryId",
      });
      models.sub_category.hasMany(bar_product_sequence, {
        foreignKey: "subCategoryId",
        as: "bar_product_sequence",
      });

      bar_product_sequence.belongsTo(models.product, {
        foreignKey: "productId",
      });
      models.product.hasMany(bar_product_sequence, {
        foreignKey: "productId",
        as: "bar_product_sequence",
      });

      bar_product_sequence.belongsTo(models.bar, { foreignKey: "barId" });
      bar_product_sequence.belongsTo(models.product, {
        foreignKey: "productId",
      });
    }
  }
  bar_product_sequence.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      barId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
      categoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "category",
          key: "id",
        },
      },
      subCategoryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "sub_category",
          key: "id",
        },
      },
      productId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "product",
          key: "id",
        },
      },
      productSequence: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: "bar_product_sequence",
      freezeTableName: true,
      timestamps: false,
    }
  );
  return bar_product_sequence;
};
