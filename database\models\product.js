'use strict';
const { Model } = require('sequelize');

const constant = require('../../config/constant');
const imageGet = require('../../middleware/multerAwsGet');

module.exports = (sequelize, DataTypes) => {
	class product extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			product.belongsTo(models.category, {
				foreignKey: 'categoryID',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
			product.belongsTo(models.sub_category, {
				foreignKey: 'subCategoryID',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
			product.belongsTo(models.pickup_location, {
				foreignKey: 'pickupLocationID',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});

			product.belongsTo(models.pos_conf, {
				foreignKey: 'fromPosId',
				targetKey: 'id',
				onDelete: 'CASCADE'
			});
			product.hasMany(models.product_variants, {
				foreignKey: 'productID'
				// as: "product_variants",
			});

			product.hasMany(models.product_extras, {
				foreignKey: 'productID',
				as: 'product_extras'
			});
			product.hasMany(models.product_variant_types, {
				foreignKey: 'productID',
				as: 'product_variant_types'
			});
			product.hasMany(models.product_food_options, {
				foreignKey: 'productID',
				targetKey: 'id',
				as: 'productFoodOptions'
			});
		}
	}
	product.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},

			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			categoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'category',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			fromPosId: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'pos_conf',
					key: 'id'
				}
			},
			name: DataTypes.STRING(255),
			description: DataTypes.STRING,
			avatar: {
				type: DataTypes.STRING(255),
				get() {
					if (
						this.getDataValue('avatar') != '' &&
						this.getDataValue('avatar') != null
					) {
						return (
							constant.AWSS3PUBLICURL +
							constant.AWSS3PRODUCTFOLDER +
							this.getDataValue('avatar')
						);
						// const contentValue = imageGet(
						// );
						// return contentValue;
					} else {
						return '';
					}
				}
			},
			price: DataTypes.FLOAT,
			dailyStockRenewal: DataTypes.INTEGER,
			isDailyStockRenewal: DataTypes.ENUM('Yes', 'No'),
			stock: DataTypes.INTEGER,
			isStockLimit: DataTypes.ENUM('Yes', 'No'),
			productTax: DataTypes.ENUM('gst', 'tax_free'),
			// calorie: DataTypes.FLOAT,
			// fat: DataTypes.FLOAT,
			// carbohydrates: DataTypes.FLOAT,
			// protein: DataTypes.FLOAT,
			pickupLocationID: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'pickup_location',
					key: 'id'
				}
			},
			posID: DataTypes.STRING(50),
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			serviceType: {
				type: DataTypes.ENUM('PICKUP', 'TABLE', 'BOTH'),
				defaultValue: null
			},
			isDeleted: { type: DataTypes.ENUM('Yes', 'No'), defaultValue: 'NO' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'product',
			timestamps: true,
			freezeTableName: true
		}
	);
	return product;
};
