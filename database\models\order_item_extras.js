const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
    class order_item_extras extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            order_item_extras.belongsTo(models.product_extras, {
                foreignKey: "productExtrasID",
            });

            models.order_items.hasMany(order_item_extras, {
                foreignKey: "orderItemID",
            });
            order_item_extras.belongsTo(models.order_items, {
                foreignKey: "orderItemID",
            });
        }
    }
    order_item_extras.init(
        {
            id: {
                type: DataTypes.BIGINT,
                autoIncrement: true,
                primaryKey: true,
            },
            orderItemID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "orderItems",
                    key: "id",
                },
            },
            productExtrasID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: "productExtras",
                    key: "id",
                },
            },
            price: DataTypes.FLOAT,
            createdAt: DataTypes.DATE,
            updatedAt: DataTypes.DATE,
        },
        {
            sequelize,
            freezeTableName: true,
            modelName: "order_item_extras",
            timestamps: true,
        }
    );
    return order_item_extras;
};
