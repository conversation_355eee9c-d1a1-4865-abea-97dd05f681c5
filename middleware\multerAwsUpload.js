require('dotenv').config();

// Used to interact with AWS Service
const AWS = require('aws-sdk');
const fs = require('fs');
const constant = require('../config/constant');

AWS.config.update({
	secretAccessKey: process.env.S3_SECRETKEY,
	accessKeyId: process.env.S3_ACCESSKEY,
	region: process.env.S3_REGION
});

var s3 = new AWS.S3({
	params: {
		Bucket: process.env.S3_BUCKET_NAME
	}
});

// Create an STS client
// const sts = new AWS.STS();

// async function assumeRole() {
// 	const params = {
// 		RoleArn: constant.AWSS3ARN,
// 		RoleSessionName: 'awsMyTabS3AccessRole'
// 	};

// 	try {
// 		const data = await sts.assumeRole(params).promise();
// 		return {
// 			accessKeyId: data.Credentials.AccessKeyId,
// 			secretAccessKey: data.Credentials.SecretAccessKey,
// 			sessionToken: data.Credentials.SessionToken
// 		};
// 	} catch (error) {
// 		console.log('Error assuming role:', error);
// 		throw error;
// 	}
// }

//To upload qr  on S3
function s3QrUpload(files, path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();
	return new Promise((resolve, reject) => {
		try {
			buf = Buffer.from(
				files.replace(/^data:image\/\w+;base64,/, ''),
				'base64'
			);
			let type = files.split(';')[0].split('/')[1];
			let data = {
				Key: path,
				Body: buf,
				ContentEncoding: 'base64',
				ContentType: `image/${type}`
			};
			s3.putObject(data, function (err, data) {
				if (err) {
					console.log(err);
					console.log('Error uploading data: ', data);
				} else {
					resolve(data);
				}
			});
		} catch (e) {
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

// To Upload image media on S3
function s3Upload(files, path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();
	return new Promise((resolve, reject) => {
		try {
			fs.readFile(files.path, (err, data) => {
				if (err) throw err;
				const params = {
					Bucket: process.env.S3_BUCKET_NAME,
					Key: path,
					// Body: files,
					ContentType: files.mimetype,
					Body: data
					// ACL: 'public-read'
				};

				s3.upload(params, function (err1, rese) {
					if (err1) {
						console.log(
							'🚀 ~ file: multerAwsUpload.js ~ line 102 ~ err1',
							err1
						);
						throw err1;
					}
					resolve(rese.Location);
				});
			});
		} catch (e) {
			console.log('function s3Upload -> e', e);
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

function s3PublicUpload(files, path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();
	return new Promise((resolve, reject) => {
		try {
			fs.readFile(files.path, (err, data) => {
				if (err) throw err;
				const params = {
					Bucket: process.env.S3_PUBLIC_BUCKET_NAME,
					Key: path,
					// Body: files,
					ContentType: files.mimetype,
					Body: data
					// ACL: 'public-read'
				};

				s3.upload(params, function (err1, rese) {
					if (err1) {
						console.log(
							'🚀 ~ file: multerAwsUpload.js ~ line 144 ~ err1',
							err1
						);
						throw err1;
					}
					resolve(rese.Location);
				});
			});
		} catch (e) {
			console.log('functions3Upload -> e', e);
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

function s3PublicUploadBuffer(imageData, mimetype, path) {
	// const credentials = await assumeRole();
	// AWS.config.update({
	// 	accessKeyId: credentials.accessKeyId,
	// 	secretAccessKey: credentials.secretAccessKey,
	// 	sessionToken: credentials.sessionToken,
	// 	region: process.env.S3_REGION
	// });

	// var s3 = new AWS.S3();

	return new Promise((resolve, reject) => {
		try {
			const params = {
				Bucket: process.env.S3_PUBLIC_BUCKET_NAME,
				Key: path,
				// Body: files,
				ContentType: mimetype,
				Body: imageData
				// ACL: 'public-read'
			};

			s3.upload(params, function (err1, rese) {
				if (err1) {
					console.log('🚀 ~ file: multerAwsUpload.js ~ line 160 ~ err1', err1);
					throw err1;
				}
				resolve(rese.Location);
			});
		} catch (e) {
			console.log('s3PublicUploadBuffer -> e', e);
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

module.exports = { s3Upload, s3QrUpload, s3PublicUpload, s3PublicUploadBuffer };
