'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class email_verification extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
		}
	}
	email_verification.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			email: DataTypes.TEXT,
			otp: DataTypes.TEXT
		},
		{
			sequelize,
			modelName: 'email_verification',
			timestamps: true,
			freezeTableName: true
		}
	);
	return email_verification;
};
