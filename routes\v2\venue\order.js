const express = require('express');
const router = express();
const orderController = require('../../../controllers/v2/venue/orderController');
const { venueAuthorization } = require('../../../middleware/venueAuth');
const IsVenueConnectedWithBar =
	require('../../../middleware/venueAuth').isVenueUserConnectedWithBar;

//order history(filter-order type,data,search)
router.post(
	'/barOrderHistory',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.barOrderHistory
);
router.post(
	'/barOrderList',
	venueAuthorization,
	IsVenueConnectedWithBar,
	orderController.barOrderList
);
module.exports = router;
