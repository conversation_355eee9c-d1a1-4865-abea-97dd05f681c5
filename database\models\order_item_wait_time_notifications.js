const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class order_item_wait_time_notifications extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {}
	}
	order_item_wait_time_notifications.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			orderID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'orders',
					key: 'id'
				}
			},
			orderItemID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'order_items',
					key: 'id'
				}
			},
			userID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'users',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			pickupLocationID: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'pickup_location',
					key: 'id'
				}
			},
			waitTime: DataTypes.TIME,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE,
			isDeleted: DataTypes.ENUM('Yes', 'No'),
			notification_type: DataTypes.ENUM('1', '2')
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'order_item_wait_time_notifications',
			timestamps: true
		}
	);
	return order_item_wait_time_notifications;
};
