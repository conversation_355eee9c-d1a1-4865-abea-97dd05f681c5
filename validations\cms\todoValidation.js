const Joi = require("joi");

module.exports = {
  async addValidation(req) {
    const schema = Joi.object({
      status: Joi.string().optional().messages({
        "any.required": "status is required",
        "string.empty": "status is required",
      }),
      priority: Joi.string().optional().messages({
        "any.required": "priority is required",
        "string.empty": "priority is required",
      }),
      assigned_to: Joi.number().optional().messages({
        "any.required": "assigned id is required",
        "number.empty": "assigned id is required",
      }),
      task: Joi.string().required().messages({
        "any.required": "authtoken is required",
        "string.empty": "authtoken is required",
      }),
      due_date: Joi.date().required().messages({
        "any.required": "due_date is required",
      }),
    });

    return schema.validate(req.body);
  },
  // async deleteTodovalidation(req) {
  //   const schema = Joi.object({
  //     id: Joi.number().required().messages({
  //       "any.required": "id is required",
  //       "number.empty": "id is required",
  //     }),
  //   });

  //   return schema.validate(req.body);
  // },
  async updateValidation(req) {
    const schema = Joi.object({
      id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.empty": "id is required",
      }),
      status: Joi.number().optional().allow(""),
      priority: Joi.string().optional().allow(""),
      assigned_to: Joi.number().optional().allow(""),
      task: Joi.string().optional().allow(""),
      due_date: Joi.date().optional().allow(""),
    });

    return schema.validate(req.body);
  },
  async listValidation(req) {
    const schema = Joi.object({
      page: Joi.number().required().messages({
        "any.required": "page is required",
        "number.base": "page is required",
      }),
      sortBy: Joi.string().optional().allow(""),
      status: Joi.number().optional().allow(""),
    });

    return schema.validate(req.body);
  },
  async updateStatusValidation(req) {
    const schema = Joi.object({
      id: Joi.number().required().messages({
        "any.required": "id is required",
        "number.empty": "id is required",
      }),
    });

    return schema.validate(req.body);
  },
};
