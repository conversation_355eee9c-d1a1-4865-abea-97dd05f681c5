const Joi = require("joi");

module.exports = {
    async itemsummaryreportValidation(req) {
        const schema = Joi.object({
            start_date: Joi.date().optional({}).allow(""),
            end_date: Joi.date().optional({}).allow(""),
            search: Joi.string()
                .messages({
                    'any.required': 'Search is required',
                    'string.base': 'Search must be a string'
                })
                .allow(''),
            page: Joi.number().required().messages({
                'any.required': 'page is required',
                'number.base': 'page must be number'
            }),
            id: Joi.number().optional().messages({}).allow(""),
            bar_id: Joi.number().required().messages({
                'any.required': 'Bar id is required.',
                'number.empty': 'Bar id is required.'
            }),
        });
        return schema.validate(req.body);
    },
    async downloaditemsummaryreportValidation(req) {
        const schema = Joi.object({
            start_date: Joi.date().optional({}).allow(""),
            end_date: Joi.date().optional({}).allow(""),
            search: Joi.string()
                .messages({
                    'any.required': 'Search is required',
                    'string.base': 'Search must be a string'
                })
                .allow(''),
            id: Joi.number().optional().messages({}).allow(""),
            bar_id: Joi.number().required().messages({
                'any.required': 'Bar id is required.',
                'number.empty': 'Bar id is required.'
            }),
        });
        return schema.validate(req.body);
    },
};
