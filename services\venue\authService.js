/*Messages,status code and services require*/
require('dotenv').config();
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const BarModel = require('../../database/models').bar; //import modal always Capital
const SubscriptionModel = require('../../database/models').subscription; //import modal always Capital
const VenueUserBarModel = require('../../database/models').venue_user_bar;
const VenueUserSubscriptionModel =
	require('../../database/models').venue_user_subscription;
const VenueUserTokenModel =
	require('../../database/models').venue_user_accesstoken;
const constant = require('../../config/constant');
const VenueUserModel = require('../../database/models').venue_user;
const models = require('../../database/models');
const generateRandomString =
	require('../../helper/generalHelper').generateRandomString;
const Sequelize = require('sequelize');
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const BarTokenModel = require('../../database/models').bar_accesstoken;
const { default: jwtDecode } = require('jwt-decode');

const Op = Sequelize.Op;

module.exports = {
	/*Register */
	async register(req, res) {
		try {
			let findUser = await VenueUserModel.findOne({
				where: {
					email: req.body.email.toLowerCase().trim(),
					deleted_at: null
				}
			});

			// email is exist
			if (findUser) {
				return 0;
			}
			let secret = speakeasy.generateSecret({});
			var url = speakeasy.otpauthURL({
				secret: secret.base32,
				label: req.body.first_name,
				issuer: 'MyTab Management Portal',
				encoding: 'base32'
			});
			let imageData = await QRCode.toDataURL(url);

			let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
			let imageName =
				generateRandomString(5) + '_' + Date.now() + '.' + extension;

			await QrimageUpload(
				imageData,
				constant.AWSS3VENUEQRCODEFOLDER + imageName
			);

			let qrCode = await helper.s3GetImage(
				constant.AWSS3VENUEQRCODEFOLDER + imageName
			);

			let userDetails = {
				first_name: req.body.first_name,
				last_name: req.body.last_name,
				email: req.body.email.toLowerCase().trim(),
				// address: req.body.address,
				password: req.body.password,
				mfa_qr_code: imageName,
				mfa_code: secret.base32,
				// profile_image: req.file.originalname,
				password_updated_at: new Date()
			};

			let findVenueUser = await VenueUserModel.create(userDetails);

			let subject = 'Set Up 2FA For Your MyTab Management Portal';
			let mailbody =
				'<p>Hello  ' +
				req.body.first_name +
				',' +
				'</p>' +
				'<p>Please follow the below steps to set up 2 Factor Authentication for your MyTab Management Portal. To ensure the highest security for your Management Portal and to keep our community safe, 2FA will be required each time you sign in.</p>' +
				'<p>1. Download an authenticator app on your mobile, or if you have an existing authenticator app, open your authenticator app on your mobile and use it to scan the QR code below. </p>' +
				'<p>2. Enter the 6-digit code provided by your authenticator app to log in to your MyTab Management Portal.</p>' +
				"<img src='" +
				qrCode +
				"' width='200'/>" +
				'<p> Enter the below code manually instead </p><p> <b>' +
				secret.base32 +
				'</b> </p>' +
				'<p>If you did not initiate this request, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a></p>' +
				'<p>Thank you, <br /> MyTab Venue Support <br />' +
				'<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>';

			mail.sendmail(res, req.body.email, subject, mailbody);

			delete findVenueUser.dataValues.password;
			delete findVenueUser.dataValues.mfa_qr_code;
			delete findVenueUser.dataValues.mfa_code;

			return findVenueUser;
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	/* login  */
	async login(req, res) {
		try {
			let findVenueUser = await VenueUserModel.findOne({
				where: {
					email: req.body.email.toLowerCase().trim(),
					deleted_at: null
				},
				attributes: {
					exclude: ['createdAt', 'updatedAt', 'deletedAt', 'otp_token']
				}
			});

			//user not found
			if (!findVenueUser) {
				return 0;
			}

			// Check Password
			if (
				!Bcryptjs.compareSync(
					req.body.password,
					findVenueUser.dataValues.password
				)
			) {
				return 0; //password is incorrect
			}

			//send mail if mfa code is null
			if (findVenueUser.mfa_code === null) {
				let secret = speakeasy.generateSecret({
					length: 20,
					name: findVenueUser.first_name
				});
				let imageData = await QRCode.toDataURL(secret.otpauth_url);

				let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
				let imageName =
					findVenueUser.dataValues.id +
					'_' +
					generateRandomString(5) +
					'_' +
					Date.now() +
					'.' +
					extension;

				await QrimageUpload(
					imageData,
					constant.AWSS3VENUEQRCODEFOLDER + imageName
				);

				let qrCode = await helper.s3GetImage(
					constant.AWSS3VENUEQRCODEFOLDER + imageName
				);

				await VenueUserModel.update(
					{ mfa_code: secret.base32, mfa_qr_code: imageName },
					{
						where: {
							id: findVenueUser.dataValues.id,
							deleted_at: null
						}
					}
				);

				let subject = 'Multi-Factor Authentication - ' + constant.APP_NAME;
				let mailbody =
					'<p>Welcome! The first step to gaining access to the ' +
					constant.APP_NAME +
					' portal is setting up multi-factor authentication, which helps keep ' +
					' our community safe. Please follow the below instructions: </p>' +
					'<p>1. Download the Google Authenticator app<br/> 2. Scan the QR code via the Google Authenticator app <br/> 3. Enter the 6-digit code provided by the Google Authenticator app to log in to your MyTab Venue CMS account </p>' +
					"<img src='" +
					qrCode +
					"' width='200'/>" +
					'<p>If you did not create a MyTab Venue CMS account, please contact our venue support <NAME_EMAIL> for assistance.</p>';
				mail.sendmail(res, req.body.email, subject, mailbody);
			}
			delete findVenueUser.dataValues.password;
			delete findVenueUser.dataValues.mfa_qr_code;
			return findVenueUser;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	//verify MFA
	async verifyMFA(req) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				email: req.body.email,
				id: req.body.user_id,
				deleted_at: null,
				status: 'Active'
			},
			order: [[{ model: BarModel }, 'restaurantName', 'ASC']],
			include: [
				{
					model: BarModel,
					where: {
						isDeleted: 'No'
					},
					required: false,
					attributes: [
						'restaurantName',
						'avatar',
						'id',
						'docketStatus',
						'managerName',
						'posStatus',
						'venueId',
						'docketCommission',
						'posFee',
						'passcodeStatus',
						'countryCode',
						'mobile',
						'createdAt',
						[Sequelize.literal('LENGTH(passcode)'), 'passcodeLength']
					],
					through: {
						attributes: []
					}
				},
				{
					model: SubscriptionModel,
					attributes: ['id', 'name', 'price'],
					required: false,
					through: {
						where: {
							status: 1
						},
						attributes: []
					}
				}
			]
		});
		if (findVenueUser) {
			let verified = speakeasy.totp.verify({
				secret: findVenueUser.dataValues.mfa_code,
				encoding: 'base32',
				token: req.body.code,
				window: 1
			});

			if (verified) {
				// generate JWT token with email and admin id
				let jwtString = JwtToken.sign(
					{
						email: findVenueUser.dataValues.email,
						user_id: findVenueUser.dataValues.id
					},
					constant.JWTTOKEN.secret,
					{
						expiresIn: constant.JWTTOKEN.expiresIn,
						algorithm: constant.JWTTOKEN.algo
					}
				); // default: HS256 encryption

				await VenueUserTokenModel.create({
					access_token: jwtString,
					device_token: req.body.device_token,
					user_id: req.body.user_id,
					device_type: req.body.device_type,
					device_name: req.body.device_name,
					device_location: req.body.device_location
				});

				// await imageDelete(
				//     constant.AWSS3VENUEQRCODEFOLDER +
				//     findVenueUser.dataValues.mfa_qr_code
				// );

				// await VenueUserModel.update(
				//   {
				//     mfa_qr_code: null,
				//   },
				//   {
				//     where: {
				//       id: req.body.user_id,
				//     },
				//   }
				// );

				findVenueUser.dataValues.token = jwtString;
				delete findVenueUser.dataValues.password;
				return findVenueUser;
			}
			return 1;
		} else {
			return 0;
		}
	},

	//verify Password
	async verifyPassword(req) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id,
				deleted_at: null,
				status: 'Active'
			}
		});
		if (findVenueUser) {
			// Check Password
			if (
				!Bcryptjs.compareSync(
					req.body.password,
					findVenueUser.dataValues.password
				)
			) {
				return 0; //password is incorrect
			} else {
				return 1;
			}
		} else {
			return 0;
		}
	},

	//setup MFA
	async setupMFA(req, res) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				email: req.body.email,
				deleted_at: null,
				status: 'Active'
			}
		});
		if (!findVenueUser) {
			return 0;
		}

		if (findVenueUser.dataValues.mfa_qr_code) {
			imageDelete(
				constant.AWSS3VENUEQRCODEFOLDER + findVenueUser.dataValues.mfa_qr_code
			);
		}
		let secret = speakeasy.generateSecret({});
		var url = speakeasy.otpauthURL({
			secret: secret.base32,
			label: findVenueUser.first_name,
			issuer: 'MyTab Management Portal',
			encoding: 'base32'
		});
		let imageData = await QRCode.toDataURL(url);
		let extension = imageData.match(/[^:/]\w+(?=;|,)/)[0];
		let imageName =
			findVenueUser.dataValues.id +
			'_' +
			generateRandomString(5) +
			'_' +
			Date.now() +
			'.' +
			extension;

		await QrimageUpload(imageData, constant.AWSS3VENUEQRCODEFOLDER + imageName);

		let qrCode = await helper.s3GetImage(
			constant.AWSS3VENUEQRCODEFOLDER + imageName
		);

		await VenueUserModel.update(
			{ mfa_code: secret.base32, mfa_qr_code: imageName },
			{ where: { id: findVenueUser.dataValues.id } }
		);

		let subject = 'Set Up 2FA For Your MyTab Management Portal';
		let mailbody =
			'<p>Hello  ' +
			findVenueUser.first_name +
			',' +
			'</p>' +
			'<p>Please follow the below steps to set up 2 Factor Authentication for your MyTab Management Portal. To ensure the highest security for your Management Portal and to keep our community safe, 2FA will be required each time you sign in.</p>' +
			'<p>1. Download an authenticator app on your mobile, or if you have an existing authenticator app, open your authenticator app on your mobile and use it to scan the QR code below. </p>' +
			'<p>2. Enter the 6-digit code provided by your authenticator app to log in to your MyTab Management Portal.</p>' +
			"<img src='" +
			qrCode +
			"' width='200'/>" +
			'<p> Enter the below code manually instead </p><p> <b>' +
			secret.base32 +
			'</b> </p>' +
			'<p>If you did not initiate this request, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a></p>' +
			'<p>Thank you, <br /> MyTab Venue Support <br />' +
			'<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>';
		await mail.sendmail(res, req.body.email, subject, mailbody);
		return 1;
	},

	/* Forgot-Password */
	async forgotPassword(req, res) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				email: req.body.email,
				deleted_at: null,
				status: 'Active'
			}
		});

		if (!findVenueUser) {
			return 0; // user not found
		}
		let token = helper.generateExpirationToken(req.body.email);

		//save token
		await VenueUserModel.update(
			{ otp_token: token },
			{
				where: {
					email: req.body.email
				}
			}
		);

		var subject = 'Reset Your MyTab Management Portal Password';
		var mailbody =
			'<div><p>We have received a password reset request for your Management Portal associated with the email <b><a href="mailto:' +
			req.body.email +
			'" style="color:#ff6460">' +
			req.body.email +
			'</a></b>.</p>' +
			'<p> To reset your password for your MyTab Management Portal, click  ' +
			'<a style="color:#ff6460" href="' +
			constant.CMS_URL + //for local testing
			// constant.API_URL +
			// "app/auth/reset-password-page/" +
			'/reset-password/venue/' +
			findVenueUser.dataValues.id +
			'_' +
			helper.generateRandomString(8) +
			'~' + //using tilde a its is the only character left which does not need encoding and .,-,_ are used in jwttoken
			token +
			'"  > here</a>' +
			'. Please note, to ensure the highest security this link is valid for only 48 hours.</p>' +
			'<p>For further assistance or enquiries, please reach out to us at <a style="color:#ff6460" href="mailto:<EMAIL>"><EMAIL></a> and our dedicated venue representatives will be happy to help.</p>' +
			'<p>If you did not initiate this password reset request, you can safely ignore this email.</p>' +
			'<p> Thank you <br /> MyTab Venue Support <br />' +
			'<a style="color:#ff6460" href="https://www.mytabinfo.com">www.mytabinfo.com</a><p/>';
		mail.sendmail(res, req.body.email, subject, mailbody);
		return 1;
	},

	/* verify token */
	async verifyToken(req) {
		try {
			const jwtVerify = JwtToken.verify(
				req.body.token,
				constant.JWTTOKEN.secret
			);

			if (jwtVerify) {
				const findToken = await VenueUserModel.findOne({
					where: {
						otp_token: req.body.token
					}
				});

				if (findToken) {
					return 1; // token valid
				}
			}
			return 0; // token invalid
		} catch (error) {
			return 0;
		}
	},

	//reset password
	async resetPasswsord(req) {
		try {
			let otp_token = req.body.otp_token;

			const JwtDetails = jwtDecode(otp_token);

			let venueUserData = await VenueUserModel.findOne({
				where: {
					email: JwtDetails.email,
					otp_token: otp_token
				}
			});
			if (!venueUserData) {
				return 0;
			}
			//update password and delete token
			await venueUserData.update(
				{ password: req.body.password, otp_token: null },
				{
					where: {
						email: venueUserData.dataValues.email
					}
				}
			);
			return 1;
		} catch (error) {
			console.log(err);
			return error;
		}
	},

	/* Update Profile */
	async updateProfile(req) {
		try {
			let userId = req.user_id;
			let findVenueUser = await VenueUserModel.findOne({
				where: {
					id: userId,
					deleted_at: null,
					status: 'Active'
				},
				attributes: [
					'first_name',
					'last_name',
					'profile_image',
					'email',
					'mobile',
					'pronouns'
				]
			});

			if (!findVenueUser) {
				return 0; // user not found
			}

			//email is present
			let VenueUseEmailExists = await VenueUserModel.findOne({
				where: {
					email: req.body.email,
					id: {
						[Op.ne]: userId
					}
				}
			});
			//email already exists
			if (VenueUseEmailExists) {
				return 2;
			}

			if (req.file) {
				if (findVenueUser.profile_image) {
					// delete old image
					await imageDelete(findVenueUser.profile_image.split('.com/')[1]);
				}

				await imageUpload(
					req.file,
					constant.AWSS3VENUEUSERFOLDER + req.file.originalname
				);
			}
			//update data
			await VenueUserModel.update(
				{
					first_name: req.body.first_name,
					last_name: req.body.last_name,
					email: req.body.email,
					mobile: req.body.mobile,
					address: req.body.address,
					profile_image: req.file?.originalname,
					pronouns: req.body.pronouns,
					country_code: req.body.country_code
				},
				{
					where: {
						id: userId
					}
				}
			);
			let updatedUser = await VenueUserModel.findOne({
				where: {
					id: userId
				},
				attributes: [
					'first_name',
					'last_name',
					'profile_image',
					'email',
					'mobile',
					'pronouns',
					'address',
					'country_code'
				]
			});

			return updatedUser;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	//get profile
	async getProfileDetails(req) {
		try {
			let userId = req.user_id;

			let findVenueUser = await VenueUserModel.findOne({
				where: {
					id: userId,
					deleted_at: null,
					status: 'Active'
				},

				attributes: [
					'first_name',
					'last_name',
					'profile_image',
					'email',
					'mobile',
					'address',
					'pronouns',
					'country_code'
				],
				include: [
					{
						model: BarModel,
						attributes: ['restaurantName', 'avatar', 'id'],
						through: {
							attributes: []
						}
					},
					{
						model: SubscriptionModel,
						attributes: ['id', 'name', 'price'],
						through: {
							where: {
								status: 1
							},
							attributes: []
						}
					}
				]
			});
			if (!findVenueUser) {
				return 0;
			}

			return findVenueUser;
		} catch (error) {
			console.log(error);
		}
	},

	//get my venue details
	async myVenueDetails(req) {
		try {
			let userId = req.user_id;
			let BarList = await VenueUserBarModel.findAll({
				where: {
					user_id: userId,
					deleted_at: null
				},
				include: [
					{
						model: BarModel,
						where: {
							isDeleted: 'No'
						},
						attributes: [
							'restaurantName',
							'avatar',
							'id',
							'docketStatus',
							'managerName',
							'posStatus',
							'venueId',
							'docketCommission',
							'posFee',
							'passcodeStatus',
							'countryCode',
							'mobile',
							[Sequelize.literal('LENGTH(passcode)'), 'passcodeLength']
						]
					}
				]
			});
			if (!BarList) {
				return 0;
			}

			return BarList;
		} catch (error) {
			console.log(error);
		}
	},

	/*LOGOUT  */
	async logout(req, res) {
		try {
			// delete token
			let Token = await VenueUserTokenModel.destroy({
				where: {
					access_token: req.headers.authorization,
					user_id: req.user_id
				}
			});

			//token not present
			if (Token) {
				return 1;
			}
			return 0;
		} catch (err) {
			console.log('err');
			throw err;
		}
	},
	//verify MFA
	async verifyMFAOtp(req) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id,
				deleted_at: null,
				status: 'Active'
			}
		});

		if (findVenueUser) {
			let verified = speakeasy.totp.verify({
				secret: findVenueUser.dataValues.mfa_code,
				encoding: 'base32',
				token: req.body.code,
				window: 1
			});

			if (verified) {
				delete findVenueUser.dataValues.password;
				return findVenueUser;
			}
			return 1;
		} else {
			return 0;
		}
	},
	//save subscription
	async saveSubscription(req) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id,
				deleted_at: null,
				status: 'Active'
			}
		});

		if (!findVenueUser) {
			return 0;
		}
		let subscriptionData = await VenueUserSubscriptionModel.create({
			subscription_id: req.body.subscription_id,
			user_id: req.user_id,
			status: '1'
		});

		return subscriptionData;
	},
	//delete venue
	async deleteVenue(req) {
		let findVenueUser = await VenueUserModel.findOne({
			where: {
				id: req.user_id
			}
		});

		if (!findVenueUser) {
			return 0;
		}

		await VenueUserModel.destroy({
			where: {
				id: req.user_id
			}
		});

		return 1;
	}
};
