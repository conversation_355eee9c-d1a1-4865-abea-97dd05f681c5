// utils/authHelper.js
const JwtToken = require('jsonwebtoken');
const constant = require('../config/constant');

const AdvertiserUserTokenModel = require('../database/models').advertiser_user_accesstoken;

async function generateAdvertiserToken(userData, req) {
	try {
		let jwtString = JwtToken.sign(
			{
				email: userData.dataValues.email,
				user_id: userData.dataValues.id
			},
			constant.JWTTOKEN.secret,
			{
				expiresIn: constant.JWTTOKEN.expiresIn,
				algorithm: constant.JWTTOKEN.algo
			}
		);

		await AdvertiserUserTokenModel.create({
			access_token: jwtString,
			device_token: req.body.device_token,
			user_id: userData.dataValues.id,
			device_type: req.body.device_type,
			device_name: req.body.device_name,
			device_location: req.body.device_location
		});

		userData.dataValues.token = jwtString;
		delete userData.dataValues.password;

		return userData;
	} catch (err) {
		console.error('Token generation error:', err);
		throw err;
	}
}

module.exports = {
	generateAdvertiserToken
};
