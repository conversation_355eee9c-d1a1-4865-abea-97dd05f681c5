ALTER TABLE `bar_opening_hours` ADD INDEX(`barID`, `weekDay`);
ALTER TABLE `bar_opening_hours_utc` ADD INDEX(`barOpeningHoursID`, `barID`, `weekDay`);
ALTER TABLE `bar_sub_category_opening_hours` ADD INDEX(`barID`, `subCategoryID`, `weekDay`);
ALTER TABLE `bar_sub_category_opening_hours_utc` ADD INDEX(`barSubCategoryOpeningHoursID`, `barID`, `subCategoryID`, `weekDay`);
ALTER TABLE `bar_sub_category_wait_time` ADD INDEX(`barID`, `subCategoryID`, `barSubCategoryOpeningHoursID`, `weekDay`);
ALTER TABLE `bar_sub_category_wait_time_utc` ADD INDEX(`barID`, `subCategoryID`, `barSubCategoryWaitTimeID`, `barSubCategoryOpeningHoursID`, `weekDay`);