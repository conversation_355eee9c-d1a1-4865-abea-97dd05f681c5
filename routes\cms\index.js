/*
 * Summary:     index.js file for handling all routes, request and response for admin panel - (CMS related actions).
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require("express");
var router = express.Router();

/* require for Authentication */
const CommonRouter = require("../cms/common");

/*require  for security */
const SecurityRouter = require("../cms/security");

/*Todo router */
const TodoRouter = require("../cms/todo");

/* routes of admin panel*/
router.use("/admin", CommonRouter);

/*routes of security */
router.use("/security", SecurityRouter);

/*routes of security */
router.use("/todo", TodoRouter);

module.exports = router;
