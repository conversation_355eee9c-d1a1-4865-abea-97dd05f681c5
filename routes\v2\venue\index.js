/*
 * Summary:     index.js file for handling all v2 routes, request and response for admin panel - (CMS related actions).
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require('express');
var router = express.Router();

/*require for bar Router */
const BarRouter = require('./bar');
const ProductRouter = require('./product');
const PickupLocationRouter = require('./pickuplocation');
const UpsellRouter = require('./upsell');
const OpeningHoursRouter = require('./openingHours');
const DiscountRouter = require('./discount');
const OrderRouter = require('./order');

/*Routes of  bar */
router.use('/bar', BarRouter);
router.use('/product', ProductRouter);
router.use('/pickup-location', PickupLocationRouter);
router.use('/upsell', UpsellRouter);
router.use('/opening-hours', OpeningHoursRouter);
router.use('/discount', DiscountRouter);
router.use('/order', OrderRouter);

module.exports = router;
