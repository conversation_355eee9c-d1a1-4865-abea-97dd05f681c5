// 1. & 2. New Taxes Page under Settings.... Starts
var Sequelize = require('sequelize');
const orders = require('./order');
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class order_refund_tax extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			order_refund_tax.belongsTo(models.orders, { foreignKey: 'orderID' });
			models.orders.hasMany(order_refund_tax, { foreignKey: 'orderID' });
		}
	}
	order_refund_tax.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			orderID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'order',
					key: 'id'
				}
			},
			taxID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'tax',
					key: 'id'
				}
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			amount: DataTypes.FLOAT,
			name: DataTypes.TEXT,
			percentage: DataTypes.FLOAT,
			createdAt: DataTypes.DATE,
			updatedAt: DataTypes.DATE
		},
		{
			sequelize,
			freezeTableName: true,
			modelName: 'order_refund_tax'
		}
	);
	return order_refund_tax;
};
