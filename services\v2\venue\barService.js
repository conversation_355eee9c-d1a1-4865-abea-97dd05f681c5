/*Messages,status code and services require*/
require('dotenv').config();
const Sequelize = require('sequelize');
const BarModel = require('../../../database/models').bar;
const ProductTaxModel = require('../../../database/models').product_tax;
const BarSubCategoryWaitTimeModel =
	require('../../../database/models').bar_sub_category_wait_time;
const BarSubCategoryWaitTimeUTCModel =
	require('../../../database/models').bar_sub_category_wait_time_utc;
const moment = require('moment');
const mail = require('../../../helper/sendmail');
const constant = require('../../../config/constant');

module.exports = {
	async getAccountDeleteForm(req, res) {
		try {
			const DELETE_FORMS = {
				PAYMENT_REQUIRED: `https://n1g6fmcikpp.typeform.com/to/vzhKKZXL#bar_id=${req.body.bar_id}`,
				NO_PAYMENT_REQUIRED: `https://n1g6fmcikpp.typeform.com/to/ty0lOSW4#bar_id=${req.body.bar_id}`
			};

			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id
				}
			});

			if (barDetails) {
				const createdAt = barDetails.dataValues.createdAt;
				const oneYearAgo = moment().subtract(1, 'year');
				const isOlderThanOneYear = moment(createdAt).isBefore(oneYearAgo);
				const venueName = barDetails.dataValues?.restaurantName;
				const venueAddress = barDetails.dataValues?.address;
				const managerName = barDetails.dataValues?.managerName;
				const countryCode = barDetails.dataValues?.countryCode;
				const prefixCountryCode = countryCode
					? countryCode.startsWith('+')
						? countryCode
						: `+${countryCode}`
					: '';
				const managerMobile = barDetails.dataValues?.mobile;
				const managerEmail = barDetails.dataValues?.email;
				const venueCreated = moment(createdAt)
					.tz('Australia/Perth')
					.format('DD-MM-YYYY');

				let subject = `Request to Delete Venue Account: ${venueName}`;

				var mailbody =
					'<p>Hello,</p>' +
					'<p>We have received a request to delete a venue account. ' +
					'Below are the details of the account that initiated the deletion process:' +
					'</p><p><strong>Venue Name</strong>: ' +
					venueName +
					'</p><p><strong>Venue Address</strong>: ' +
					venueAddress +
					'</p><p><strong>Venue Owner/Manager Name</strong>: ' +
					managerName +
					'</p><p><strong>Venue Owner/Manager Mobile</strong>: ' +
					prefixCountryCode +
					' ' +
					managerMobile +
					'</p><p><strong>Venue Owner/Manager Email</strong>: <a href="mailto:' +
					managerEmail +
					'">' +
					managerEmail +
					'</a></p><p><strong>Date Venue Account Was Created</strong>: ' +
					venueCreated +
					'</p>' +
					'<p>Best Regards,</p>' +
					'<p><strong>MyTab</strong>' +
					'</p>';

				mail.sendmail(res, process.env.BAREMAILTO, subject, mailbody);

				if (isOlderThanOneYear) {
					return { link: DELETE_FORMS.NO_PAYMENT_REQUIRED };
				} else {
					return { link: DELETE_FORMS.PAYMENT_REQUIRED };
				}
			}
			return 0;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getProductTax(req, res) {
		try {
			let taxDetails = await ProductTaxModel.findOne({
				attributes: ['name', 'value'],
				where: {
					id: 1
				}
			});
			if (taxDetails) {
				return taxDetails;
			}

			return 0;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getSubHeadingWaitTime(req, res) {
		try {
			let subCategoryWaitTimeDetails =
				await BarSubCategoryWaitTimeModel.findAll({
					where: {
						subCategoryID: req.body.sub_category_id,
						barID: req.body.bar_id
					},
					attributes: [
						[Sequelize.literal('waitTimeType'), 'type'],
						[
							Sequelize.literal(
								"`weekDay`,JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'startTime',TIME_FORMAT(`startTime`,'%H:%i'),'endTime',TIME_FORMAT(`endTime`,'%H:%i'),'waitTime',TIME_FORMAT(`waitTime`,'%H:%i')))"
							),
							'weekDay_WaitingTime'
						]
					],
					group: ['weekDay'],
					order: ['weekDay'],
					raw: true
				});
			if (subCategoryWaitTimeDetails) {
				let newResponse = subCategoryWaitTimeDetails.map((day) => {
					let weekDay_WaitingTime = day.weekDay_WaitingTime.sort(function (
						a,
						b
					) {
						return a.startTime.localeCompare(b.startTime);
					});
					return { ...day, weekDay_WaitingTime: weekDay_WaitingTime };
				});
				return newResponse;
			} else {
				return 0;
			}
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	async updateSubHeadingWaitTime(req, res) {
		try {
			let {
				bar_id: barID,
				type: waitTimeType,
				sub_category_id: subCategoryID,
				data: waitTimeArray
			} = req.body;
			let subCategoryWaitTime = await BarSubCategoryWaitTimeModel.findOne({
				where: { subCategoryID, barID }
			});

			if (!subCategoryWaitTime) return 0;

			await Promise.all([
				BarSubCategoryWaitTimeModel.update(
					{ waitTimeType },
					{ where: { subCategoryID, barID } }
				),
				BarSubCategoryWaitTimeUTCModel.update(
					{ waitTimeType },
					{ where: { subCategoryID, barID } }
				)
			]);

			if (waitTimeArray) {
				for (let day of waitTimeArray) {
					if (!day?.weekDay_WaitingTime) continue;
					await Promise.all(
						day.weekDay_WaitingTime.map(async (item) => {
							await Promise.all([
								BarSubCategoryWaitTimeUTCModel.update(
									{
										waitTime: item.waitTime >= '00:00' ? item.waitTime : '00:10'
									},
									{
										where: {
											barSubCategoryWaitTimeID: item.id
										}
									}
								),
								BarSubCategoryWaitTimeModel.update(
									{
										waitTime: item.waitTime >= '00:00' ? item.waitTime : '00:10'
									},
									{ where: { id: item.id, weekDay: day.weekDay } }
								)
							]);
						})
					);
				}
			}
			return 1;
		} catch (err) {
			console.error(err);
			throw err;
		}
	}
};
