'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class operating_hour extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			operating_hour.belongsTo(models.bar, {
				foreignKey: 'barID',
				targetKey: 'id'
			});
		}
	}
	operating_hour.init(
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true
			},
			weekDay: { type: DataTypes.INTEGER, defaultValue: '0' },
			openingHours: DataTypes.TIME,
			closingHours: DataTypes.TIME,
			isClosed: { type: DataTypes.BOOLEAN, defaultValue: '0' },

			barID: {
				type: DataTypes.INTEGER
			},

			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'operating_hour',
			timestamps: true
		}
	);
	return operating_hour;
};
