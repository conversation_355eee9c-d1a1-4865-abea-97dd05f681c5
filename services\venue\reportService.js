require("dotenv").config();
const constant = require("../../config/constant");
require('dotenv').config();
const order = require('../../database/models').orders;
const orderItems = require('../../database/models').order_items;
const SubCategoryModel = require('../../database/models').sub_category;
const VenueUserModel = require('../../database/models').venue_user;
const BarModel = require('../../database/models').bar;
const product = require('../../database/models').product;
const OperatingHoursModel = require("../../database/models").operating_hour;
const mail = require('../../helper/sendmail');
const { Parser } = require("json2csv");

const {Sequelize,QueryTypes} = require('sequelize');
const { sequelize } = require("../../database/models");
const Op = Sequelize.Op;
const convertUTCToVenueTimezone = require('../../helper/generalHelper').convertUTCToVenueTimezone;

module.exports = {
    async itemsummaryreport(req, res) {
        try {
            let orderWhere = [];
            let subCategorywhere = [];
            let productWhere = [];
            if (req.body.start_date && req.body.end_date) {
                orderWhere.push({
                    orderDate: {
                        [Op.gte]: req.body.start_date,
                        [Op.lte]: req.body.end_date,
                    },
                    paymentStatus: "received",
                    barID: req.body.bar_id,
                });
            } else if (req.body.start_date) {
                orderWhere.push({
                    orderDate: {
                        [Op.gte]: req.body.start_date,
                    },
                    paymentStatus: "received",
                    barID: req.body.bar_id,
                });
            }
            else {
                orderWhere.push({
                    paymentStatus: "received",
                    barID: req.body.bar_id,
                })
            }

            if (req.body.id) {
                subCategorywhere.push({
                    id: req.body.id

                });
            }
            if (req.body.search) {
                productWhere.push({
                    name: {
                        [Op.like]: `%${req.body.search}%`,
                    }
                })
            }

            const offset = (req.body.page - 1) * constant.LIMIT;
            const limit = constant.LIMIT;
            let orderItemsdeta = await orderItems.findAndCountAll({
                where: {
                    isCanceled: "No"
                },
                include: [
                    {
                        required: true,
                        model: product,
                        where: productWhere,
                        include: [{
                            required: true,
                            model: SubCategoryModel,
                            where: subCategorywhere,
                            attributes: []
                        }
                        ],
                        attributes: [],
                    },
                    {
                        required: true,
                        model: order,
                        where: orderWhere,
                        attributes: []
                    }
                ],
                offset: offset,
                limit: limit,
                attributes: ['productID', [Sequelize.fn('sum', Sequelize.col('quantity')), 'soldQunitity'], [Sequelize.literal(`product.name`), 'product_name'], [Sequelize.literal(`(SELECT name FROM sub_category WHERE id=product.subCategoryID )`), 'sub_category_name']],
                group: ["productID"],
            })
            orderItemsdeta.count = orderItemsdeta.count.length;
            return orderItemsdeta;
        } catch (err) {
            console.log('err', err);
            throw err;
        }
    },
    async downloaditemsummaryreport(req, res) {
        try {
            let orderWhere = [];
            let subCategorywhere = [];
            let productWhere = [];
            const convertedStartDate = await convertUTCToVenueTimezone(req.body.bar_id, req.body.startDate);
			const convertedEndDate = await convertUTCToVenueTimezone(req.body.bar_id, req.body.endDate);
            if (req.body.startDate && req.body.endDate) {
                orderWhere.push({
                    orderDate: {
                        [Op.gte]: req.body.startDate,
                        [Op.lte]: req.body.endDate,
                    },
                    barID: req.body.bar_id,
                    paymentStatus: "received"
                });
            } else if (req.body.startDate) {
                orderWhere.push({
                    orderDate: {
                        [Op.gte]: req.body.startDate,
                    },
                    barID: req.body.bar_id,
                    paymentStatus: "received",
                });
            }
            else {
                orderWhere.push({
                    barID: req.body.bar_id,
                    paymentStatus: "received",
                })
            }
            if (req.body.id) {
                subCategorywhere.push({
                    id: req.body.id
                });
            }
            if (req.body.search) {
                productWhere.push({
                    name: {
                        [Op.like]: `%${req.body.search}%`,
                    }
                })
            }
            let orderItemsdeta = await orderItems.findAndCountAll({
                where: {
                    isCanceled: "No"
                },
                include: [
                    {
                        required: true,
                        model: product,
                        where: productWhere,
                        include: [{
                            required: true,
                            model: SubCategoryModel,
                            where: subCategorywhere,
                            attributes: ["id", "name"]
                        }
                        ],
                        attributes: ["id", "name"],
                    },
                    {
                        required: true,
                        model: order,
                        where: orderWhere,
                        attributes: []
                    }
                ],
                attributes: ['productID', [Sequelize.fn('sum', Sequelize.col('quantity')), 'soldQunitity'], [Sequelize.literal(`product.name`), 'product_name'], [Sequelize.literal(`(SELECT name FROM sub_category WHERE id=product.subCategoryID )`), 'sub_category_name']],
                group: ["productID"],
            })

            const csvFormatedData = orderItemsdeta.rows.map((order) => {
                return {
                    "Menu Item Names": order?.product?.name,
                    "Category": order?.product?.sub_category?.name,
                    "Sold Quantity": order.dataValues.soldQunitity
                };
            })
            const fields = [
                "Menu Item Names",
                "Category",
                "Sold Quantity",
            ];
            const json2csvParser = new Parser({
                fields,
                defaultValue: "NA",
                includeEmptyRows: true,
            });

            const data = json2csvParser.parse(csvFormatedData);
         return data;
        } catch (err) {
            console.log('err', err);
            throw err;
        }
    },

    async ListCategory(req, res) {
    try{
        let data=await sequelize.query(`SELECT DISTINCT sub_category.id AS subCategoryID, sub_category.name FROM product AS product INNER JOIN sub_category AS sub_category ON product.subCategoryID = sub_category.id AND sub_category.isDeleted = 'No' AND sub_category.status = 'Active' WHERE product.isDeleted = 'No' AND product.barID =${req.body.bar_id}  AND product.status = 'Active' ORDER BY sub_category.name ASC`,
        {
            type:QueryTypes.SELECT
        })        
        return data;
    }
    catch(err){
        console.log(err);
        throw err;
    }
    }
};
