CREATE TABLE IF NOT EXISTS `bar_sub_category_wait_time` (
  `id` int NOT NULL,
  `barID` int NOT NULL,
  `subCategoryID` int NOT NULL,
  `barSubCategoryOpeningHoursID` int NOT NULL,
  `waitTimeType` enum('1','2') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '2' COMMENT '1- Per Hour, 2- All Day',
  `weekDay` smallint NOT NULL DEFAULT '0',
  `startTime` time NOT NULL,
  `endTime` time NOT NULL,
  `waitTime` time NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT NULL,
  `deletedAt` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `bar_sub_category_wait_time_utc` (
  `id` int NOT NULL,
  `barID` int NOT NULL,
  `subCategoryID` int NOT NULL,
  `barSubCategoryWaitTimeID` int NOT NULL,
  `barSubCategoryOpeningHoursID` int NOT NULL,
  `waitTimeType` enum('1','2') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '2' COMMENT '1- Per Hour, 2- All Day',
  `weekDay` smallint NOT NULL DEFAULT '0',
  `startTime` time NOT NULL,
  `endTime` time NOT NULL,
  `waitTime` time NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT NULL,
  `deletedAt` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);