CREATE TABLE `bar_pos_subscriptions` (
  `id` int NOT NULL,
  `barID` int NOT NULL,
  `subscription_id` varchar(255) NOT NULL,
  `payment_intent_id` varchar(255) NOT NULL,
  `payment_intent_client_secret` varchar(255) DEFAULT NULL,
  `payment_status` varchar(255) NOT NULL,
  `subscription_status` varchar(255) DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT NULL
);

ALTER TABLE `bar_pos_subscriptions` ADD PRIMARY KEY (`id`);
ALTER TABLE `bar_pos_subscriptions` MODIFY `id` int NOT NULL AUTO_INCREMENT;

CREATE TABLE `stripe_webhook_event` (
  `id` int NOT NULL,
  `eventID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `subscriptionID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `data` longtext NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT NULL
);

ALTER TABLE `stripe_webhook_event` ADD PRIMARY KEY (`id`);
ALTER TABLE `stripe_webhook_event` MODIFY `id` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `orders` ADD `posFailedCount` INT NOT NULL DEFAULT '0' AFTER `posOrderStatus`;
ALTER TABLE `sub_category` ADD `posID` VARCHAR(255) NULL DEFAULT NULL AFTER `isDeleted`;
