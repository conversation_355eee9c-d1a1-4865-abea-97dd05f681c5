"use strict";
const { Model } = require("sequelize");

module.exports = (sequelize, DataTypes) => {
  class timezone extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  
  timezone.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false
      },
      sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
      status: {
        type: DataTypes.ENUM('Active', 'Inactive'),
        defaultValue: 'Active'
      }
    },
    {
      sequelize,
      modelName: "timezone",
      tableName: "timezones",
      timestamps: false,
      freezeTableName: true
    }
  );

  return timezone;
};
