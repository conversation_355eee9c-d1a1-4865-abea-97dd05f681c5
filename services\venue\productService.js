/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const moment = require('moment');
const sharp = require('sharp');
const commonFunction = require('../../common/commonFunction');
const VenueUserModel = require('../../database/models').venue_user;
const BarModel = require('../../database/models').bar;
const ProductModel = require('../../database/models').product;
const OperatingHoursModel = require('../../database/models').operating_hour;
const ItemActiveHoursModel = require('../../database/models').itemActiveHours;
const ProductExtraModel = require('../../database/models').product_extras;
const Sequelize = require('sequelize');
const ProductFoodOptionsModel =
	require('../../database/models').product_food_options;
const BarProductSequenceModel =
	require('../../database/models').bar_product_sequence;
const BarCategorySequenceModel =
	require('../../database/models').bar_category_sequence;
const SegmentTagsModel = require('../../database/models').segment_tags;
const ProductVariantsModel = require('../../database/models').product_variants;

const ProductVariantTypesModel =
	require('../../database/models').product_variant_types;
const ProductVariantSubTypesModel =
	require('../../database/models').product_variant_sub_types;
const pickupLocationModel = require('../../database/models').pickup_location;
const FoodOptionsModel = require('../../database/models').food_options;
const SubCategoryModel = require('../../database/models').sub_category;
const SegmentProductTagsModel =
	require('../../database/models').segment_product_tags;
const SubCategoryWaitTimeModel =
	require('../../database/models').sub_category_wait_time;
const imageUpload = require('../../middleware/multerAwsUpload').s3PublicUpload;
const imageUploadBuffer =
	require('../../middleware/multerAwsUpload').s3PublicUploadBuffer;
const imageDelete = require('../../middleware/multerAwsDelete').s3PublicDelete;
const constant = require('../../config/constant');
const { sequelize } = require('../../database/models');
const Op = Sequelize.Op;
const { QueryTypes } = require('sequelize');

// const { POS } = require("../../helper/pos");
const VenueUserTokenModel =
	require('../../database/models').venue_user_accesstoken;

module.exports = {
	/* add Product */
	async addProduct(req, res) {
		try {
			//product image upload
			let awsUrl = '';
			if (req.file) {
				const compressImageBuffer = await sharp(req.file.path)
					.rotate()
					.webp({ quality: 30 })
					.toBuffer();

				await imageUploadBuffer(
					compressImageBuffer,
					req.file.mimetype,
					constant.AWSS3PRODUCTFOLDER + req.file.originalname
				);
				awsUrl = req.file?.originalname;
			}
			const tags = req.body?.tags;

			if (tags?.length) {
				const segmentTags = await SegmentTagsModel.count({
					where: {
						id: tags
					}
				});
				if (segmentTags !== tags.length) {
					return 1; //tagID not found
				}
			}

			/* create product */
			const productData = {
				barID: req.body.bar_id,
				categoryID: req.body.category_id,
				subCategoryID: req.body.subcategory_id, //subcategory model
				name: req.body.name, //name
				description: req.body.description,
				price: req.body.price,
				serviceType: req.body.serviceType,
				isDailyStockRenewal: req.body.isDailyStockRenewal,
				dailyStockRenewal: req.body.dailyStockRenewal,
				isStockLimit: req.body.isStockLimit,
				stock: req.body.stock,
				price: req.body.price,
				pickupLocationID: req.body.pickuplocation_id, //pickup location model
				productTax: req.body.product_tax,
				// calorie: req.body.calorie,
				// fat: req.body?.fat === 'null' ? null : req.body?.fat,
				// carbohydrates:
				// 	req.body?.carbohydrates === 'null' ? null : req.body?.carbohydrates,
				// protein: req.body?.protein === 'null' ? null : req.body?.protein,
				avatar: awsUrl
			};
			let Product = await ProductModel.create(productData);
			if (tags?.length) {
				const tagsData = tags.map((tagID) => ({
					productID: Product.id,
					tagID
				}));
				await SegmentProductTagsModel.bulkCreate(tagsData);
			}
			/* additional extras */
			if (req.body.extra_items) {
				let prmExtrasItem = req.body.extra_items;
				let arrExtrasItem = [];
				for (let i = 0; i < prmExtrasItem.length; i++) {
					arrExtrasItem.push({
						productID: Product.id,
						extraItem: prmExtrasItem[i].extra_item,
						price: prmExtrasItem[i].price,
						extraSequence: i + 1
					});
				}
				var productExtras = await ProductExtraModel.bulkCreate(arrExtrasItem);
			}
			// demo data req.body.product_varient_types
			// [
			//   {
			//     lable: "label",
			//     product_variant_sub_types: [
			//       {
			//         extra_item: "1",
			//         price:"23",
			//       },
			//       {},
			//     ],
			//   },
			//   {data2},
			// ];

			/*  product variant type */
			if (req.body.product_variant_types) {
				let productTypes = req.body.product_variant_types;
				var productVarientType = [];
				if (productTypes.length) {
					for (const type of productTypes) {
						/* store label of product types */

						let productVarientData = await ProductVariantTypesModel.create({
							label: type.label,
							serviceType: type.serviceType,
							productID: Product.id
						});

						/* store sizes/sub-types of product */
						let productSubTypeArr = [];
						let extraSequence = 0;
						for (const size of type.product_variant_sub_types) {
							productSubTypeArr.push({
								variantType: size.extra_item,
								price: size.price,
								productVariantTypeID: productVarientData.id,
								extraSequence: extraSequence++
							});
						}

						let productVarientSubtype =
							await ProductVariantSubTypesModel.bulkCreate(productSubTypeArr);

						productVarientType.push({
							productVarientData,
							productVarientSubtype
						});
					}
				}
			}

			if (req.body.food_options) {
				const foodOptions = req.body.food_options;
				let foodOptionsArr = [];
				for (const option of foodOptions) {
					foodOptionsArr.push({
						productID: Product.id,
						foodOptionID: option
					});
				}
				var productFoodOptions = await ProductFoodOptionsModel.bulkCreate(
					foodOptionsArr
				);
			}

			return {
				Product: Product,
				productVarientTypes: productVarientType,
				ProductExtra: productExtras,
				productFoodOptions: productFoodOptions
			};
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	/* delete product */
	async deleteProduct(req, res) {
		try {
			let barID = req.body.bar_id;
			let subCategory = req.body.sub_category_id;
			let isProductExist = await ProductModel.findOne({
				where: {
					id: req.body.id,
					isDeleted: 'No'
				}
			});

			if (!isProductExist) {
				return 0;
			}

			/* deleteproduct */
			let deletedProduct = await ProductModel.update(
				{
					isDeleted: 'Yes'
				},
				{
					where: {
						id: req.body.id
					}
				}
			);
			await SegmentProductTagsModel.destroy({
				where: {
					productID: req.body.id
				}
			});
			const existingItem = await ProductModel.count({
				where: { subCategoryID: subCategory, isDeleted: 'No', barID: barID }
			});

			if (deletedProduct === 1 && existingItem === 0) {
				/*delete category sequence if there are no product in subcategory */
				await BarCategorySequenceModel.destroy({
					where: { subCategoryId: subCategory, barId: barID }
				});
			}

			return deletedProduct;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* change status product */
	async chageStatusProduct(req, res) {
		try {
			let isProductExist = await ProductModel.findOne({
				where: {
					id: req.body.id,
					isDeleted: 'No',
					barID: req.body.bar_id
				}
			});

			if (!isProductExist) {
				return 0;
			}
			/* updateproduct */
			await ProductModel.update(
				{
					status: req.body.status
				},
				{
					where: {
						id: req.body.id
					}
				}
			);

			return 1;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* change status product */
	async getFoodOptionList(req, res) {
		try {
			// foodproduct
			return await FoodOptionsModel.findAll({
				where: { isDeleted: 'No', status: 'Active' },
				attributes: [['id', 'foodOptionID'], 'name', 'initials'],
				order: [
					['listingOrder', 'ASC']
					// [Sequelize.fn('length', Sequelize.col('initials')), 'ASC'],
					// ['initials', 'ASC']
				]
			});
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	/* Edit Product */
	async editProduct(req, res) {
		try {
			let productId = req.body.id;
			let barId = req.body.bar_id;
			const addedTags = req.body?.added_tags;
			const deletedTags = req.body?.deleted_tags;

			const productData = await ProductModel.findOne({
				where: {
					id: productId,
					barID: barId,
					isDeleted: 'No'
				}
			});

			if (!productData) {
				return 0; /* product not found */
			}

			if (addedTags?.length) {
				const segmentTags = await SegmentTagsModel.count({
					where: {
						id: addedTags
					}
				});
				if (segmentTags !== addedTags.length) {
					return 3; //tagID not found
				}
				const tagsData = addedTags.map((tagID) => ({
					productID: productId,
					tagID
				}));
				await SegmentProductTagsModel.bulkCreate(tagsData);
			}

			if (deletedTags?.length) {
				await SegmentProductTagsModel.destroy({
					where: {
						productID: productId,
						tagID: {
							[Op.in]: deletedTags
						}
					},
					force: true
				});
			}

			let awsUrl;
			//file present
			if (req.file) {
				//delete old file
				if (productData.avatar) {
					await imageDelete(productData.avatar.split('.com/')[1]);
				}
				const compressImageBuffer = await sharp(req.file.path)
					.rotate()
					.webp({ quality: 30 })
					.toBuffer();

				await imageUploadBuffer(
					compressImageBuffer,
					req.file.mimetype,
					constant.AWSS3PRODUCTFOLDER + req.file.originalname
				);
				awsUrl = req.file.originalname;
			}

			if (
				req.body.serviceType &&
				req.body.serviceType != productData.dataValues.serviceType
			) {
				let isVenueUpdatable = await commonFunction.checkBarIsOpenV2(barId);
				if (isVenueUpdatable) {
					return 2;
				}
			}

			/* update product */
			await ProductModel.update(
				{
					categoryID: req.body.category_id,
					subCategoryID: req.body.subcategory_id,
					name: req.body.name,
					description: req.body.description,
					price: req.body.price,
					serviceType: req.body.serviceType,
					isUpdateByUser: 'Yes',
					isDailyStockRenewal: req.body.isDailyStockRenewal,
					dailyStockRenewal: req.body.dailyStockRenewal,
					isStockLimit: req.body.isStockLimit,
					stock: req.body.stock,
					pickupLocationID: req.body.pickuplocation_id,
					productTax: req.body.product_tax,
					// calorie: req.body.calorie,
					// fat: req.body?.fat === 'null' ? null : req.body?.fat,
					// carbohydrates:
					// 	req.body?.carbohydrates === 'null' ? null : req.body?.carbohydrates,
					// protein: req.body?.protein === 'null' ? null : req.body?.protein,
					avatar: awsUrl
				},
				{
					where: {
						id: req.body.id
					}
				}
			);

			/* delete product varient */
			if (req.body.delete_varient_type) {
				await ProductVariantsModel.update(
					{
						isDeleted: 'Yes'
					},
					{
						where: {
							id: {
								[Op.in]: req.body.delete_varient_type
							},
							productID: productId
						}
					}
				);
			}

			//update extra done
			if (req.body.extra_items) {
				let prmExtrasItem = req.body.extra_items;

				let arrExtrasItem = [];
				let extraSequence = 0;
				for (const { extra_item, id, price } of prmExtrasItem) {
					if (id) {
						const extraUpdates = {
							extraItem: extra_item,
							price: price,
							extraSequence: extraSequence++
						};
						await ProductExtraModel.update(extraUpdates, {
							where: { id: id, productID: productId }
						});
					} else {
						const additionItems = {
							productID: productId,
							extraItem: extra_item,
							price: price,
							extraSequence: extraSequence++
						};
						arrExtrasItem.push(additionItems);
					}
				}

				//add arrExtra item without id
				await ProductExtraModel.bulkCreate(arrExtrasItem);
			}
			//deleteProduct Extra
			if (req.body.delete_product_extra) {
				await ProductExtraModel.update(
					{
						isDeleted: 'Yes',
						updatedAt: new Date()
					},
					{
						where: {
							id: {
								[Op.in]: req.body.delete_product_extra
							},
							productID: productId
						}
					}
				);
			}

			// demo data req.body.product_varient_types
			// [
			//   {  id:23
			//     lable: "label",
			//     product_variant_sub_types: [
			//       {
			//         extra_item: "1",
			//         price:"23",
			//       },
			//       {},
			//     ],
			//   },
			//   {data2},
			// ];
			//product varient type

			if (req.body.product_variant_types) {
				let productTypes = req.body.product_variant_types;
				if (productTypes.length) {
					for (const type of productTypes) {
						if (type.id) {
							const productVariantTypeData =
								await ProductVariantTypesModel.findOne({
									where: { id: type.id }
								});
							if (
								type.serviceType !=
								productVariantTypeData.dataValues.serviceType
							) {
								let isVenueUpdatable = await commonFunction.checkBarIsOpenV2(
									barId
								);
								if (isVenueUpdatable) {
									return 2;
								}
							}

							//if id in producttype
							ProductVariantTypesModel.update(
								{
									label: type.label,
									serviceType: type.serviceType,
									isUpdateByUser: 'Yes',
									posID: type.posID,
									updatedAt: new Date()
								},
								{
									where: {
										id: type.id,
										productID: productId
									}
								}
							);
							//update product varient subtype
							let productSubTypeArr = [];
							let extraSequence = 0;
							for (const size of type.product_variant_sub_types) {
								if (size.id) {
									//if id present in productsubtype
									ProductVariantSubTypesModel.update(
										{
											variantType: size.extra_item,
											price: size.price,
											extraSequence: extraSequence++,
											updatedAt: new Date()
										},
										{
											where: {
												id: size.id,
												productVariantTypeID: type.id
											}
										}
									);
								} else {
									productSubTypeArr.push({
										variantType: size.extra_item,
										price: size.price,
										productVariantTypeID: type.id,
										extraSequence: extraSequence++
									});
								}
							}
							await ProductVariantSubTypesModel.bulkCreate(productSubTypeArr);
						} else {
							//id not present in producttype
							let productVariantTypesData =
								await ProductVariantTypesModel.create({
									label: type.label,
									productID: productId,
									serviceType: type.serviceType ? type.serviceType : null,
									isUpdateByUser: 'Yes',
									posID: type.posID
								});

							let productSubTypeArr = [];
							let extraSequence = 0;
							for (const size of type.product_variant_sub_types) {
								productSubTypeArr.push({
									variantType: size.extra_item,
									price: size.price,
									productVariantTypeID: productVariantTypesData.id,
									extraSequence: extraSequence++
								});
							}
							await ProductVariantSubTypesModel.bulkCreate(productSubTypeArr);
						}
					}
				}
			}

			//delete productvarienttype
			if (req.body.deleted_product_variant_types_ids) {
				await ProductVariantTypesModel.update(
					{
						isDeleted: 'Yes'
					},
					{
						where: {
							id: {
								[Op.in]: req.body.deleted_product_variant_types_ids
							},
							productID: productId
						}
					}
				);

				await ProductVariantSubTypesModel.update(
					{
						isDeleted: 'Yes'
					},
					{
						where: {
							productVariantTypeID: {
								[Op.in]: req.body.deleted_product_variant_types_ids
							}
						}
					}
				);
			}
			//delete product varient subtype
			if (req.body.deleted_product_variant_sub_types_ids) {
				ProductVariantSubTypesModel.update(
					{
						isDeleted: 'Yes'
					},
					{
						where: {
							id: {
								[Op.in]: req.body.deleted_product_variant_sub_types_ids
							}
						}
					}
				);
			}
			//update food option done
			if (req.body.food_options) {
				const foodOptions = req.body.food_options;
				for (const option of foodOptions) {
					await ProductFoodOptionsModel.findOrCreate({
						where: {
							productID: productId,
							foodOptionID: option
						}
					});
				}
			}

			if (req.body.delete_food_option) {
				await ProductFoodOptionsModel.destroy({
					where: {
						foodOptionID: {
							[Op.in]: req.body.delete_food_option
						},
						productID: productId
					}
				});
			}

			return 1;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	//list product
	async listProduct(req, res) {
		try {
			let barID = req.body.bar_id;
			try {
				let whereClause = [];
				whereClause.push({
					isDeleted: 'No'
				});

				const barDetails = await BarModel.findOne({
					attributes: ['id', 'serviceType', 'posStatus'],
					where: { id: barID }
				});
				if (
					barDetails.dataValues.posStatus &&
					barDetails.dataValues.posStatus == '1'
				) {
					whereClause.push({
						categoryID: '-1'
					});
				} else {
					// if (req.body.categoryID) {
					whereClause.push({
						[Op.not]: [{ categoryID: '-1' }]
					});
					// }
				}

				let status = 'All';
				if (req.body.status) {
					status = req.body.status;
				}

				const selectedServiceType = req.body.serviceType;
				let currentDay = moment().tz('Australia/Perth').isoWeekday();

				let categories = await SubCategoryModel.findAll({
					where: whereClause,
					attributes: [
						'id',
						'categoryID',
						'name',
						[
							Sequelize.literal(
								`(SELECT IF(IA.status = '1' and CAST('${moment()
									.tz('Australia/Perth')
									.format(
										'HH:mm:ss'
									)}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = WEEKDAY(CAST(NOW() AS DATE))HAVING isActive = 1)`
							),
							'operatingFlag'
						]
					],
					include: [
						{
							model: BarCategorySequenceModel,
							as: 'bar_category_sequence',
							required: false,
							attributes: [
								[
									Sequelize.fn(
										'coalesce',
										Sequelize.col('subCategorySequence'),
										1000000000000
									),
									'subCategorySequence'
								]
							],
							where: { barId: barID }
						}
					],
					order: [
						[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
						'id'
					]
				});

				if (categories) {
					let productFetchPromises = [];
					let filterLoop = [];
					categories.map((cat1) => {
						if (cat1.dataValues.operatingFlag == 1) {
							filterLoop.push(cat1.id);
						}
					});

					if (filterLoop.length == 0) {
						console.log('No Popular');
					} else {
						// if (req.body?.showPopular == '1') {
						//fetch popular product list
						let data = await this.fetchPopularProductList(
							barID,
							'',
							filterLoop,
							selectedServiceType
						);

						productFetchPromises.push(data);
						// }
					}

					//fetchproductlist data

					const mapData = categories.map(async (cat) => {
						let data = await this.fetchProductList(
							cat,
							barID,
							status,
							selectedServiceType,
							req.body?.search
						);
						return data;
					});

					let AllData = await Promise.all(mapData);

					productFetchPromises = [...productFetchPromises, ...AllData];
					let arryProcessPromises = [];
					productFetchPromises.map((product) => {
						if (product.value.categoryProduct.length) {
							arryProcessPromises.push(product.value);
						}
					});

					return arryProcessPromises;
				}
			} catch (error) {
				console.log('error');
				throw error;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async listProductV2(req, res) {
		try {
			let barID = req.body.bar_id;
			let serviceType = req.body.serviceType;
			let subCatID = req.body.sub_cat_id;
			let status = req.body?.status || 'All';

			if (subCatID == 0) {
				// Popular
				let whereClause = [];
				whereClause.push({ isDeleted: 'No' });

				const barDetails = await BarModel.findOne({
					attributes: ['id', 'serviceType', 'posStatus'],
					where: { id: barID }
				});

				if (barDetails) {
					const { posStatus } = barDetails.dataValues;

					whereClause.push(
						posStatus == '1'
							? { categoryID: '-1' }
							: { [Op.not]: [{ categoryID: '-1' }] }
					);
				}

				let currentDay = moment().tz('Australia/Perth').isoWeekday();
				const currentTime = moment().tz('Australia/Perth').format('HH:mm:ss');

				let categories = await SubCategoryModel.findAll({
					where: whereClause,
					attributes: [
						'id',
						'categoryID',
						'name',
						[
							Sequelize.literal(
								`(SELECT IF(IA.status = '1' and CAST('${currentTime}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = WEEKDAY(CAST(NOW() AS DATE))HAVING isActive = 1)`
							),
							'operatingFlag'
						],
						[
							Sequelize.literal(`
								(select waitTime from sub_category_wait_time
									where
									barID = ${barID} and
									subCategoryID = sub_category.id and
									weekDay = '${currentDay - 1}' and
									startTime <= '${currentTime}' and
									endTime >= '${currentTime}' LIMIT 1
								)
							`),
							'waitTime'
						],
						[
							Sequelize.literal(`
								(select id from sub_category_wait_time
									where
									barID = ${barID} and
									subCategoryID = sub_category.id and
									weekDay = '${currentDay - 1}' and
									startTime <= '${currentTime}' and
									endTime >= '${currentTime}' LIMIT 1
								)
							`),
							'waitTimeId'
						]
					],
					include: [
						{
							model: BarCategorySequenceModel,
							as: 'bar_category_sequence',
							required: false,
							attributes: [
								[
									Sequelize.fn(
										'coalesce',
										Sequelize.col('subCategorySequence'),
										1000000000000
									),
									'subCategorySequence'
								]
							],
							where: { barId: barID }
						}
					],
					order: [
						[Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
						'id'
					]
				});

				let filteredCategories = [];
				categories.map((cat) => {
					if (cat.dataValues.operatingFlag == 1) {
						filteredCategories.push(cat.id);
					}
				});

				let data = await this.fetchPopularProductListV2(
					barID,
					'',
					filteredCategories,
					serviceType,
					(page = 0),
					(limit = 5)
				);

				return data;
			} else {
				let data = await this.fetchProductListV2(
					subCatID,
					barID,
					status,
					serviceType,
					req.body?.search,
					req.body?.page,
					req.body?.limit
				);

				return data;
			}
		} catch (err) {
			throw err;
		}
	},

	/*delete product varient */
	async deleteProductVariant(req, res) {
		try {
			let isProductVarientExist = await ProductVariantsModel.findOne({
				where: {
					id: req.body.id,
					isDeleted: 'No',
					productId: req.body.product_id
				}
			});

			if (!isProductVarientExist) {
				return 0;
			}

			/*delete product varient */
			await ProductVariantsModel.update(
				{
					isDeleted: 'Yes'
				},
				{
					where: {
						id: req.body.id,
						productId: req.body.product_id
					}
				}
			);
			return 1;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/*delete product extra */
	async deleteProductExtras(req, res) {
		try {
			let isProductExtraExist = await ProductExtraModel.findOne({
				where: {
					id: req.body.id,
					isDeleted: 'No',
					productId: req.body.product_id
				}
			});

			if (!isProductExtraExist) {
				return 0;
			}

			/*delete product varient */
			await ProductExtraModel.update(
				{
					isDeleted: 'Yes'
				},
				{
					where: {
						id: req.body.id,
						productId: req.body.product_id
					}
				}
			);
			return 1;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	/*list single product */
	async getSingleProduct(req, res) {
		try {
			let barId = req.body.bar_id;
			let productId = req.body.id;

			/*list single product */
			let listProduct = await ProductModel.findOne({
				where: {
					isDeleted: 'No',
					id: productId,
					barId: barId,
					isDeleted: 'No'
				},
				include: [
					{
						model: pickupLocationModel,
						where: {
							isDeleted: 'No'
						},
						attributes: { exclude: ['createdAt', 'updatedAt', 'isDeleted'] },
						required: false
					},
					{
						model: SubCategoryModel,
						where: {
							status: 'Active',
							isDeleted: 'No'
						},
						attributes: ['name', 'id'],
						required: false
					},
					// {
					//   model: ProductVariantsModel,
					//   where: {
					//     isDeleted: "No",
					//   },
					//   attributes: ["id", "productID", "variantType", "price"],
					//   required: false,
					// },

					{
						model: ProductExtraModel,
						as: 'product_extras',
						where: {
							isDeleted: 'No'
						},
						attributes: [
							'id',
							'productID',
							'extraItem',
							'price',
							'extraSequence'
						],
						required: false
					},
					{
						model: ProductVariantTypesModel,
						// as: 'productVariantTypes',
						required: false,
						where: {
							isDeleted: 'No'
						},
						attributes: { exclude: ['createdAt', 'updatedAt', 'isDeleted'] },
						as: 'product_variant_types',
						include: {
							model: ProductVariantSubTypesModel,
							as: 'productVariantSubTypes',
							required: false,
							attributes: [
								'id',
								['variantType', 'extraItem'],
								'price',
								'extraSequence'
							],
							where: { isDeleted: 'No' }
						}
					},
					{
						model: ProductFoodOptionsModel,
						as: 'productFoodOptions',
						attributes: [
							'id',
							'productID',
							'foodOptionID',
							[
								Sequelize.literal(
									`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`
								),
								'name'
							],
							[
								Sequelize.literal(
									`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`
								),
								'initials'
							]
						],
						include: {
							model: FoodOptionsModel,
							as: 'foodOptions',
							attributes: { exclude: ['createdAt', 'updatedAt', 'isDeleted'] }
						}
					},
					{
						model: SegmentProductTagsModel,
						attributes: ['id'],
						include: {
							model: SegmentTagsModel,
							attributes: ['id', 'name']
						}
					}
				],
				order: [
					[
						Sequelize.literal(
							'`product_variant_types.productVariantSubTypes.extraSequence`'
						),
						'ASC'
					],
					[
						Sequelize.literal(
							'`product_variant_types.productVariantSubTypes.id`'
						),
						'ASC'
					],
					[Sequelize.literal('`product_extras.extraSequence`'), 'ASC'],
					[Sequelize.literal('`product_extras.id`'), 'ASC']
				]
				// order: [["productVariantTypes", "id", "asc"], ["productVariantTypes", "productVariantSubTypes", "extraSequence", "asc"], ["productFoodOptions", 'id', 'ASC']]
			});
			if (listProduct == null) {
				return 0;
			}
			return listProduct;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	// Api to get time slot(active hour & inactive hours) of an Item....
	async getItemActiveHours(req, res) {
		try {
			let barID = req.body.bar_id;
			let subCategoryID = req.body.sub_category_id;

			let data = await ItemActiveHoursModel.findAll({
				where: { subCategoryID: subCategoryID, barID: barID },
				attributes: [
					[
						Sequelize.literal(
							"`weekDay`, JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'activeHours',TIME_FORMAT(`activeHours`,'%H:%i'),'weekday',`weekDay`, 'inActiveHours', TIME_FORMAT(`inActiveHours`,'%H:%i'),'status',`status`))"
						),
						'WeekDay_ItemActiveHours'
					]
				],
				group: ['weekDay'],
				order: ['weekDay'],
				raw: true
			});

			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	// Api to get time slot(active hour & inactive hours) of an Item....
	async addUpdateItemActiveHours(req, res) {
		try {
			let itemActiveHoursId = req.body.id;

			if (itemActiveHoursId) {
				/* update data */
				let barID = req.body.bar_id;
				let subCategoryID = req.body.sub_category_id;
				let itemActiveHoursId = req.body.id;
				let acHours = moment(`${req.body.active_hours}`, 'HH:mm:ss');
				let inAcHours = moment(`${req.body.in_active_hours}`, 'HH:mm:ss');

				let result = await ItemActiveHoursModel.findAll({
					where: { subCategoryID: subCategoryID, barID: barID },
					attributes: [
						'id',
						'weekDay',
						'activeHours',
						'inActiveHours',
						'status'
					]
				});

				let failure = false;
				if (
					moment(inAcHours).isBefore(acHours) ||
					moment(acHours).isAfter(inAcHours)
				) {
					failure = true;
					return 0;
				} else {
					result.forEach((element) => {
						if (req.body.weekDay == element.dataValues.weekDay) {
							let activeHRS = moment(
								`${element.dataValues.activeHours}`,
								'HH:mm:ss'
							);
							let inActiveHRS = moment(
								`${element.dataValues.inActiveHours}`,
								'HH:mm:ss'
							);

							if (
								(moment(acHours).isBetween(activeHRS, inActiveHRS) ||
									moment(inAcHours).isBetween(activeHRS, inActiveHRS) ||
									moment(acHours).isSame(inActiveHRS) ||
									moment(inAcHours).isSame(activeHRS)) &&
								itemActiveHoursId != element.dataValues.id
							) {
								failure = true;
							}
							if (
								(moment(activeHRS).isBetween(acHours, inAcHours) ||
									moment(inActiveHRS).isBetween(acHours, inAcHours) ||
									moment(activeHRS).isSame(inAcHours) ||
									moment(inActiveHRS).isSame(acHours)) &&
								itemActiveHoursId != element.dataValues.id
							) {
								failure = true;
							}
						}
					});
					if (failure === false) {
						await ItemActiveHoursModel.update(
							{
								activeHours: moment(acHours).format('HH:mm:ss'),
								inActiveHours: moment(inAcHours).format('HH:mm:ss'),
								status: req.body.status
							},
							{ where: { id: itemActiveHoursId } }
						);

						await SubCategoryWaitTimeModel.destroy({
							where: { itemActiveHoursId: itemActiveHoursId }
						});
						let subCategoryWaitTimeData = commonFunction.timeChunkArray(
							acHours,
							inAcHours,
							'60'
						);
						let subCategoryWaitTimeArray =
							subCategoryWaitTimeData &&
							subCategoryWaitTimeData.map((item) => ({
								...item,
								barID: barID,
								subCategoryID: subCategoryID,
								itemActiveHoursID: itemActiveHoursId,
								weekDay: req.body.week_day,
								waitTime: '00:10:00' //default 10 minutes
							}));
						if (subCategoryWaitTimeArray) {
							await SubCategoryWaitTimeModel.bulkCreate(
								subCategoryWaitTimeArray
							).catch(function (err) {
								console.log(err);
							});
						}

						//updated successfully
						return 1;
					} else {
						// Time-Slot already exists
						return 2;
					}
				}
			} else {
				/* add time slote */
				var barID = req.body.bar_id;
				var subCategoryID = req.body.sub_category_id;
				var acHours = moment(`${req.body.active_hours}`, 'HH:mm:ss');
				var inAcHours = moment(`${req.body.in_active_hours}`, 'HH:mm:ss');

				let result = await ItemActiveHoursModel.findAll({
					where: { subCategoryID: subCategoryID, barID: barID },
					attributes: ['weekDay', 'activeHours', 'inActiveHours']
				});

				let failure = false;
				if (
					moment(inAcHours).isBefore(acHours) ||
					moment(acHours).isAfter(inAcHours)
				) {
					failure = true;

					return 0;
				} else {
					result.forEach((element) => {
						if (req.body.week_day == element.dataValues.weekDay) {
							let activeHRS = moment(
								`${element.dataValues.activeHours}`,
								'HH:mm:ss'
							);

							let inActiveHRS = moment(
								`${element.dataValues.inActiveHours}`,
								'HH:mm:ss'
							);

							if (
								moment(acHours).isBetween(activeHRS, inActiveHRS) ||
								moment(inAcHours).isBetween(activeHRS, inActiveHRS) ||
								moment(acHours).isSame(inActiveHRS) ||
								moment(inAcHours).isSame(activeHRS)
							) {
								failure = true;
							}
							if (
								moment(activeHRS).isBetween(acHours, inAcHours) ||
								moment(inActiveHRS).isBetween(acHours, inAcHours) ||
								moment(activeHRS).isSame(inAcHours) ||
								moment(inActiveHRS).isSame(acHours)
							) {
								failure = true;
							}
						}
					});
					if (failure === false) {
						let itemActiveHoursRecord = await ItemActiveHoursModel.create({
							barID: barID,
							subCategoryID: subCategoryID,
							activeHours: moment(acHours).format('HH:mm:ss'),
							inActiveHours: moment(inAcHours).format('HH:mm:ss'),
							weekDay: req.body.week_day,
							status: req.body.status
						});

						let subCategoryData = commonFunction.timeChunkArray(
							acHours,
							inAcHours,
							'60'
						);
						let subCategoryWaitTimeArray =
							subCategoryData &&
							subCategoryData.map((item) => ({
								...item,
								barID: barID,
								subCategoryID: subCategoryID,
								itemActiveHoursID: itemActiveHoursRecord.id,
								weekDay: req.body.week_day,
								waitTime: '00:10:00' //default 10 minutes
							}));
						if (subCategoryWaitTimeArray) {
							await SubCategoryWaitTimeModel.bulkCreate(
								subCategoryWaitTimeArray
							).catch(function (err) {
								console.log(err);
							});
						}

						/* Time-slot added */
						return 4;
					} else {
						/* Time-Slot already exists! Enter another one */
						return 2;
					}
				}
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* product sequence */
	async productSequence(req, res) {
		try {
			const barID = req.body.bar_id;
			const productsIds = req.body.product_ids;
			const categoryId = req.body.category_id;
			const subCategoryId = req.body.sub_category_id;

			let subCategoryExist = await SubCategoryModel.findOne({
				where: { id: subCategoryId }
			});
			if (!subCategoryExist) {
				return 0;
			}

			let count = await ProductModel.count({
				where: { isDeleted: 'No', id: productsIds }
			});

			if (count > 0) {
				//destroy barsequence if exist
				await BarProductSequenceModel.destroy({
					where: {
						barId: barID,
						subCategoryId: subCategoryId
					}
				});

				productsIds.map(async (product, index) => {
					await BarProductSequenceModel.create({
						barId: barID,
						productSequence: index + 1,
						subCategoryId: subCategoryId,
						productId: product,
						categoryId: categoryId
					});
				});

				return 1;
			} else {
				return 2;
			}
		} catch (error) {
			console.log('err', err);
			throw error;
		}
	},
	/* pickup location list */
	async pickupLocationList(req, res) {
		try {
			let barId = req.body.bar_id;
			let data = await pickupLocationModel.findAll({
				where: {
					barID: barId,
					isDeleted: 'No'
				},
				attributes: { exclude: ['isDeleted', 'createdAt', 'updatedAt'] }
			});

			return data;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},
	/* delete item active hours*/
	async deleteItemActiveHours(req, res) {
		try {
			let itemHours = await ItemActiveHoursModel.findOne({
				where: {
					id: req.body.id
				}
			});

			if (itemHours) {
				await SubCategoryWaitTimeModel.destroy({
					where: { itemActiveHoursId: req.body.id }
				});

				await ItemActiveHoursModel.destroy({
					where: {
						id: req.body.id
					}
				});

				return 1;
			}
			return 0;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	//functions

	fetchPopularProductList: async (
		barID,
		categoryID = null,
		subCategoryIDs = null,
		selectedServiceType
	) => {
		var productWhereClause = [];
		if (subCategoryIDs.length == 0) {
			productWhereClause.push({
				isDeleted: 'No',
				barID: barID
			});
		} else {
			productWhereClause.push({
				isDeleted: 'No',
				barID: barID,
				subCategoryID: subCategoryIDs,
				status: 'Active'
			});
		}

		if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			productWhereClause.push({
				[Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

		// if (status == 'Active') {
		//   productWhereClause.push({
		//     status: 'Active'
		//   })
		// }

		if (categoryID) {
			productWhereClause.push({
				categoryID: categoryID
			});
		}

		let data = await ProductModel.findAll({
			attributes: [
				'id',
				'barID',
				'categoryID',
				'subCategoryID',
				'name',
				'description',
				'price',
				'pickupLocationID',
				'status',
				'avatar',
				'serviceType',
				'stock',
				'isStockLimit',
				'dailyStockRenewal',
				'isDailyStockRenewal',
				// [
				//   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
				//   'avatar'
				// ],
				[
					Sequelize.literal(
						`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`
					),
					'productIsValid'
				],
				// [
				// 	Sequelize.literal(
				// 		'(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
				// 	),
				// 	'minPrice'
				// ],
				// [
				// 	Sequelize.literal(
				// 		'(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
				// 	),
				// 	'maxPrice'
				// ],
				[
					Sequelize.literal(
						'(select SUM(quantity) from order_items WHERE order_items.productID = `product`.id)'
					),
					'soldQuantity'
				]
			],
			include: {
				model: ProductFoodOptionsModel,
				as: 'productFoodOptions',
				attributes: [
					'id',
					'productID',
					'foodOptionID',
					[
						Sequelize.literal(
							`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`
						),
						'name'
					],
					[
						Sequelize.literal(
							`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`
						),
						'initials'
					]
				],
				include: {
					model: FoodOptionsModel,
					as: 'foodOptions',
					attributes: [],
					order: [
						[Sequelize.fn('length', Sequelize.col('initials')), 'ASC'],
						['initials', 'ASC']
					]
				}
			},
			where: productWhereClause,
			order: [Sequelize.literal('soldQuantity DESC'), ['id', 'DESC']],
			distinct: true
		});
		console.log('Popular product details query run');
		let productResponse = [];
		for (let index = 0; index < 5; index++) {
			let product = data[index];
			if (
				product &&
				product.dataValues.productIsValid == '1' &&
				product.dataValues.soldQuantity > '0'
			) {
				productResponse.push(product);
			}
		}
		let resData = {
			categoryID: 0,
			maincategoryID: 0,
			categoryName: 'Popular',
			categoryProduct: productResponse,
			categoryActive: 1
		};
		return {
			key: 'categoryWiseProduct',
			value: resData
		};
	},
	fetchProductList: async (
		category,
		barID,
		status = 'Active',
		selectedServiceType,
		search = undefined
	) => {
		var productWhereClause = [];
		productWhereClause.push({
			isDeleted: 'No',
			subCategoryID: category.id,
			barID: barID
		});

		if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			productWhereClause.push({
				[Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

		if (status == 'Active') {
			productWhereClause.push({
				status: 'Active'
			});
		}

		if (search) {
			productWhereClause.push({
				name: {
					[Op.like]: '%' + search + '%'
				}
			});
		}

		let productFetchResponse = await ProductModel.findAll({
			attributes: [
				'id',
				'barID',
				'categoryID',
				'subCategoryID',
				'name',
				'description',
				'price',
				'pickupLocationID',
				'status',
				'avatar',
				'serviceType',
				'stock',
				'isStockLimit',
				'dailyStockRenewal',
				'isDailyStockRenewal'
				// [
				//   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
				//   'avatar'
				// ],
				// [
				// 	Sequelize.literal(
				// 		'(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
				// 	),
				// 	'minPrice'
				// ],
				// [
				// 	Sequelize.literal(
				// 		'(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
				// 	),
				// 	'maxPrice'
				// ]
			],
			include: [
				{
					model: ProductFoodOptionsModel,
					as: 'productFoodOptions',
					attributes: [
						'id',
						'productID',
						'foodOptionID',
						[
							Sequelize.literal(
								`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`
							),
							'name'
						],
						[
							Sequelize.literal(
								`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`
							),
							'initials'
						]
					],
					include: {
						model: FoodOptionsModel,
						as: 'foodOptions',
						attributes: []
					},
					order: [
						[
							Sequelize.fn(
								'length',
								Sequelize.col('`productFoodOptions->foodOptions`.`initials`')
							),
							'ASC'
						],
						['productFoodOptions', 'foodOptions', 'initials', 'ASC']
					] // 8. Ability to rearrange menu products under subheadings
				},
				// 8. Ability to rearrange menu products under subheadings start
				{
					model: BarProductSequenceModel,
					as: 'bar_product_sequence',
					required: false,
					attributes: [
						[
							Sequelize.fn(
								'coalesce',
								Sequelize.col('productSequence'),
								'infinite'
							),
							'productSequence'
						]
					]
				} // 8. Ability to rearrange menu products under subheadings end
			],
			where: productWhereClause,
			distinct: true,
			order: [
				[Sequelize.literal(`bar_product_sequence.productSequence`), 'Desc'],
				'id'
			] // 8. Ability to rearrange menu products under subheadings add
			// order: [["id", "DESC"]],
		});

		var resData = {
			categoryID: category.id,
			mainCategoryID: category.categoryID,
			categoryName: category.name,
			categoryProduct: productFetchResponse,
			categoryActive: category.dataValues.operatingFlag
				? category.dataValues.operatingFlag
				: 0
		};
		return {
			key: 'categoryWiseProduct',
			value: resData
		};
	},

	fetchProductListV2: async (
		category,
		barID,
		status = 'Active',
		selectedServiceType,
		search = undefined,
		page = undefined,
		limit = undefined
	) => {
		let productWhereClause = [
			{ isDeleted: 'No' },
			{ subCategoryID: category },
			{ barID: barID }
		];

		if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			productWhereClause.push({
				[Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

		if (status == 'Active') {
			productWhereClause.push({ status: 'Active' });
		}

		if (search) {
			productWhereClause.push({ name: { [Op.like]: '%' + search + '%' } });
		}

		let query = {
			attributes: [
				'id',
				'barID',
				'categoryID',
				'subCategoryID',
				'name',
				'description',
				'price',
				'pickupLocationID',
				'status',
				'avatar',
				'serviceType',
				'isStockLimit',
				'dailyStockRenewal',
				'isDailyStockRenewal'
			],
			include: [
				{
					model: BarProductSequenceModel,
					as: 'bar_product_sequence',
					required: false,
					attributes: [
						[
							Sequelize.fn(
								'coalesce',
								Sequelize.col('bar_product_sequence.productSequence'),
								'infinite'
							),
							'productSequence'
						]
					]
				},
				{
					model: ProductFoodOptionsModel,
					as: 'productFoodOptions',
					attributes: [
						'id',
						'productID',
						'foodOptionID',
						[
							Sequelize.literal(
								`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`
							),
							'name'
						],
						[
							Sequelize.literal(
								`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`
							),
							'initials'
						]
					],
					include: {
						model: FoodOptionsModel,
						as: 'foodOptions',
						attributes: []
					},
					order: [
						[
							Sequelize.fn(
								'length',
								Sequelize.col('`productFoodOptions->foodOptions`.`initials`')
							),
							'ASC'
						],
						['productFoodOptions', 'foodOptions', 'initials', 'ASC']
					]
				}
			],
			where: productWhereClause,
			distinct: true,
			order: [
				[
					{ model: BarProductSequenceModel, as: 'bar_product_sequence' },
					'productSequence',
					'DESC'
				],
				['id', 'ASC']
			]
		};

		if (page) {
			const start = page > 1 ? (page - 1) * (limit || constant.LIMIT) : 0;
			query.limit = limit || constant.LIMIT;
			query.offset = start;
		}

		let { count: totalCount, rows: productData } =
			await ProductModel.findAndCountAll(query);

		return {
			products: productData,
			totalCount: totalCount
		};
	},
	fetchPopularProductListV2: async (
		barID,
		categoryID = null,
		subCategoryIDs = null,
		selectedServiceType,
		page,
		limit
	) => {
		let productWhereClause = [];
		if (subCategoryIDs.length == 0) {
			productWhereClause.push({ isDeleted: 'No' }, { barID: barID });
		} else {
			productWhereClause.push(
				{ isDeleted: 'No' },
				{ barID: barID },
				{ subCategoryID: subCategoryIDs },
				{ status: 'Active' }
			);
		}

		if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			productWhereClause.push({
				[Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

		if (categoryID) {
			productWhereClause.push({ categoryID: categoryID });
		}

		let query = {
			offset: page,
			limit: limit,
			attributes: [
				'id',
				'barID',
				'categoryID',
				'subCategoryID',
				'name',
				'description',
				'price',
				'pickupLocationID',
				'status',
				'avatar',
				'serviceType',
				'stock',
				'isStockLimit',
				'dailyStockRenewal',
				'isDailyStockRenewal',
				[
					Sequelize.literal(
						`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`
					),
					'productIsValid'
				],
				[
					Sequelize.literal(
						'(select SUM(quantity) from order_items WHERE order_items.productID = `product`.id)'
					),
					'soldQuantity'
				]
			],
			include: {
				model: ProductFoodOptionsModel,
				as: 'productFoodOptions',
				attributes: [
					'id',
					'productID',
					'foodOptionID',
					[
						Sequelize.literal(
							`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`
						),
						'name'
					],
					[
						Sequelize.literal(
							`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`
						),
						'initials'
					]
				],
				include: {
					model: FoodOptionsModel,
					as: 'foodOptions',
					attributes: [],
					order: [
						[Sequelize.fn('length', Sequelize.col('initials')), 'ASC'],
						['initials', 'ASC']
					]
				}
			},
			where: productWhereClause,
			order: [Sequelize.literal('soldQuantity DESC'), ['id', 'DESC']],
			distinct: true
		};

		let { count: totalCount, rows: data } = await ProductModel.findAndCountAll(
			query
		);

		let productResponse = [];
		for (let index = 0; index < data.length; index++) {
			let product = data[index];
			if (
				product &&
				product.dataValues.productIsValid == '1' &&
				product.dataValues.soldQuantity > '0'
			) {
				productResponse.push(product);
			}
		}

		return {
			products: productResponse,
			totalCount: productResponse.length //totalCount
		};
	}
};
