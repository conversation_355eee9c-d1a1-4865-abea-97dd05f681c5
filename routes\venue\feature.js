const express = require('express');
const router = express();
const featureController = require('../../controllers/venue/featureController');
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueUserConnectedWithBar =
    require("../../middleware/venueAuth").isVenueUserConnectedWithBar;

//enable-disable my-tab premium feature
router.post(
    '/change-status',
    venueAuthorization,
    IsVenueUserConnectedWithBar,
    featureController.changeStatus
);

module.exports = router;