'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class bar_sub_category_opening_hours extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
			bar_sub_category_opening_hours.belongsTo(models.bar, {
				foreignKey: 'barID',
				targetKey: 'id'
			});
		}
	}
	bar_sub_category_opening_hours.init(
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			subCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			weekDay: { type: DataTypes.INTEGER, defaultValue: '0' },
			openingHours: DataTypes.TIME,
			closingHours: DataTypes.TIME,
			isClosed: { type: DataTypes.BOOLEAN, defaultValue: '0' },
			timeZone: { type: DataTypes.STRING, defaultValue: 'Australia/Perth' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'bar_sub_category_opening_hours',
			timestamps: true,
			freezeTableName: true
		}
	);
	return bar_sub_category_opening_hours;
};
