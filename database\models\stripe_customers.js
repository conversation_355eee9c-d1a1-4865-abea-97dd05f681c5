module.exports = (sequelize, DataTypes) => {
  const StripeCustomers = sequelize.define(
    'stripe_customers',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      user_type: {
        type: DataTypes.ENUM('advertiser', 'venue', 'cms'),
        allowNull: false
      },
      stripe_customer_id: {
        type: DataTypes.STRING,
        allowNull: false
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      }
    },
    {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    }
  );

  return StripeCustomers;
};