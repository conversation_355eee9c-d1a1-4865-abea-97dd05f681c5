"use strict";
const { Model } = require("sequelize");
const constant = require("../../config/constant");
module.exports = (sequelize, DataTypes) => {
  class coupon extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      coupon.belongsTo(models.bar, { foreignKey: "barID", targetKey: "id" });
      models.bar.hasMany(coupon, { foreignKey: "barID" });
      coupon.belongsTo(models.sub_category, { foreignKey: "subcategoryID" }); // 9. Promo codes - Subheading specific....
      models.sub_category.hasMany(coupon, { foreignKey: "subcategoryID" }); // 9. Promo codes - Subheading specific....
    }
  }
  coupon.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      barID: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "bar",
          key: "id",
        },
      },
      code: DataTypes.STRING(255),
      name: DataTypes.STRING(255),
      description: DataTypes.TEXT,
      max_uses: {
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      max_uses_user: {
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      discount_amount: DataTypes.FLOAT,
      is_fixed: { type: DataTypes.ENUM("Yes", "No"), defaultValue: "No" },
      startsAt: DataTypes.DATEONLY,
      expiresAt: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        defaultValue: null,
      },
      status: {
        type: DataTypes.ENUM("Active", "Inactive"),
        defaultValue: "Active",
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
      isDeleted: {
        type: DataTypes.ENUM("Yes", "No"),
        defaultValue: "No",
      },
    },
    {
      sequelize,
      modelName: "coupon",
      timestamps: true,
    }
  );
  return coupon;
};
