const message = require("../../config/cmsMessage").cmsMessage;
const status = require("../../config/status").status;
const taxService = require("../../services/venue/taxService");
const taxValidation = require("../../validations/venue/taxValidation");

module.exports = {
  /* add Promocode */
  async add(req, res) {
    try {
      // validation
      const valid = await taxValidation.add(req);
      if (valid.error) {
        return response(
          res,
          status.BAD_REQUEST_CODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let data = await taxService.add(req, res);

      if (data == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TAXALREADYEXIST,
          status.ERROR
        );
      } else if (data == 1) {
        //response on old password mis-match
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TAXADDEDSUCESSFULLY,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*list promocode */
  async list(req, res) {
    try {
      // validation
      const valid = await taxValidation.list(req);
      if (valid.error) {
        return response(
          res,
          status.BAD_REQUEST_CODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let data = await taxService.list(req, res);

      if (data) {
        return response(
          res,
          status.SUCCESSSTATUS,
          data,
          message.TAXFETCHEDSUCESSFULLY,
          status.SUCCESS
        );
      } else if (data == 0) {
        //response on old password mis-match
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.NOTAXFOUND,
          status.ERROR
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*edit Promocode */
  async edit(req, res) {
    try {
      // validation
      const valid = await taxValidation.edit(req);
      if (valid.error) {
        return response(
          res,
          status.BAD_REQUEST_CODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let data = await taxService.edit(req, res);

      if (data == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.NOTAXFOUND,
          status.ERROR
        );
      } else if (data == 1) {
        //response on old password mis-match
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TAXALREADYEXIST,
          status.ERROR
        );
      } else if (data == 2) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TAXUPDATEDSUCESSFULLY,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
  /*delete  promocode*/
  async delete(req, res) {
    try {
      // validation
      const valid = await taxValidation.delete(req);
      if (valid.error) {
        return response(
          res,
          status.BAD_REQUEST_CODE,
          {},
          valid.error.details[0].message,
          status.ERROR
        );
      }
      let data = await taxService.delete(req, res);
      if (data == 0) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.NOTFOUND,
          status.ERROR
        );
      } else if (data == 1) {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.TAXDELETEDSUCCESSFULLY,
          status.SUCCESS
        );
      } else {
        return response(
          res,
          status.SUCCESSSTATUS,
          {},
          message.SOMETHINGWENTWRONG,
          status.ERROR
        );
      }
    } catch (error) {
      //response on internal server error
      return response(
        res,
        status.INTERNALSERVERERRORSTATUS,
        [],
        message.INTERNALSERVERERROR,
        status.ERROR
      );
    }
  },
};
