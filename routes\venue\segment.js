var express = require('express');
var router = express.Router();

/* require for Authentication */
const { venueAuthorization } = require('../../middleware/venueAuth');
const IsVenueConnectedWithBar =
	require('../../middleware/venueAuth').isVenueUserConnectedWithBar;

const SegmentController = require('../../controllers/venue/segmentController.js');

/* Segment list API */
router.post(
	'/list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.list
);

router.post(
	'/all-segment-list',
	// venueAuthorization,
	SegmentController.allSegmentList
);

router.post(
	'/export-list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.exportList
);
router.post(
	'/details',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.details
);
router.post(
	'/customer-list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.customerList
);
router.post(
	'/export-customer-list',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.exportCustomerList
);
router.post(
	'/download',
	venueAuthorization,
	IsVenueConnectedWithBar,
	SegmentController.download
);

module.exports = router;
