"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class pos_conf extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      pos_conf.belongsTo(models.admin, {
        foreignKey: "assigned_to",
        targetKey: "id",
        onDelete: "CASCADE",
      });
    }
  }
  pos_conf.init(
    {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
      },
      posName: DataTypes.STRING(100),
      posConf: {
        type: DataTypes.STRING,
      },
      posConf: {
        type: DataTypes.STRING,
      },

      createdAt: { type: DataTypes.DATE, allowNull: false },
      updatedAt: { type: DataTypes.DATE, allowNull: false },
    },
    {
      sequelize,
      modelName: "pos_conf",
      timestamps: true,
      freezeTableName: true,
    }
  );
  return pos_conf;
};
