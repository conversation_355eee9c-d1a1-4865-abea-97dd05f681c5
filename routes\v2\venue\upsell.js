var express = require('express');
var router = express.Router();

const Authorization =
	require('../../../middleware/venueAuth').venueAuthorization;
const IsVenueUserConnectedWithBar =
	require('../../../middleware/venueAuth').isVenueUserConnectedWithBar;

const UpsellController = require('../../../controllers/v2/venue/upsellController');

router
	.route('/')
	.post(
		Authorization,
		IsVenueUserConnectedWithBar,
		UpsellController.addVenueSubCategoryUpsell
	)
	.put(
		Authorization,
		IsVenueUserConnectedWithBar,
		UpsellController.updateVenueSubCategoryUpsell
	)
	.delete(
		Authorization,
		IsVenueUserConnectedWithBar,
		UpsellController.deleteVenueSubCategoryUpsell
	);

router.post(
	'/list',
	Authorization,
	IsVenueUserConnectedWithBar,
	UpsellController.getVenueSubCategoryUpsell
);

module.exports = router;
