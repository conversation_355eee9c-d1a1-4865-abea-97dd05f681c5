const Joi = require('joi');

module.exports = {
	async logoutDeviceValidation(req) {
		const schema = Joi.object({
			id: Joi.number().required().messages({
				'any.required': 'Id is required.',
				'number.empty': 'Id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async changePasswordValidation(req) {
		const schema = Joi.object({
			old_password: Joi.string().required().messages({
				'any.required': 'Old Password is required.',
				'string.empty': 'Old Password is required.'
			}),
			new_password: Joi.string().required().messages({
				'any.required': 'New Password is required.',
				'string.empty': 'New Password is required.'
			})
		});
		return schema.validate(req.body);
	},	
	async contactUsValidation(req) {
		const schema = Joi.object({
			name: Joi.string().required().messages({
				'any.required': 'Name is required.',
				'string.empty': 'Name is required.'
			}),
			message: Joi.string().required().messages({
				'any.required': 'Message is required.',
				'string.empty': 'Message is required.'
			}),
			email: Joi.string().email().required().messages({
				'any.required': 'Email is required.',
				'string.email': 'Email is not valid.',
				'string.empty': 'Email is required.'
			})
		});
		return schema.validate(req.body);
	},
	async verifyOtpValidation(req) {
		const schema = Joi.object({
			otp: Joi.string().required().messages({
				'any.required': 'Otp is required.',
				'string.empty': 'Otp is required.'
			})
		});
		return schema.validate(req.body);
	}
};
