/*Messages,status code and services require*/
require('dotenv').config();

const commonFunction = require('../../common/commonFunction');
const order = require('../../database/models').orders;
const orderItems = require('../../database/models').order_items;
const orderItemExtras = require('../../database/models').order_item_extras;
const orderItemVariants = require('../../database/models').order_item_variants;
const orderTableNumber = require('../../database/models').orderTableNumber;
const taxModel = require('../../database/models').tax;
const coupons = require('../../database/models').coupons;
const orderProductVariantTypes =
	require('../../database/models').order_product_variant_types;
const orderProductVariantSubTypes =
	require('../../database/models').order_product_variant_sub_types;
const OrderItemWaitTimeNotificationsModel =
	require('../../database/models').order_item_wait_time_notifications;
const productVariantTypes =
	require('../../database/models').product_variant_types;
const productVariantSubTypes =
	require('../../database/models').product_variant_sub_types;
const user = require('../../database/models').user;
const bar = require('../../database/models').bar;
const orderTax = require('../../database/models').order_tax;
const orderRefundTax = require('../../database/models').order_refund_tax;
const product = require('../../database/models').product;
const productVariants = require('../../database/models').product_variants;
const productExtras = require('../../database/models').product_extras;
const pickupLocation = require('../../database/models').pickup_location;
const transErrorLogsModel =
	require('../../database/models').transaction_err_logs;
const transLogsModel = require('../../database/models').transaction_logs;
const UserNotificationModel =
	require('../../database/models').user_notification;
const constant = require('../../config/constant');
const { Parser } = require('json2csv');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const moment = require('moment');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const sequelize = require('sequelize');
const convertUTCToVenueTimezone = require('../../helper/generalHelper').convertUTCToVenueTimezone;

module.exports = {
	/*  bar order history */
	async barOrderListCount(req, res) {
		try {
			const barID = 42;
			const whereClauseProduct = [];
			const dashboardOrder = {};
			let pickupOrder = [];
			let newOrder = [];
			let havingClauseOrder = [
				// {
				// 	totalCancelItem: {
				// 		[Op.gt]: 0
				// 	}
				// }
			];

			const orderAttributes = [
				'id',
				'orderNo',
				'subTotal',
				'transactionFee',
				'tax',
				'total',
				'orderDate',
				'orderStatus',
				'refundStatus',
				'promocode_id',
				'promocode_amount',
				'promocode_discount',
				'userID',
				'barID',
				'posOrderStatus',
				'orderServiceType',
				'createdAt',
				'docketPrintingStatus',
				'pickupCode'
				// [
				// 	Sequelize.literal(
				// 		'(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
				// 	),
				// 	'totalCancelItem'
				// ]
			];
			newOrder = await order.findAll({
				where: [
					{
						isDeleted: 'No',
						paymentStatus: 'received',
						barID: barID,
						isCanceled: 'No',
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
						],
						[Op.not]: [
							{
								orderStatus: 'Intoxicated'
							}
						]
					}
				],
				attributes: orderAttributes,
				include: [
					{
						required: true,
						// attributes: itemAttributes,
						where: [
							{
								[Op.or]: [
									{
										orderStatus: 'New'
									},
									{
										orderStatus: 'Preparing'
									}
								],
								isCanceled: 'No'
							}
						],
						model: orderItems
						// include: [
						//   {
						//     where: [...whereClauseProduct],
						//     // attributes: productAttributes,
						//     model: product,
						//   }
						// ]
					}
					// {
					//   required: true,
					//   model: user,
					//   attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
					// }
				],
				order: [['createdAt', 'ASC']],
				distinct: true,
				duplicating: false
			});
			pickupOrder = await order.findAll({
				where: [
					{
						orderStatus: {
							[Op.notIn]: ['Intoxicated', 'Pickedup']
						},
						isDeleted: 'No',
						barID: barID,
						isCanceled: 'No',
						paymentStatus: 'received'
					}
				],
				having: havingClauseOrder,
				attributes: [...orderAttributes],
				include: [
					{
						required: true,
						// attributes: itemAttributes,
						model: orderItems,
						where: {
							orderStatus: 'Pickup',
							isCanceled: 'No'
						}
						// include: [
						//   {
						//     where: [...whereClauseProduct],
						//     // attributes: productAttributes,
						//     model: product
						//   }
						// ]
					}
					// {
					//   required: true,
					//   model: user,
					//   attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
					// }
				],
				order: [['updatedAt', 'ASC']],
				distinct: true,
				duplicating: false
			});
			return { newOrder: newOrder.length, pickupOrder: pickupOrder.length };
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	/*  bar order history */
	async barOrderHistory(req, res) {
		try {
			const offset = (req.body.page - 1) * constant.LIMIT;
			const limit = constant.LIMIT;
			const barID = req.body.bar_id;
			const whereClauseOrder = [];
			const whereClauseOrderItems = [];
			const whereClauseRefundedItem = [];

			const orderAttributes = [
				'id',
				'orderNo',
				'subTotal',
				'transactionFee',
				'refundTransactionFee',
				'tax',
				'total',
				'orderDate',
				'orderStatus',
				'refundStatus',
				'promocode_id',
				'promocode_amount',
				'paymentStatus',
				'promocode_discount',
				'userID',
				'barID',
				'stripeFee',
				'orderServiceType',
				'isPosOrder',
				'posOrderFee',
				'isDocketOrder',
				'docketOrderFee',
				[
					Sequelize.literal(
						'(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
					),
					'totalOrderTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
					),
					'totalOrderRefundTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_items.newRefundAmount) from order_items WHERE order_items.orderID = `orders`.id)'
					),
					'refundAmount'
				],
				[
					Sequelize.literal(
						'round(total - round(stripeFee, 2) - transactionFee, 2)'
					),
					'net_revenue'
				],
				'createdAt'
			];

			whereClauseOrder.push({
				isDeleted: 'No',
				barID: barID
			});

			// if(getUserCategory.length > 0) {
			//   whereClauseProduct.push({
			//     subCategoryID : getUserCategory
			//   });
			// }

			if (req.body.startDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.gte]: req.body.startDate
					}
				});
			}

			if (req.body.endDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.lte]: req.body.endDate
					}
				});
			}

			if (req.body.search) {
				whereClauseOrder.push({
					orderNo: {
						[Op.like]: '%' + req.body.search + '%'
					}
				});
			}

			if (req.body.orderType && req.body.orderType != '') {
				if (req.body.orderType == 'current') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
						],
						isCanceled: 'No',
						paymentStatus: 'received'
					});
					whereClauseOrderItems.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
							//   orderStatus: 'Pickup'
							// }
						],
						isCanceled: 'No'
					});
				} else if (req.body.orderType == 'past') {
					whereClauseOrderItems.push({
						orderStatus: {
							[Op.notIn]: ['New', 'Preparing']
						}
					});
					whereClauseOrder.push({
						//isCanceled: 'No',
						paymentStatus: 'received'
					});
				} else if (req.body.orderType == 'refund') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								refundStatus: 'Refunded'
							},
							{
								refundStatus: 'PartialRefunded'
							},
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							)
						],
						paymentStatus: 'received'
					});
					// whereClauseRefundedItem.push({
					//   [Op.or]: [
					//     {
					//       isCanceled: 'Yes'
					//     },
					//     {
					//       refundedQuantity: {
					//         [Op.gt]: 0
					//       }
					//     },
					//     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
					//   ]
					// })
				} else if (req.body.orderType == 'cancel') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'Yes'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'Intoxicated') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'No'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							),
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'promo_code') {
					whereClauseOrder.push({
						[Op.and]: [
							{
								promocode_id: {
									[Op.gt]: 0
								},
								promocode_amount: {
									[Op.gt]: 0
								}
							}
						]
					});
				}
			} else {
				whereClauseOrder.push({
					// [Op.or]: [
					//   {
					//     orderStatus: 'New'
					//   },
					//   {
					//     orderStatus: 'Preparing'
					//   },
					//   {
					//     orderStatus: 'Pickup'
					//   }
					// ],
					paymentStatus: 'received'
				});
				whereClauseOrderItems.push({
					// [Op.or]: [
					// 	{
					// 		orderStatus: 'New'
					// 	},
					// 	{
					// 		orderStatus: 'Preparing'
					// 	},
					// 	{
					// 		orderStatus: 'Pickup'
					// 	}
					// ]
				});
			}

			const orderHistoryList = await order.findAndCountAll({
				where: [...whereClauseOrder],
				attributes: [...orderAttributes, 'pickupCode'],
				include: [
					{
						required: true,
						attributes: [
							'id',
							'orderID',
							'productID',
							'price',
							'chargeAmount',
							'quantity',
							'specialRequest',
							'isCanceled',
							'refundAmount',
							'refundedQuantity',
							'waitTime',
							'orderStatus',
							'PreparingStartTime',
							'ReadyTime',
							'PickedupTime',
							[
								Sequelize.literal(
									`(ADDTIME(order_items.createdAt, waitTime)) `
								),
								'expectedTime'
							],
							[
								Sequelize.literal(
									`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
								),
								'pickupLocation'
							],
							[
								Sequelize.literal(
									`(select subCategoryID from product where product.id = order_items.productID ) `
								),
								'subCategoryID'
							]
						],
						model: orderItems,
						where: [...whereClauseRefundedItem, ...whereClauseOrderItems],
						include: [
							{
								// where: [ {categoryID: [1,2] }, ...whereClauseProduct ],
								attributes: [
									'id',
									'name',
									'categoryID',
									'subCategoryID',
									'description',
									'avatar',
									'posID',
									'pickupLocationID'
								],
								required: false,
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
								model: orderItemExtras,
								include: [
									{
										attributes: ['id', 'extraItem', 'posID'],
										model: productExtras
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
								model: orderItemVariants,
								include: [
									{
										attributes: ['id', 'variantType'],
										model: productVariants
									}
								]
							},
							{
								attributes: [
									['id', 'orderProductVariantTypeID'],
									'orderItemID'
								],
								where: Sequelize.where(
									Sequelize.col('`order_items`.`id`'),
									Sequelize.col(
										'`order_items->order_product_variant_types`.`orderItemID`'
									)
								),
								model: orderProductVariantTypes,
								required: false,
								include: [
									{
										attributes: [['id', 'productVariantTypeID'], 'label'],
										model: productVariantTypes,
										required: true,
										include: [
											{
												attributes: [
													['id', 'orderProductVariantSubTypeID'],
													'orderItemID'
												],
												// where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
												model: orderProductVariantSubTypes,
												include: [
													{
														attributes: [
															['id', 'productVariantSubTypeID'],
															['variantType', 'extraItem'],
															'price'
														],
														model: productVariantSubTypes
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						required: false,
						model: user,
						attributes: ['id', 'fullName', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: [
							'id',
							'code',
							'name',
							'description',
							'is_fixed',
							'discount_amount'
						]
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				],
				order: [['createdAt', 'DESC']],
				distinct: true,
				duplicating: false,
				offset: offset,
				limit: limit
			});
			if (orderHistoryList.count > 0) {
				for (let i = 0; i < orderHistoryList.rows.length; i++) {
					const order = orderHistoryList.rows[i];
					let newNetRevenue = orderHistoryList.rows[i].dataValues.net_revenue;
					if (orderHistoryList.rows[i].dataValues.refundStatus != 'no') {
						newNetRevenue =
							newNetRevenue -
							orderHistoryList.rows[i].dataValues.totalOrderRefundTax -
							orderHistoryList.rows[i].dataValues.refundAmount;
					}
					if (orderHistoryList.rows[i].dataValues.isPosOrder == 1) {
						var posCommissionFee =
							(orderHistoryList.rows[i].dataValues.posOrderFee / 100) *
							(orderHistoryList.rows[i].dataValues.subTotal +
								orderHistoryList.rows[i].dataValues.totalOrderTax);
						newNetRevenue = newNetRevenue - posCommissionFee;
						orderHistoryList.rows[i].dataValues.posOrderFee =
							Number(posCommissionFee).toFixed(2);
					}
					if (orderHistoryList.rows[i].dataValues.isDocketOrder == 1) {
						var docketCommissionFee =
							(orderHistoryList.rows[i].dataValues.docketOrderFee / 100) *
							(orderHistoryList.rows[i].dataValues.subTotal +
								orderHistoryList.rows[i].dataValues.totalOrderTax);
						newNetRevenue = newNetRevenue - docketCommissionFee;
						orderHistoryList.rows[i].dataValues.docketOrderFee =
							Number(docketCommissionFee).toFixed(2);
					}
					if (
						orderHistoryList.rows[i].dataValues.refundTransactionFee == 'Yes'
					) {
						newNetRevenue =
							newNetRevenue -
							orderHistoryList.rows[i].dataValues.transactionFee;
					}
					orderHistoryList.rows[i].dataValues.net_revenue =
						newNetRevenue.toFixed(2);
					let totalRefundAmount = Number(
						orderHistoryList.rows[i].dataValues.refundAmount +
							orderHistoryList.rows[i].dataValues.totalOrderRefundTax
					);
					if (
						orderHistoryList.rows[i].dataValues.refundTransactionFee == 'Yes'
					) {
						totalRefundAmount =
							totalRefundAmount +
							orderHistoryList.rows[i].dataValues.transactionFee;
					}
					orderHistoryList.rows[i].dataValues.refundAmount = totalRefundAmount;
					for (let j = 0; j < order.order_items.length; j++) {
						const item = orderHistoryList.rows[i].order_items[j];
						for (let k = 0; k < item.order_product_variant_types.length; k++) {
							const variant =
								orderHistoryList.rows[i].order_items[j]
									.order_product_variant_types[k];
							let getProductSubVariant =
								await orderProductVariantSubTypes.findOne({
									attributes: [
										['id', 'orderProductVariantSubTypeID'],
										'orderItemID',
										[
											Sequelize.literal(
												'(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'extraItem'
										],
										[
											Sequelize.literal(
												'(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'price'
										],
										[
											Sequelize.literal(
												'(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
											),
											'productVariantSubTypeID'
										]
									],
									where: {
										orderItemID: variant.orderItemID,
										productVariantTypeID:
											variant.product_variant_type.dataValues
												.productVariantTypeID
									}
								});

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID =
								getProductSubVariant.dataValues.productVariantSubTypeID;

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem =
								getProductSubVariant.dataValues.extraItem;

							orderHistoryList.rows[i].order_items[
								j
							].order_product_variant_types[
								k
							].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price =
								getProductSubVariant.dataValues.price;
						}
					}
				}
				orderHistoryList.rows =
					orderHistoryList &&
					orderHistoryList.rows.map((order) => {
						let newOrderItems = groupByOrderItems(
							order.order_items,
							order.orderServiceType,
							order.refundStatus
						); // Group By order items
						// delete order.dataValues.order_items; // Delete old key
						order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
						return order;
					});
				return orderHistoryList;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	/*  bar order list */
	async barOrderList(req, res) {
		try {
			const barID = req.body.bar_id;
			const dashboardOrder = {};
			let whereClauseOrder = {};

			let havingClauseOrder = [
				{
					totalCancelItem: {
						[Op.gt]: 0
					}
				}
			];

			const barDetails = await bar.findOne({
				attributes: [
					'id',
					'serviceType',
					'attachedPosConfig',
					'avatar',
					'docketStatus',
					'posStatus'
				],
				where: { id: barID }
			});

			if (barDetails.serviceType != 'BOTH') {
				whereClauseOrder = {
					orderServiceType: barDetails.serviceType
				};
			}

			const orderAttributes = [
				'id',
				'orderNo',
				// [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
				'subTotal',
				'transactionFee',
				'refundTransactionFee',
				'tax',
				'total',
				'orderDate',
				'orderStatus',
				'refundStatus',
				'promocode_id',
				'promocode_amount',
				'promocode_discount',
				'userID',
				'barID',
				'isPosOrder',
				'posOrderStatus',
				'orderServiceType',
				'createdAt',
				'isDocketOrder',
				'docketPrintingStatus',
				'pickupCode',
				[
					Sequelize.literal(
						'(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
					),
					'totalCancelItem'
				]
			];
			const itemAttributes = [
				'id',
				'orderID',
				'productID',
				'price',
				'quantity',
				'specialRequest',
				'isCanceled',
				'refundAmount',
				'chargeAmount',
				'discountedAmount',
				'newRefundAmount',
				'refundedQuantity',
				'waitTime',
				'orderStatus',
				'PreparingStartTime',
				'ReadyTime',
				'PickedupTime',
				[
					Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `),
					'expectedTime'
				],
				[
					Sequelize.literal(
						`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
					),
					'pickupLocation'
				],
				[
					Sequelize.literal(
						`(select subCategoryID from product where product.id = order_items.productID ) `
					),
					'subCategoryID'
				]
			];
			const productAttributes = [
				'id',
				'name',
				'categoryID',
				'description',
				'avatar',
				'subCategoryID'
			];

			let newOrder = await order.findAll({
				where: [
					{
						isDeleted: 'No',
						paymentStatus: 'received',
						barID: barID,
						isCanceled: 'No',
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
						],
						[Op.not]: [
							{
								orderStatus: 'Intoxicated'
							}
						],
						...whereClauseOrder
					}
				],
				attributes: orderAttributes,
				include: [
					{
						required: true,
						attributes: itemAttributes,
						where: [
							{
								[Op.or]: [
									{
										orderStatus: 'New'
									},
									{
										orderStatus: 'Preparing'
									}
								],
								isCanceled: 'No'
							}
						],
						model: orderItems,
						include: [
							{
								attributes: productAttributes,
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
								model: orderItemExtras,
								include: [
									{
										attributes: ['id', 'extraItem'],
										model: productExtras
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
								model: orderItemVariants,
								include: [
									{
										attributes: ['id', 'variantType'],
										model: productVariants
									}
								]
							},
							{
								attributes: [
									['id', 'orderProductVariantTypeID'],
									'orderItemID'
								],
								where: Sequelize.where(
									Sequelize.col('`order_items`.`id`'),
									Sequelize.col(
										'`order_items->order_product_variant_types`.`orderItemID`'
									)
								),
								model: orderProductVariantTypes,
								required: false,
								include: [
									{
										attributes: [['id', 'productVariantTypeID'], 'label'],
										model: productVariantTypes,
										required: true,
										include: [
											{
												attributes: [
													['id', 'orderProductVariantSubTypeID'],
													'orderItemID'
												],
												where: Sequelize.where(
													Sequelize.col(
														'`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'
													),
													Sequelize.col(
														'`order_items->order_product_variant_types`.`id`'
													)
												),
												model: orderProductVariantSubTypes,
												include: [
													{
														attributes: [
															['id', 'productVariantSubTypeID'],
															['variantType', 'extraItem'],
															'price'
														],
														model: productVariantSubTypes
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						required: true,
						model: user,
						attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: ['id', 'code', 'name', 'description']
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				],
				order: [['createdAt', 'ASC']],
				distinct: true,
				duplicating: false
			});

			let pickupOrder = await order.findAll({
				where: [
					{
						orderStatus: {
							[Op.notIn]: ['Intoxicated', 'Pickedup']
						},
						isDeleted: 'No',
						barID: barID,
						isCanceled: 'No',
						paymentStatus: 'received',
						...whereClauseOrder
					}
				],
				having: havingClauseOrder,
				attributes: [...orderAttributes],
				include: [
					{
						required: true,
						attributes: itemAttributes,
						model: orderItems,
						where: {
							orderStatus: 'Pickup',
							isCanceled: 'No'
						},
						include: [
							{
								attributes: productAttributes,
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
								model: orderItemExtras,
								include: [
									{
										attributes: ['id', 'extraItem'],
										model: productExtras
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
								model: orderItemVariants,
								include: [
									{
										attributes: ['id', 'variantType'],
										model: productVariants
									}
								]
							},
							{
								attributes: [
									['id', 'orderProductVariantTypeID'],
									'orderItemID'
								],
								where: Sequelize.where(
									Sequelize.col('`order_items`.`id`'),
									Sequelize.col(
										'`order_items->order_product_variant_types`.`orderItemID`'
									)
								),
								model: orderProductVariantTypes,
								required: false,
								include: [
									{
										attributes: [['id', 'productVariantTypeID'], 'label'],
										model: productVariantTypes,
										required: true,
										include: [
											{
												attributes: [
													['id', 'orderProductVariantSubTypeID'],
													'orderItemID'
												],
												where: Sequelize.where(
													Sequelize.col(
														'`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'
													),
													Sequelize.col(
														'`order_items->order_product_variant_types`.`id`'
													)
												),
												model: orderProductVariantSubTypes,
												include: [
													{
														attributes: [
															['id', 'productVariantSubTypeID'],
															['variantType', 'extraItem'],
															'price'
														],
														model: productVariantSubTypes
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						required: true,
						model: user,
						attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: ['id', 'code', 'name', 'description']
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				],
				order: [['updatedAt', 'ASC']],
				distinct: true,
				duplicating: false
			});

			let newOrdersObj = newOrder.map((order) => {
				let newOrderItems = groupByOrderItems(
					order.order_items,
					order.orderServiceType,
					order.refundStatus
				); // Group By order items
				// delete order.dataValues.order_items; // Delete old key
				order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
				return order;
			});

			dashboardOrder.newOrder = newOrdersObj;

			let pickupOrderObj = pickupOrder.map((order) => {
				let newOrderItems = groupByOrderItems(
					order.order_items,
					order.orderServiceType,
					order.refundStatus
				); // Group By order items
				// delete order.dataValues.order_items; // Delete old key
				order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
				return order;
			});

			dashboardOrder.pickupOrder = pickupOrderObj;

			return dashboardOrder;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	/*download order history*/
	async downloadOrderHistory(req, res) {
		try {
			const offset = 0;
			const limit = 1000000;
			const barID = req.body.bar_id;
			const whereClauseOrder = [];
			const whereClauseProduct = [];
			const whereClauseOrderItems = [];
			const whereClauseRefundedItem = [];			
			const convertedStartDate = await convertUTCToVenueTimezone(barID, req.body.startDate);
			const convertedEndDate = await convertUTCToVenueTimezone(barID, req.body.endDate);

			const orderAttributes = [
				'id',
				'orderNo',
				'subTotal',
				'transactionFee',
				'refundTransactionFee',
				'stripeFee',
				'tax',
				'total',
				'orderDate',
				'orderStatus',
				'paymentStatus',
				'refundStatus',
				'promocode_id',
				'promocode_amount',
				'promocode_discount',
				'userID',
				'barID',
				'orderServiceType',
				'isPosOrder',
				'posOrderFee',
				'isDocketOrder',
				'docketOrderFee',
				'pickupCode',
				[
					Sequelize.literal(
						'(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
					),
					'totalOrderTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
					),
					'totalOrderRefundTax'
				],
				[
					Sequelize.literal(
						'(select sum(order_items.newRefundAmount) from order_items WHERE order_items.orderID = `orders`.id)'
					),
					'refundAmount'
				],
				[
					Sequelize.literal(
						'round(total - round(stripeFee, 2) - transactionFee, 2)'
					),
					'net_revenue'
				],
				'createdAt'
			];

			whereClauseOrder.push({
				isDeleted: 'No',
				barID: barID
			});

			if (req.body.startDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.gte]: req.body.startDate
					}
				});
			}

			if (req.body.endDate) {
				whereClauseOrder.push({
					orderDate: {
						[Op.lte]: req.body.endDate
					}
				});
			}

			if (req.body.search) {
				whereClauseOrder.push({
					orderNo: {
						[Op.like]: '%' + req.body.search + '%'
					}
				});
			}

			if (req.body.orderType && req.body.orderType != '') {
				if (req.body.orderType == 'current') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
						],
						isCanceled: 'No',
						paymentStatus: 'received'
					});
					whereClauseOrderItems.push({
						[Op.or]: [
							{
								orderStatus: 'New'
							},
							{
								orderStatus: 'Preparing'
							}
							//   orderStatus: 'Pickup'
							// }
						],
						isCanceled: 'No'
					});
				} else if (req.body.orderType == 'past') {
					whereClauseOrderItems.push({
						orderStatus: {
							[Op.notIn]: ['New', 'Preparing']
						}
					});
					whereClauseOrder.push({
						//isCanceled: 'No',
						paymentStatus: 'received'
					});
				} else if (req.body.orderType == 'refund') {
					whereClauseOrder.push({
						[Op.or]: [
							{
								refundStatus: 'Refunded'
							},
							{
								refundStatus: 'PartialRefunded'
							},
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							)
						],
						paymentStatus: 'received'
					});
					// whereClauseRefundedItem.push({
					//   [Op.or]: [
					//     {
					//       isCanceled: 'Yes'
					//     },
					//     {
					//       refundedQuantity: {
					//         [Op.gt]: 0
					//       }
					//     },
					//     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
					//   ]
					// })
				} else if (req.body.orderType == 'cancel') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'Yes'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'Intoxicated') {
					whereClauseOrderItems.push({
						[Op.and]: [
							{
								isCanceled: 'No'
							}
						]
					});
					whereClauseOrder.push({
						[Op.and]: [
							sequelize.where(
								sequelize.col('orders.orderStatus'),
								'=',
								'Intoxicated'
							),
							{
								refundStatus: 'No'
							}
						]
					});
				} else if (req.body.orderType == 'promo_code') {
					whereClauseOrder.push({
						[Op.and]: [
							{
								promocode_id: {
									[Op.gt]: 0
								},
								promocode_amount: {
									[Op.gt]: 0
								}
							}
						]
					});
				}
			} else {
				whereClauseOrder.push({
					// [Op.or]: [
					//   {
					//     orderStatus: 'New'
					//   },
					//   {
					//     orderStatus: 'Preparing'
					//   },
					//   {
					//     orderStatus: 'Pickup'
					//   }
					// ],
					paymentStatus: 'received'
				});
				whereClauseOrderItems.push({
					// [Op.or]: [
					// 	{
					// 		orderStatus: 'New'
					// 	},
					// 	{
					// 		orderStatus: 'Preparing'
					// 	},
					// 	{
					// 		orderStatus: 'Pickup'
					// 	}
					// ]
				});
			}
			const orderHistoryList = await order.findAndCountAll({
				where: [...whereClauseOrder],
				attributes: [...orderAttributes],
				include: [
					{
						required: true,
						attributes: [
							'id',
							'orderID',
							'productID',
							'price',
							'quantity',
							'specialRequest',
							'isCanceled',
							'refundAmount',
							'refundedQuantity',
							'waitTime',
							'orderStatus',
							'PreparingStartTime',
							'ReadyTime',
							'PickedupTime',
							[
								Sequelize.literal(
									`(ADDTIME(order_items.createdAt, waitTime)) `
								),
								'expectedTime'
							],
							[
								Sequelize.literal(
									`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
								),
								'pickupLocation'
							],
							[
								Sequelize.literal(
									`(select subCategoryID from product where product.id = order_items.productID ) `
								),
								'subCategoryID'
							]
						],
						model: orderItems,
						where: [...whereClauseRefundedItem, ...whereClauseOrderItems],
						include: [
							{
								// where: { categoryID: [1, 2] },
								attributes: [
									'id',
									'name',
									'categoryID',
									'subCategoryID',
									'description',
									'avatar',
									'posID',
									'pickupLocationID'
								],
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							}
						]
					},
					{
						required: false,
						model: user,
						attributes: ['id', 'fullName', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: ['id', 'code', 'name', 'discount_amount', 'description']
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				],
				offset: offset,
				limit: limit,
				order: [['createdAt', 'DESC']],
				distinct: true,
				duplicating: false
			});

			const csvFormatedData = orderHistoryList.rows.map((order) => {
				if (
					order.dataValues.posOrderFee != '' &&
					order.dataValues.posOrderFee > 0
				) {
					order.dataValues.posOrderFee =
						(order.dataValues.posOrderFee / 100) *
						(order.dataValues.subTotal + order.dataValues.totalOrderTax);
					order.dataValues.net_revenue =
						order.dataValues.net_revenue - order.dataValues.posOrderFee;
				}
				if (
					order.dataValues.docketOrderFee != '' &&
					order.dataValues.docketOrderFee > 0
				) {
					order.dataValues.docketOrderFee =
						(order.dataValues.docketOrderFee / 100) *
						(order.dataValues.subTotal + order.dataValues.totalOrderTax);
					order.dataValues.net_revenue =
						order.dataValues.net_revenue - order.dataValues.docketOrderFee;
				}
				let transactionFeeRefundAmount = 0;
				if (order.dataValues.refundTransactionFee == 'Yes') {
					transactionFeeRefundAmount = order.dataValues.transactionFee;
				}
				if (order.dataValues.orderStatus == 'Pickedup') {
					if (order.dataValues.orderServiceType != 'PICKUP') {
						order.dataValues.orderStatus = 'Served';
					} else {
						order.dataValues.orderStatus = 'Picked up';
					}
				}
				if (order.dataValues.orderServiceType == 'PICKUP') {
					order.dataValues.orderServiceType = 'Takeaway';
				} else {
					order.dataValues.orderServiceType = 'Table';
				}
				return {
					'Order Date': order.orderDate,
					Customer: order?.user?.fullName ?? 'MyTab USer',
					'Order ID': order.orderNo,
					'Service Type': order.orderServiceType,
					'Pickup Code': order.pickupCode,
					'Table No': order.orderTableNumbers[0]?.tableCode,
					'Order Status': order.orderStatus.toUpperCase(),
					'Payment Status': order.paymentStatus.toUpperCase(),
					Taxes: order.dataValues.totalOrderTax?.toFixed(2),
					'Promo Code': order.coupon?.code,
					'Promo Code Amount': order.promocode_amount,
					'Promo Code (%)': order.coupon?.discount_amount,
					'Total Paid By Customer': order?.total,
					'MyTab Customer Fee': order.transactionFee,
					'Stripe Transaction Fee': order.stripeFee,
					'POS Integration Fee': Number(order.dataValues.posOrderFee).toFixed(
						2
					),
					'Docket Printing Fee': Number(
						order.dataValues.docketOrderFee
					).toFixed(2),
					Refund:
						order.dataValues.refundStatus.charAt(0).toUpperCase() +
						order.dataValues.refundStatus.slice(1),
					'Refunded Amount': (
						Number(order.dataValues.refundAmount) +
						Number(transactionFeeRefundAmount) +
						Number(order.dataValues.totalOrderRefundTax)
					).toFixed(2),
					'Net Revenue (Incl GST)': Number(
						order.dataValues.net_revenue -
							(Number(order.dataValues.refundAmount) +
								Number(transactionFeeRefundAmount) +
								Number(order.dataValues.totalOrderRefundTax))
					).toFixed(2)
				};
			});
			const fields = [
				'Order Date',
				'Customer',
				'Order ID',
				'Service Type',
				'Pickup Code',
				'Table No',
				'Order Status',
				'Payment Status',
				'Taxes',
				'Promo Code',
				'Promo Code Amount',
				'Promo Code (%)',
				'Total Paid By Customer',
				'MyTab Customer Fee',
				'Stripe Transaction Fee',
				'POS Integration Fee',
				'Docket Printing Fee',
				'Refund',
				'Refunded Amount',
				'Net Revenue (Incl GST)'
			];
			const json2csvParser = new Parser({
				fields,
				defaultValue: 'NA',
				includeEmptyRows: true
			});

			return json2csvParser.parse(csvFormatedData);
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async orderView(req, res) {
		try {
			let whereClauseOrder = [];
			whereClauseOrder.push({
				isDeleted: 'No'
			});
			whereClauseOrder.push({
				barID: req.body.bar_id
			});
			whereClauseOrder.push({
				id: req.body.id
			});

			let orderResponse = await order.findOne({
				where: whereClauseOrder,
				attributes: [
					'id',
					'orderNo',
					'pickupCode',
					// [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
					'subTotal',
					'orderServiceType',
					'transactionFee',
					'tax',
					'total',
					'orderDate',
					'orderStatus',
					'refundStatus',
					'promocode_id',
					'promocode_amount',
					'promocode_discount',
					'userID',
					'barID',
					'createdAt'
				],
				include: [
					{
						attributes: [
							'id',
							'orderID',
							'productID',
							'price',
							'chargeAmount',
							'quantity',
							'specialRequest',
							'isCanceled',
							'refundAmount',
							'refundedQuantity',
							'waitTime',
							'orderStatus',
							'PreparingStartTime',
							'ReadyTime',
							'PickedupTime',
							[
								Sequelize.literal(
									`(ADDTIME(order_items.createdAt, waitTime)) `
								),
								'expectedTime'
							],
							[
								Sequelize.literal(
									`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `
								),
								'pickupLocation'
							],
							[
								Sequelize.literal(
									`(select subCategoryID from product where product.id = order_items.productID ) `
								),
								'subCategoryID'
							]
						],
						model: orderItems,
						include: [
							{
								attributes: ['id', 'name', 'description', 'avatar'],
								model: product,
								include: [
									{
										attributes: ['id', 'description', 'address'],
										model: pickupLocation
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productExtrasID', 'price'],
								model: orderItemExtras,
								include: [
									{
										attributes: ['id', 'extraItem', 'posID'],
										model: productExtras
									}
								]
							},
							{
								attributes: ['id', 'orderItemID', 'productVariantsID', 'price'],
								model: orderItemVariants,
								include: [
									{
										attributes: ['id', 'variantType'],
										model: productVariants
									}
								]
							},
							{
								attributes: [
									['id', 'orderProductVariantTypeID'],
									'orderItemID'
								],
								where: Sequelize.where(
									Sequelize.col('`order_items`.`id`'),
									Sequelize.col(
										'`order_items->order_product_variant_types`.`orderItemID`'
									)
								),
								model: orderProductVariantTypes,
								required: false,
								include: [
									{
										attributes: [['id', 'productVariantTypeID'], 'label'],
										model: productVariantTypes,
										required: true,
										include: [
											{
												attributes: [
													['id', 'orderProductVariantSubTypeID'],
													'orderItemID'
												],
												// where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
												model: orderProductVariantSubTypes,
												include: [
													{
														attributes: [
															['id', 'productVariantSubTypeID'],
															['variantType', 'extraItem'],
															'price'
														],
														model: productVariantSubTypes
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						required: true,
						model: user,
						attributes: ['id', 'fullName', 'mobile', 'email']
					},
					{
						model: coupons,
						attributes: ['id', 'code', 'name', 'description']
					},
					{
						model: orderTableNumber,
						attributes: ['tableCode']
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderTax
					},
					{
						attributes: ['id', 'name', 'percentage', 'taxID', 'amount'],
						model: orderRefundTax
					}
				]
			});
			for (let j = 0; j < orderResponse.order_items.length; j++) {
				const item = orderResponse.order_items[j];
				for (let k = 0; k < item.order_product_variant_types.length; k++) {
					const variant =
						orderResponse.order_items[j].order_product_variant_types[k];
					let getProductSubVariant = await orderProductVariantSubTypes.findOne({
						attributes: [
							['id', 'orderProductVariantSubTypeID'],
							'orderItemID',
							[
								Sequelize.literal(
									'(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
								),
								'extraItem'
							],
							[
								Sequelize.literal(
									'(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
								),
								'price'
							],
							[
								Sequelize.literal(
									'(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
								),
								'productVariantSubTypeID'
							]
						],
						where: {
							orderItemID: variant.orderItemID,
							productVariantTypeID:
								variant.product_variant_type.dataValues.productVariantTypeID
						}
					});

					orderResponse.order_items[j].order_product_variant_types[
						k
					].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID =
						getProductSubVariant.dataValues.productVariantSubTypeID;

					orderResponse.order_items[j].order_product_variant_types[
						k
					].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem =
						getProductSubVariant.dataValues.extraItem;

					orderResponse.order_items[j].order_product_variant_types[
						k
					].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price =
						getProductSubVariant.dataValues.price;
				}
			}
			let newOrderItems = groupByOrderItems(
				orderResponse.order_items,
				orderResponse.orderServiceType,
				orderResponse.refundStatus
			); // Group By order items
			orderResponse.dataValues['order_items_group'] = newOrderItems;
			return orderResponse;
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async orderCancel(req, res) {
		try {
			let orderDataResponse = await order.findOne({
				where: {
					id: req.body.id,
					barID: req.body.bar_id
				},
				include: [
					{
						attributes: [
							'id',
							'orderID',
							'name',
							'percentage',
							'taxID',
							'amount'
						],
						model: orderTax
					},
					{
						attributes: [
							'id',
							'orderID',
							'name',
							'percentage',
							'taxID',
							'amount'
						],
						model: orderRefundTax
					}
				]
			});

			if (orderDataResponse && Object.keys(orderDataResponse).length > 0) {
				var orderUpdateData = {};
				orderUpdateData['refundStatus'] = 'PartialRefunded';
				orderUpdateData['updatedAt'] = new Date();
				orderUpdateData['refundedDate'] = new Date();

				var orderItemsWithQty = req.body.order_items;
				// create order_items array and push ids
				let itemIds = [];
				for (const item of orderItemsWithQty) {
					itemIds.push(item.id);
				}

				let orderItemRes = await orderItems.findAll({
					attributes: [
						'id',
						'orderID',
						'productID',
						'price',
						'chargeAmount',
						'discountedAmount',
						'quantity',
						'refundedQuantity',
						'refundAmount',
						'newRefundAmount'
					],
					where: {
						orderID: req.body.id,
						id: {
							[Op.in]: itemIds
						}
					}
				});

				let totalRefundAmount = 0;
				let totalDiscountedAmount = 0;
				await Promise.all(
					orderItemRes.map(async (orderItem) => {
						//for every item calculate refund amount and refund quantity and update
						let refundedQuantity = 0;
						let refundedAmount = 0;
						let newRefundAmount = 0;
						let isCanceled = 'Yes';

						let rQty = 0;
						for (const item of orderItemsWithQty) {
							if (item.id === orderItem.id) {
								rQty = item.quantity;
							}
						}
						let itemRefundedAmount = orderItem.discountedAmount * rQty;
						let itemRefundedDiscountedAmount =
							orderItem.discountedAmount * rQty;
						refundedAmount =
							orderItem.refundAmount + orderItem.chargeAmount * rQty;
						newRefundAmount =
							orderItem.newRefundAmount + orderItem.discountedAmount * rQty;
						refundedQuantity = orderItem.refundedQuantity + rQty;
						totalRefundAmount = totalRefundAmount + itemRefundedAmount;
						//is canceled=yes if all quantity requested for cancel
						isCanceled = refundedQuantity === orderItem.quantity ? 'Yes' : 'No';
						totalDiscountedAmount =
							totalDiscountedAmount + itemRefundedDiscountedAmount;

						// update order item
						await orderItems.update(
							{
								isCanceled: isCanceled,
								refundAmount: refundedAmount,
								newRefundAmount: newRefundAmount,
								refundedQuantity: refundedQuantity,
								updatedAt: new Date()
							},
							{
								returning: true,
								where: {
									id: orderItem.id
								}
							}
						);

						// Delete wait time notification
						await OrderItemWaitTimeNotificationsModel.destroy({
							where: {
								orderItemID: orderItem.id
							}
						});
					})
				);

				const orderItemCount = await orderItems.findAll({
					attributes: ['id'],
					where: {
						orderID: req.body.id,
						isCanceled: 'No'
					}
				});

				if (orderItemCount.length == 0) {
					orderUpdateData['isCanceled'] = 'Yes';
					orderUpdateData['refundStatus'] = 'Refunded';
					orderUpdateData['orderStatus'] = 'Pickedup';
					// add full refund with transactionFee to customer -
					totalDiscountedAmount =
						totalDiscountedAmount + orderDataResponse.transactionFee;
				}

				let taxAmount = 0;

				let reqTaxData = req.body.tax_data ? req.body.tax_data : [];
				//order tax ids array
				let orderTaxIds =
					orderDataResponse.order_taxes &&
					orderDataResponse.order_taxes.map((a) => a.taxID);
				//order refund tax ids array
				let orderRefundTaxIds =
					orderDataResponse.order_refund_taxes &&
					orderDataResponse.order_refund_taxes.map((a) => a.taxID);

				const orderRemainingTax = orderTaxIds.filter(
					(element) => !orderRefundTaxIds.includes(element)
				);

				let oldTaxAmountData =
					orderDataResponse.order_taxes &&
					orderDataResponse.order_taxes.map((item) => item.amount);

				//order tax amount
				let orderOldTaxAmount = oldTaxAmountData.length
					? oldTaxAmountData.reduce((prev, next) => prev + next)
					: 0;

				let refundOldTaxAmountData =
					orderDataResponse.order_refund_taxes &&
					orderDataResponse.order_refund_taxes.map((item) => item.amount);
				//refund tax amount
				let totalOldRefundTaxAmount = refundOldTaxAmountData.length
					? refundOldTaxAmountData.reduce((prev, next) => prev + next)
					: 0;

				if (reqTaxData && reqTaxData.length) {
					let taxIDs = reqTaxData.map((a) => a.tax);
					//find all tax that provided in body
					const taxData = await taxModel.findAll({
						attributes: ['id', 'name', 'percentage', 'status'],
						where: { id: { [Op.in]: taxIDs } }
					});
					let orderTaxData = [];
					let refundTaxAmount = 0;
					taxData.forEach(async (tax) => {
						let calculateTax = 0;
						if (totalRefundAmount > 0) {
							calculateTax =
								(totalRefundAmount * tax.dataValues.percentage) / 100;
						} else {
							let orderTaxAmountTemp = orderDataResponse.order_taxes.filter(
								(item) => item.taxID == tax.id
							);
							let orderRefundTaxAmountTemp =
								orderDataResponse.order_refund_taxes.filter(
									(item) => item.taxID == tax.id
								);
							calculateTax = Number(
								(orderTaxAmountTemp.length > 0
									? orderTaxAmountTemp[0].amount
									: 0) -
									(orderRefundTaxAmountTemp.length > 0
										? orderRefundTaxAmountTemp[0].amount
										: 0)
							);
						}
						taxAmount += calculateTax;

						let reqTaxObj = reqTaxData.find((o) => o.tax === tax.dataValues.id);

						let getTaxRefundData =
							orderDataResponse.order_refund_taxes &&
							orderDataResponse.order_refund_taxes.find(
								(o) =>
									req.body.id == o.dataValues.orderID &&
									tax.dataValues.id == o.dataValues.taxID
							);
						if (getTaxRefundData) {
							refundTaxAmount +=
								Number(reqTaxObj.amount) +
								Number(getTaxRefundData.dataValues.amount);
							await orderRefundTax.update(
								{
									amount:
										Number(reqTaxObj.amount) +
										Number(getTaxRefundData.dataValues.amount)
								},
								{
									where: {
										id: getTaxRefundData.dataValues.id
									}
								}
							);
						} else {
							refundTaxAmount += reqTaxObj.amount;
							orderTaxData.push({
								barID: req.body.bar_id,
								orderID: req.body.id,
								name: tax.dataValues.name,
								percentage: tax.dataValues.percentage,
								taxID: tax.dataValues.id,
								amount: reqTaxObj.amount
							});
						}
					});

					if (orderTaxData.length) {
						orderRefundTax.bulkCreate(orderTaxData);
					}

					let notPresentInData = orderRemainingTax.filter(
						(val) => !taxIDs.includes(val)
					);

					if (
						notPresentInData.length ||
						totalOldRefundTaxAmount + refundTaxAmount < orderOldTaxAmount
					) {
						orderUpdateData['refundStatus'] = 'PartialRefunded';
						orderUpdateData['isCanceled'] = 'No';
					}

					orderUpdateData['tax'] = taxAmount;
				} else if (
					reqTaxData.length == 0 &&
					totalOldRefundTaxAmount < orderOldTaxAmount
				) {
					orderUpdateData['refundStatus'] = 'PartialRefunded';
					orderUpdateData['isCanceled'] = 'No';
				}
				let transactionRefundAmount = 0;
				if (
					req.body.refundTransactionFee == 'Yes' &&
					orderDataResponse.refundTransactionFee == 'No'
				) {
					transactionRefundAmount = orderDataResponse.transactionFee;
					orderUpdateData['refundTransactionFee'] = 'Yes';
				}
				await order.update(orderUpdateData, {
					returning: true,
					where: {
						id: req.body.id,
						barID: req.body.bar_id
					}
				});
				// strip and notification
				if (
					orderDataResponse.transactionID != '' &&
					(totalDiscountedAmount > 0 || taxAmount > 0)
				) {
					let refundAmount = Math.round(
						(totalDiscountedAmount + taxAmount + transactionRefundAmount) * 100
					);
					const barData = await bar.findOne({
						attributes: [
							'id',
							'restaurantName',
							'managerName',
							'email',
							'countryCode',
							'mobile',
							'stripeID',
							'venueId',
							'attachedPosConfig',
							'posStatus'
						],
						where: {
							id: orderDataResponse.barID
						}
					});

					await stripe.refunds.create(
						{
							charge: orderDataResponse.transactionID,
							amount: refundAmount,
							// reverse_transfer: true,
							refund_application_fee: false
						},
						{
							stripeAccount: barData.stripeID
						},
						async function (stripeErr, refundsData) {
							if (stripeErr == null) {
								if (refundsData.status == 'succeeded') {
									await transLogsModel.create({
										orderID: orderDataResponse.id,
										amout: refundAmount,
										transaction_type: 'cancel_order',
										refundTransactionID: refundsData.id,
										reversalsTransactionID: refundsData.transfer_reversal,
										log: JSON.stringify(refundsData),
										userID: orderDataResponse.userID,
										barID: orderDataResponse.barID,
										createdAt: new Date()
									});
								} else {
									await transErrorLogsModel.create({
										orderID: orderDataResponse.id,
										amout: refundAmount,
										transaction_type: 'cancel_order_err',
										log: JSON.stringify(refundsData),
										userID: orderDataResponse.userID,
										barID: orderDataResponse.barID,
										createdAt: new Date()
									});
								}
							} else {
								await transErrorLogsModel.create({
									orderID: orderDataResponse.id,
									amout: refundAmount,
									transaction_type: 'cancel_order_err',
									log: JSON.stringify(stripeErr),
									userID: orderDataResponse.userID,
									barID: orderDataResponse.barID,
									createdAt: new Date()
								});
							}
						}
					);

					// Table code
					let table_code = await orderTableNumber.findOne({
						attributes: ['tableCode'],
						where: { orderID: orderDataResponse.id },
						order: [['id', 'DESC']]
					});
					const tableCode = table_code && table_code.tableCode;

					var message =
						orderDataResponse.orderServiceType === 'PICKUP'
							? `Your order ${orderDataResponse.pickupCode} has been refunded.`
							: `Your order for table #${tableCode} has been refunded.`;

					var notification_type = 'orderRefund';

					if (message != '') {
						await UserNotificationModel.create({
							barID: orderDataResponse.barID,
							notification_type: notification_type,
							userID: orderDataResponse.userID,
							dataID: req.body.id,
							message: message,
							createdAt: new Date()
						});
						await commonFunction.orderStatusNotificationToUser(
							orderDataResponse.userID,
							req.body.id,
							notification_type,
							message,
							'Order Refund'
						);
					}
					return 1;
				} else {
					return 0;
				}
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async updateOrderItemWaitTime(req, res) {
		try {
			var barID = req.body.bar_id;
			let orderItemsIds = req.body.id.split(',');

			let orderItemsDataResponse = await orderItems.findOne({
				where: {
					id: {
						[Op.in]: orderItemsIds
					}
				},
				include: [
					{
						model: order,
						where: {
							barID: barID
						}
					}
				]
			});

			if (
				orderItemsDataResponse &&
				Object.keys(orderItemsDataResponse).length > 0
			) {
				await orderItems.update(
					{
						waitTime: req.body.waitTime
					},
					{
						where: {
							id: {
								[Op.in]: orderItemsIds
							}
						}
					}
				);
				await OrderItemWaitTimeNotificationsModel.update(
					{
						waitTime: req.body.waitTime
					},
					{
						where: {
							orderItemID: {
								[Op.in]: orderItemsIds
							},
							isDeleted: 'No'
						}
					}
				);
				return 1;
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async updateOrderItemStatus(req, res) {
		try {
			let barID = req.body.bar_id;
			let orderStatus = req.body.orderStatus;
			let orderItemsIds = req.body.id.split(',');
			let isItemStatusUpdated = false;

			await Promise.all(
				orderItemsIds.map(async (orderItemId) => {
					const orderItemDetails = await orderItems.findOne({
						where: {
							id: orderItemId
						},
						attributes: [
							'id',
							'orderID',
							[
								Sequelize.literal(
									`(select orderServiceType from orders where id = order_items.orderID) `
								),
								'orderServiceType'
							],
							[
								Sequelize.literal(
									`(select pickupCode from orders where id = order_items.orderID) `
								),
								'pickupCode'
							],
							[
								Sequelize.literal(
									`(select barID from orders where id = order_items.orderID) `
								),
								'barID'
							],
							[
								Sequelize.literal(
									`(select userID from orders where id = order_items.orderID) `
								),
								'userID'
							]
						],
						include: [
							{
								required: true,
								model: order,
								where: {
									barID: barID
								}
							}
						]
					});
					if (orderItemDetails) {
						const updateOrderData = {
							orderStatus: orderStatus,
							updatedAt: new Date()
						};
						switch (orderStatus) {
							case 'Preparing':
								updateOrderData['PreparingStartTime'] = new Date();
								break;
							case 'Pickup':
								updateOrderData['ReadyTime'] = new Date();
								break;
							case 'Pickedup':
								updateOrderData['PickedupTime'] = new Date();
								break;
							case 'NotPickedup':
								updateOrderData['isCanceled'] = 'Yes';
								break;
						}
						await orderItems.update(updateOrderData, {
							where: { id: orderItemId }
						});
						isItemStatusUpdated = true;
						if (
							orderStatus == 'Pickup' ||
							orderStatus == 'Pickedup' ||
							orderStatus == 'NotPickedup'
						) {
							await OrderItemWaitTimeNotificationsModel.destroy({
								where: {
									orderItemID: orderItemId
								}
							});
						}
						const TotalOrderItems = await orderItems.count({
							where: {
								orderID: orderItemDetails.orderID,
								isCanceled: 'No',
								isDeleted: 'No'
							}
						});
						const TotalPickedupItems = await orderItems.count({
							where: {
								orderID: orderItemDetails.orderID,
								isDeleted: 'No',
								isCanceled: 'No',
								orderStatus: 'Pickedup'
							}
						});
						const TotalReadyItems = await orderItems.count({
							where: {
								orderID: orderItemDetails.orderID,
								isDeleted: 'No',
								isCanceled: 'No',
								orderStatus: 'Pickup'
							}
						});
						if (TotalOrderItems == TotalPickedupItems) {
							await order.update(
								{
									orderStatus: 'Pickedup',
									PickedupTime: new Date(),
									updatedAt: new Date()
								},
								{
									where: {
										id: orderItemDetails.orderID
									}
								}
							);
						} else if (TotalOrderItems == TotalReadyItems) {
							await order.update(
								{
									orderStatus: 'Pickup',
									ReadyTime: new Date(),
									updatedAt: new Date()
								},
								{
									where: {
										id: orderItemDetails.orderID
									}
								}
							);
						}
					}
				})
			);
			if (isItemStatusUpdated) {
				const orderItemDetails = await orderItems.findOne({
					where: {
						id: orderItemsIds[0]
					},
					attributes: [
						'id',
						'orderID',
						[
							Sequelize.literal(
								`(select orderServiceType from orders where id = order_items.orderID) `
							),
							'orderServiceType'
						],
						[
							Sequelize.literal(
								`(select pickupCode from orders where id = order_items.orderID) `
							),
							'pickupCode'
						],
						[
							Sequelize.literal(
								`(select barID from orders where id = order_items.orderID) `
							),
							'barID'
						],
						[
							Sequelize.literal(
								`(select userID from orders where id = order_items.orderID) `
							),
							'userID'
						]
					],
					include: [
						{
							required: true,
							model: order,
							where: {
								barID: barID
							}
						}
					]
				});
				await pushNotificationForOrdersItems(orderItemDetails, orderStatus);
				return 1;
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async orderIntoxicated(req, res) {
		try {
			let barID = req.body.bar_id;
			let orderID = req.body.id;

			let OrderData = await order.findOne({
				where: {
					id: orderID,
					barID: barID
				}
			});
			if (OrderData) {
				order.update(
					{
						orderStatus: 'Intoxicated',
						intoxicatedDate: new Date(),
						updatedAt: new Date()
					},
					{
						where: {
							id: orderID,
							barID: barID
						}
					}
				);
				// Delete wait time notification
				await OrderItemWaitTimeNotificationsModel.destroy({
					where: {
						orderID: req.body.id
					}
				});

				var message =
					'Your account has been marked as too intoxicated by the venue. Please contact the venue.';
				var notification_type = 'orderIntoxicated';

				if (message != '') {
					await UserNotificationModel.create({
						barID: barID,
						notification_type: notification_type,
						userID: OrderData.userID,
						dataID: req.body.id,
						message: message,
						createdAt: new Date()
					});
					await commonFunction.orderStatusNotificationToUser(
						OrderData.userID,
						req.body.id,
						notification_type,
						message,
						'Order Intoxicated'
					);
				}
				return 1;
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async readyForPickupAlert(req, res) {
		try {
			var barID = req.body.bar_id;
			var orderID = req.body.id;
			let orderResponse = await order.findOne({
				where: {
					id: orderID,
					barID: barID
				}
			});
			if (orderResponse) {
				// Delete wait time notification
				await OrderItemWaitTimeNotificationsModel.destroy({
					where: {
						orderID: req.body.id
					}
				});

				let table_code = await orderTableNumber.findOne({
					attributes: ['tableCode'],
					where: { orderID: req.body.id },
					order: [['id', 'DESC']]
				});
				const tableCode = table_code && table_code.tableCode;

				var message =
					orderResponse.dataValues.orderServiceType === 'PICKUP'
						? `Your order ${orderResponse.dataValues.pickupCode} is ready to collect.`
						: `Your order for table #${tableCode} is ready.`;

				var notification_type = 'orderReady_pickupAlert';

				if (message != '') {
					await UserNotificationModel.create({
						barID: barID,
						notification_type: notification_type,
						userID: orderResponse.userID,
						dataID: orderID,
						message: message,
						createdAt: new Date()
					});
					await commonFunction.orderStatusNotificationToUser(
						orderResponse.userID,
						orderID,
						notification_type,
						message
					);
				}
				return 1;
			} else {
				return 0;
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	},

	async updateOrderItemNewRefundAmount(req, res) {
		try {
			let orderDataResponse = await order.findAll({
				where: {
					promocode_discount: {
						[Op.gt]: 0
					}
				},
				include: [
					{
						model: orderItems,
						where: {
							refundedQuantity: {
								[Op.ne]: 0
							}
						}
					}
				]
			});
			if (orderDataResponse && Object.keys(orderDataResponse).length > 0) {
				orderDataResponse.map(async (orderData) => {
					let promocodeDiscount = orderData.promocode_discount;
					orderData.order_items.map(async (orderItem) => {
						let refundedQuantity = orderItem.refundedQuantity;
						let price = orderItem.chargeAmount;

						let discount_amount = (promocodeDiscount / 100) * price;
						let newRefundAmount = (price - discount_amount) * refundedQuantity;
						// update order item
						await orderItems.update(
							{
								newRefundAmount: newRefundAmount
							},
							{
								returning: true,
								where: {
									id: orderItem.id
								}
							}
						);
					});
				});
			}
		} catch (err) {
			console.log('err', err);
			throw err;
		}
	}
};

function groupByOrderItems(
	orderItems,
	orderServiceType = 'PICKUP',
	refundStatus = 'no'
) {
	let order_items = JSON.parse(JSON.stringify(orderItems));
	// Group By Sub Category ID
	// let groupByArr = groupBy(order_items, 'subCategoryID');
	// let newOrderItemsArr = [];
	// Object.entries(groupByArr).forEach(([key, value]) => {
	let newValueItemsArr = [];
	if (orderServiceType === 'PICKUP') {
		// Group By Pickup Location
		let groupByPickupLocationArr = groupBy(order_items, 'pickupLocation');
		Object.entries(groupByPickupLocationArr).forEach(([key, value]) => {
			// Group By Wait Time
			let groupByWaitTimeArr = groupBy(value, 'waitTime');
			let newValueItemsArr1 = [];
			Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
				newValueItemsArr1.push({
					wait_time: value[0] && value[0].expectedTime,
					orderStatus: value[0].orderStatus,
					refundStatus: refundStatus,
					items: value
				});
			});

			let pickup_location = value[0] && value[0].pickupLocation;
			Object.entries(newValueItemsArr1).forEach(([key, value]) => {
				newValueItemsArr.push({ pickup_location: pickup_location, ...value });
			});
		});
	} else {
		// Group By Wait Time
		let groupByWaitTimeArr = groupBy(order_items, 'waitTime');
		Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
			newValueItemsArr.push({
				wait_time: value[0] && value[0].expectedTime,
				orderStatus: value[0].orderStatus,
				refundStatus: refundStatus,
				items: value
			});
		});
	}
	newValueItemsArr.sort(function (a, b) {
		return new Date(a.wait_time) - new Date(b.wait_time);
	});
	// let subCategoryID = value[0]?.subCategoryID;
	// Object.entries(newValueItemsArr).forEach(([key, value]) => {
	//   newOrderItemsArr.push({ subCategoryID: subCategoryID, ...value });
	// });
	// });

	return newValueItemsArr;
}

function groupBy(xs, key) {
	return xs.reduce(function (rv, x) {
		(rv[x[key]] = rv[x[key]] || []).push(x);
		return rv;
	}, {});
}

async function pushNotificationForOrdersItems(orderDetails, status) {
	let orderID = orderDetails.dataValues && orderDetails.dataValues.orderID;
	let orderServiceType =
		orderDetails.dataValues && orderDetails.dataValues.orderServiceType;
	let pickupCode =
		orderDetails.dataValues && orderDetails.dataValues.pickupCode;
	let userID = orderDetails.dataValues && orderDetails.dataValues.userID;
	let barID = orderDetails.dataValues && orderDetails.dataValues.barID;

	let notification_type, posPayload, message;
	// Table code seperated for new change
	let table_code = await orderTableNumber.findOne({
		attributes: ['tableCode'],
		where: { orderID: orderID },
		order: [['id', 'DESC']]
	});
	const tableCode = table_code && table_code.tableCode;

	switch (status) {
		case 'Preparing':
			message =
				orderServiceType === 'PICKUP'
					? `Your order ${pickupCode} is confirmed and being prepared.`
					: `Order for table #${tableCode} is confirmed and being prepared.`;
			notification_type = 'orderPreparing';
			posPayload = {};
			break;

		case 'Pickup':
			message =
				orderServiceType === 'PICKUP'
					? `Your order ${pickupCode} is ready to collect.`
					: `Your order for table #${tableCode} is ready.`;
			notification_type = 'orderReady';
			posPayload = {};
			break;

		case 'Pickedup':
			message =
				orderServiceType === 'PICKUP'
					? `Thank you for collecting your order ${pickupCode}. Enjoy!`
					: `Your order has been delivered to your table #${tableCode}. Enjoy!`;
			notification_type = 'orderPickedup';
			posPayload = {};
			break;

		case 'NotPickedup':
			message =
				orderServiceType === 'PICKUP'
					? `Your order ${pickupCode} has been marked as not collected. Please contact the venue.`
					: `Your order for table #${tableCode} has been marked as not collected. Please contact the venue.`;
			notification_type = 'orderNotPickedup';
			break;
	}

	if (message !== '') {
		await UserNotificationModel.create({
			barID: barID,
			notification_type: notification_type,
			userID: userID,
			dataID: orderID,
			message: message,
			createdAt: new Date(),
			updatedAt: new Date()
		});
		await commonFunction.orderStatusNotificationToUser(
			userID,
			orderID,
			notification_type,
			message
		);
	}
}
