const Joi = require('joi');

module.exports = {
	async addOrUpdateUpsellSubCategoriesValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			}),
			parent_sub_category_id: Joi.number().required().messages({
				'any.required': 'parent_sub_category_id is required.',
				'number.empty': 'parent_sub_category_id is required.'
			}),
			child_sub_category_id: Joi.number().required().messages({
				'any.required': 'child_sub_category_id is required.',
				'number.empty': 'child_sub_category_id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async deleteUpsellSubCategoriesValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			}),
			parent_sub_category_id: Joi.number().required().messages({
				'any.required': 'parent_sub_category_id is required.',
				'number.empty': 'parent_sub_category_id is required.'
			})
		});
		return schema.validate(req.body);
	},
	async getUpsellSubCategoriesValidation(req) {
		const schema = Joi.object({
			bar_id: Joi.number().required().messages({
				'any.required': 'bar_id is required.',
				'number.empty': 'bar_id is required.'
			})
		});
		return schema.validate(req.body);
	}
};
