const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const pickupLocationService = require('../../services/venue/pickupLocationService');
const pickupLocationValidation = require('../../validations/venue/pickupLocationValidation');

module.exports = {
	/* add pickuplocation */
	async add(req, res) {
		try {
			// validation
			const valid = await pickupLocationValidation.add(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.add(req, res);
			if (data == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CATEGORYASSIGNALREADY,
					status.ERROR
				);
			} else if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.LOCATIONADDED,
					status.SUCCESS
				);
			} else {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*list pickuplocation */
	async list(req, res) {
		try {
			// validation
			const valid = await pickupLocationValidation.list(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.list(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					data,
					message.LOCATIONSFETCHSUCCESSFULLY,
					status.SUCCESS
				);
			} else {
				//response on old password mis-match
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*edit pickuplocation */
	async edit(req, res) {
		try {
			// validation
			const valid = await pickupLocationValidation.edit(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.edit(req, res);

			if (data == 2) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.CATEGORYASSIGNALREADY,
					status.ERROR
				);
			} else if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.LOCATIONUPDATED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	},
	/*delete pickuplocation */
	async delete(req, res) {
		try {
			// validation
			const valid = await pickupLocationValidation.delete(req);
			if (valid.error) {
				return response(
					res,
					status.BADREQUESTCODE,
					{},
					valid.error.details[0].message,
					status.ERROR
				);
			}
			let data = await pickupLocationService.delete(req, res);

			if (data) {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.LOCATIONDELETED,
					status.SUCCESS
				);
			} else {
				return response(
					res,
					status.SUCCESSSTATUS,
					{},
					message.SOMETHINGWENTWRONG,
					status.ERROR
				);
			}
		} catch (error) {
			//response on internal server error
			return response(
				res,
				status.INTERNALSERVERERRORSTATUS,
				[],
				message.INTERNALSERVERERROR,
				status.ERROR
			);
		}
	}
};
