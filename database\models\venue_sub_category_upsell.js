'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class venue_sub_category_upsell extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			models.sub_category.hasOne(venue_sub_category_upsell, {
				foreignKey: 'parentSubCategoryID',
				as: 'childSubCategoryLink'
			});
			venue_sub_category_upsell.belongsTo(models.sub_category, {
				foreignKey: 'childSubCategoryID',
				as: 'subCategory'
			});
		}
	}
	venue_sub_category_upsell.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			barID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'bar',
					key: 'id'
				}
			},
			parentSubCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			childSubCategoryID: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE }
		},

		{
			sequelize,
			modelName: 'venue_sub_category_upsell',
			timestamps: true,
			freezeTableName: true
		}
	);
	return venue_sub_category_upsell;
};
