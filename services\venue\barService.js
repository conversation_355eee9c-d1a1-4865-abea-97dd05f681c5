/*Messages,status code and services require*/
require('dotenv').config();
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const moment = require('moment');
const speakeasy = require('speakeasy');
const sharp = require('sharp');
const VenueUserModel = require('../../database/models').venue_user;
const BarModel = require('../../database/models').bar; //import modal always Capital
const BarOpeningHoursModel = require('../../database/models').bar_opening_hours;
const BarOpeningHoursUTCModel =
	require('../../database/models').bar_opening_hours_utc;
const BarSubCategoryOpeningHoursModel =
	require('../../database/models').bar_sub_category_opening_hours;
const BarSubCategoryOpeningHoursUTCModel =
	require('../../database/models').bar_sub_category_opening_hours_utc;
const BarSubCategoryWaitTimeModel =
	require('../../database/models').bar_sub_category_wait_time;
const BarSubCategoryWaitTimeUTCModel =
	require('../../database/models').bar_sub_category_wait_time_utc;
const EmailVerificationModel =
	require('../../database/models').email_verification; //import modal always Capital
const OperatingHoursModel = require('../../database/models').operating_hour;
const PickupLocationModel = require('../../database/models').pickup_location;
const SettingModel = require('../../database/models').settings;
const imageUpload = require('../../middleware/multerAwsUpload').s3PublicUpload;
const imageUploadBuffer =
	require('../../middleware/multerAwsUpload').s3PublicUploadBuffer;
const imageDelete = require('../../middleware/multerAwsDelete').s3PublicDelete;
const VenueUserBarModel = require('../../database/models').venue_user_bar;
const ProductModel = require('../../database/models').product;
const OrderModel = require('../../database/models').orders;
const user = require('../../database/models').user;
const ProductVariantTypes =
	require('../../database/models').product_variant_types;
const SubCategoryWaitTimeModel =
	require('../../database/models').sub_category_wait_time;
const commonFunction = require('../../common/commonFunction');
const SubCategoryModel = require('../../database/models').sub_category;
const PickupLocationSubCategoryModel =
	require('../../database/models').pickup_location_sub_category;
const helper = require('../../helper/generalHelper');
const VenueUserTokenModel =
	require('../../database/models').venue_user_accesstoken;
const timezoneModel = require('../../database/models').timezone;
const mail = require('../../helper/sendmail');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const { Parser } = require('json2csv');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const {
	convertOpeningHours,
	timeChunkArray
} = require('../../common/commonFunction');

module.exports = {
	/* add venue */
	async addVenueOperatingHours(req, res) {
		try {
			const barID = req.body.bar_id;
			const requestOperatingHours = req.body.operating_hours;

			// await OperatingHoursModel.destroy({
			// 	where: {
			// 		barID: barID
			// 	}
			// });
			requestOperatingHours.map(async (opHours) => {
				await OperatingHoursModel.create({
					openingHours: opHours.opening_hours,
					closingHours: opHours.closing_hours,
					weekDay: opHours.week_day,
					isClosed: opHours.is_closed,
					barID: barID
				});
			});

			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getTimezones() {
		try {
			return await timezoneModel.findAll({
				attributes: ["id", "name","title"],
				where: {
					status: 'Active'
				},
                order: [['sequence', 'ASC']]
			});
		} catch (error) {
			throw error;
		}
	},
	async getVenueOperatingHours(req, res) {
		try {
			const barID = req.body.bar_id;

			let barFetchResponse = await OperatingHoursModel.findAll({
				attributes: [
					[
						Sequelize.fn('date_format', Sequelize.col('openingHours'), '%k:%i'),
						'openingHours'
					],
					[
						Sequelize.fn('date_format', Sequelize.col('closingHours'), '%k:%i'),
						'closingHours'
					],
					'weekDay',
					[Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
				],
				where: { barID: barID },
				order: [['weekDay']]
			});

			if (barFetchResponse) {
				return barFetchResponse;
			}
			return 0;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async connectVenue(req, res) {
		try {
			let email = req.body.email;
			let barDetails = await BarModel.findOne({
				where: {
					email: email
				}
			});
			//venue is not avaliable with email
			if (!barDetails) {
				return 2;
			}

			// let connectedVenueCount = await VenueUserBarModel.count({
			// 	where: {
			// 		user_id: req.user_id
			// 	}
			// });
			//connected venue count
			// if (connectedVenueCount > 0) {
			// 	return 4;
			// }

			let venueUserRelation = await VenueUserBarModel.findOne({
				where: {
					user_id: req.user_id,
					bar_id: barDetails.id
				}
			});

			if (venueUserRelation) {
				//already venue connected
				return 0;
			}
			if (
				!Bcryptjs.compareSync(req.body.password, barDetails.dataValues.password)
			) {
				return 1; //password is incorrect
			}

			let otp = helper.generateOtp();

			//store otp in bar model
			await BarModel.update(
				{
					connectVenueOtp: otp
				},
				{
					where: {
						id: barDetails.id
					}
				}
			);

			let subject = 'Connect Your Venue To Your MyTab Management Portal';
			let mailbody =
				'<div><p>Hello ' +
				barDetails.managerName +
				',' +
				'</p><p>In order to connect your venue to your MyTab Management Portal, please enter the 6-digit verification code below:</p>' +
				'<p>One-Time Code: ' +
				'<b>' +
				otp +
				'</b>' +
				' </p> ' +
				'<p>If you did not request this connection, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>' +
				'<p>Thank you, <br /> MyTab Venue Support <br />' +
				'<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p></div>';
			mail.sendmail(res, req.body.email, subject, mailbody);

			return 3;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async venueOtpVerify(req, res) {
		try {
			let email = req.body.email;
			let barDetails = await BarModel.findOne({
				where: {
					email: email
				}
			});

			//venue is not avaliable with email
			if (!barDetails) {
				return 0;
			}
			//already relation created
			let venueUserRelation = await VenueUserBarModel.findOne({
				where: {
					user_id: req.user_id,
					bar_id: barDetails.id
				}
			});

			if (venueUserRelation) {
				//already venue connected
				return 1;
			}

			//OTP MISMATCH
			if (Number(barDetails.connectVenueOtp) != Number(req.body.otp)) {
				return 2;
			}

			//create connction between bar venue
			await VenueUserBarModel.create({
				user_id: req.user_id,
				bar_id: barDetails.dataValues.id
			});

			return 3;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async sendVenueOtp(req, res) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			//venue is not avaliable with email
			if (!barDetails) {
				return 2;
			}

			let otp = helper.generateOtp();

			//store otp in bar model
			await BarModel.update(
				{
					connectVenueOtp: otp
				},
				{
					where: {
						id: barDetails.id
					}
				}
			);

			let subject = 'Stripe Verification For Your MyTab Venue';
			let mailbody =
				'<div><p>Hello ' +
				barDetails.managerName +
				',' +
				'</p><p>In order to connect your Stripe Account to your MyTab Venue, Please enter the 6-digital verification code below:</p>' +
				'<p><b> ' +
				otp +
				' </b></p>' +
				'<p>If you did not request this connection, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>' +
				'<p>Thank you, <br /> MyTab Venue Support <br />' +
				'<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p></div>';
			mail.sendmail(res, barDetails.email, subject, mailbody);

			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async venueOtpVerifyOnly(req, res) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});

			//venue is not avaliable with email
			if (!barDetails) {
				return 0;
			}

			//OTP MISMATCH
			if (Number(barDetails.connectVenueOtp) != Number(req.body.otp)) {
				return 2;
			}

			return 3;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getVenueDetails(req, res) {
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id
				}
				// include: [
				// 	{
				// 		model: OperatingHoursModel,
				// 		attributes: [
				// 			'id',
				// 			'weekDay',
				// 			'openingHours',
				// 			'closingHours',
				// 			'isClosed'
				// 		]
				// 	}
				// ],
				// order: [[Sequelize.literal('weekDay'), 'ASC']]
			});
			if (barDetails) {
				return barDetails;
			}
			return 0;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},

	async updateVenueDetails(req, res) {

		try {
			if (req.body.business_register_id) {
				if (
					!commonFunction.validateABN(req.body.business_register_id) &&
					!commonFunction.validateACN(req.body.business_register_id)
				) {
					return 0;
				}
			}

			let email = req.body.email;
			let barEmailExists = await BarModel.findOne({
				where: {
					email: email,
					id: {
						[Op.ne]: req.body.bar_id
					}
				}
			});		

			//email already exists
			if (barEmailExists) {
				return 2;
			}

			let timezone = req.body.timezone;
			
			let barTimezoneExists = await BarModel.findOne({
				where: {
					id: {
						[Op.eq]: req.body.bar_id
					}
				}
			});

			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id
				}
			});
			if (!barDetails) {
				return 0;
			}

			let serviceType = req?.body?.service_type;
			let confirmChangeFlag = req?.body?.confirm_change_flag;

			if (serviceType != barDetails.serviceType) {
				let isVenueUpdatable = await commonFunction.checkBarIsOpenV2(
					req.body.bar_id
				);

				if (isVenueUpdatable) {
					return 3;
				}
				if (!parseInt(confirmChangeFlag)) {
					const switchedCount = await OrderModel.count({
						where: {
							orderServiceType:
								serviceType?.toLowerCase() === 'pickup' ? 'TABLE' : 'PICKUP',
							barID: barDetails?.id
						}
					});
					if (serviceType.toLowerCase() !== 'both' && switchedCount > 0) {
						return {
							confirmChangeFlag: true,
							switchedCount,
							serviceType: barDetails?.serviceType,
							requestServiceType: serviceType,
							popUpFlag: 1
						};
					}
				}
			}

			/*upload bar image*/
			if (req.file) {
				/* delete bar image */
				if (barDetails.avatar) {
					await imageDelete(barDetails.avatar.split('.com/')[1]);
				}
				const compressImageBuffer = await sharp(req.file.path)
					.webp({ quality: 30 })
					.toBuffer();

				await imageUploadBuffer(
					compressImageBuffer,
					req.file.mimetype,
					constant.AWSBARFOLDER + req.file.originalname
				);
			}

			/* update bar model */
			// serviceType: req.body.service_type ? req.body.service_type : 'PICKUP',
			await BarModel.update(
				{
					restaurantName: req.body.venue_name,
					managerName: req.body.manager_name,
					email: req.body.email,
					address: req.body.venue_address,
					latitude: req.body.latitude,
					longitude: req.body.longitude,
					mobile: req.body.mobile,
					avatar: req.file?.originalname,
					isVenueServeAlcohol: req.body.is_venue_serve_alcohol,
					liquorLicenseNumber: req.body.liquor_license_number,
					businessRegisterId: req.body.business_register_id,
					countryCode: req.body.country_code,
					serviceType: serviceType,
					timezone: timezone
				},
				{
					where: {
						id: req.body.bar_id
					}
				}
			);
			
			if (barTimezoneExists && timezone !== barTimezoneExists.timezone) {
				await BarOpeningHoursModel.update(
					{ timeZone: timezone }, 
					{ where: { barID: req.body.bar_id } }
				);

				/* Updated timezone in bar_opening_hours & bar_opening_hours_utc tables start */
				const barFetchResponse = await BarOpeningHoursModel.findAll({
					attributes: [
						'id',
						'barID',
						'weekDay',
						[
							Sequelize.fn('date_format', Sequelize.col('openingHours'), '%H:%i'),
							'openingHours'
						],
						[
							Sequelize.fn('date_format', Sequelize.col('closingHours'), '%H:%i'),
							'closingHours'
						],
						[Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
					],
					where: { barID: req.body.bar_id },
					order: [['weekDay'], ['openingHours']]
				});

				const barUTCFetchResponse = barFetchResponse.flatMap((r) =>				
					convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
						...e,
						barID: r.barID,
						isClosed: r.isClosed,
						barOpeningHoursID: r.id
					}))
				);

				if (barUTCFetchResponse.length){
					await BarOpeningHoursUTCModel.destroy({
						where: { barID: req.body.bar_id }
					}),
					await BarOpeningHoursUTCModel.bulkCreate(barUTCFetchResponse);
				}
				/* Updated timezone in bar_opening_hours & bar_opening_hours_utc tables end */

				/* Updated timezone in bar_sub_category_opening_hours & bar_sub_category_opening_hours_utc tables start */
				await BarSubCategoryOpeningHoursModel.update(
					{ timeZone: timezone }, 
					{ where: { barID: req.body.bar_id } }
				);

				const subCategoryFetchResponse = await BarSubCategoryOpeningHoursModel.findAll({
					attributes: [
						'id',
						'barID',
						'weekDay',
						'subCategoryID',
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('openingHours'),
								'%H:%i'
							),
							'openingHours'
						],
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('closingHours'),
								'%H:%i'
							),
							'closingHours'
						],
						[Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
					],
					where: { barID: req.body.bar_id },
					order: [['weekDay'], ['openingHours']]
				});
			
				const subCategoryUTCFetchResponse = subCategoryFetchResponse.flatMap((r) => 
					convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
						...e,
						barID: r.barID,
						isClosed: r.isClosed,
						subCategoryID: r.subCategoryID,
						barSubCategoryOpeningHoursID: r.id
					}))
				);
				
				if (subCategoryUTCFetchResponse.length){
					await BarSubCategoryOpeningHoursUTCModel.destroy({
						where: { barID: req.body.bar_id }
					}),
					await BarSubCategoryOpeningHoursUTCModel.bulkCreate(subCategoryUTCFetchResponse);
				}
				/* Updated timezone in bar_sub_category_opening_hours & bar_sub_category_opening_hours_utc tables end */

				/* Updated timezone in bar_sub_category_wait_time & bar_sub_category_wait_time_utc tables start */
				let subCategoryWaitTimeDetails = await BarSubCategoryWaitTimeModel.findAll({
					where: {
						barID: req.body.bar_id
					},
					attributes: [
						'id',
						'barID',
						'weekDay',
						'subCategoryID',
						'barSubCategoryOpeningHoursID',
						'waitTimeType',
						'waitTime',
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('startTime'),
								'%H:%i'
							),
							'openingHours'
						],
						[
							Sequelize.fn(
								'date_format',
								Sequelize.col('endTime'),
								'%H:%i'
							),
							'closingHours'
						],
						[Sequelize.literal('waitTimeType'), 'type']
					],
					order: ['weekDay'],
					raw: true
				});

				const subCategoryUTCWaitTimeDetails = subCategoryWaitTimeDetails.flatMap((r) => 
					convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
						...e,
						startTime: e.openingHours,
						endTime: e.closingHours,
						waitTime: r.waitTime,
						barID: r.barID,
						subCategoryID: r.subCategoryID,
						barSubCategoryOpeningHoursID: r.barSubCategoryOpeningHoursID,
						barSubCategoryWaitTimeID: r.id,
					}))
				);				
				
				if (subCategoryUTCWaitTimeDetails.length){
					await BarSubCategoryWaitTimeUTCModel.destroy({
						where: { barID: req.body.bar_id }
					}),
					await BarSubCategoryWaitTimeUTCModel.bulkCreate(subCategoryUTCWaitTimeDetails);
				}
				/* Updated timezone in bar_sub_category_wait_time & bar_sub_category_wait_time_utc tables start */
			}

			/* update oprating hours */
			if (req.body.operating_hours) {
				// req.body.operating_hours.map(async (opHours) => {
				// 	await OperatingHoursModel.update(
				// 		{
				// 			openingHours: opHours.opening_hours,
				// 			closingHours: opHours.closing_hours,
				// 			isClosed: opHours.is_closed,
				// 			barID: req.body.bar_id
				// 		},
				// 		{
				// 			where: {
				// 				id: opHours.id
				// 			}
				// 		}
				// 	);
				// });
			}

			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async updateVenueMobile(req, res) {
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id,
					countryCode: req.body.old_country_code,
					mobile: req.body.old_mobile
				}
			});
			if (barDetails) {
				/* update bar mobile */
				await BarModel.update(
					{
						mobile: req.body.mobile,
						countryCode: req.body.country_code
					},
					{
						where: {
							id: req.body.bar_id
						}
					}
				);
				return 1;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async createVenue(req, res) {
		try {
			// let connectedVenueCount = await VenueUserBarModel.count({
			// 	where: {
			// 		user_id: req.user_id
			// 	}
			// });
			// //connected venue count
			// if (connectedVenueCount > 0) {
			// 	return 4;
			// }

			if (
				!commonFunction.validateABN(req.body.business_register_id) &&
				!commonFunction.validateACN(req.body.business_register_id)
			) {
				return 0;
			}

			let findVenueUser = await BarModel.findOne({
				where: {
					email: req.body.email.toLowerCase().trim(),
					isDeleted: 'No'
				}
			});
			if (findVenueUser) {
				return 2; //email alerady connected with bar
			}

			//upload bar image
			if (req.file) {
				const compressImageBuffer = await sharp(req.file.path)
					.webp({ quality: 30 })
					.toBuffer();

				await imageUploadBuffer(
					compressImageBuffer,
					req.file.mimetype,
					constant.AWSBARFOLDER + req.file.originalname
				);
			}

			let barData = await BarModel.create({
				restaurantName: req.body.venue_name,
				managerName: req.body.manager_name,
				address: req.body.venue_address,
				latitude: req.body.latitude,
				longitude: req.body.longitude,
				email: req.body.email.toLowerCase().trim(),
				mobile: req.body.mobile,
				password: req.body.password,
				avatar: req.file?.originalname ? req.file.originalname : null,
				serviceType: req.body.service_type ? req.body.service_type : 'PICKUP',
				isVenueServeAlcohol: req.body.is_venue_serve_alcohol,
				liquorLicenseNumber: req.body.liquor_license_number,
				businessRegisterId: req.body.business_register_id,
				countryCode: req.body.country_code,
				timezone: req.body.timezone,
				readPopup: 'Yes'
			});

			const pickupLocation = await PickupLocationModel.create({
				barID: barData.id,
				address: 'Collect at counter',
				isDefault: '1'
			});

			const whereCondition = {
				...(barData.posStatus === 1
					? { categoryID: -1 }
					: { categoryID: { [Op.ne]: -1 } })
			};

			const allSubCategories = await SubCategoryModel.findAll({
				where: whereCondition,
				attributes: ['id'],
				raw: true
			});

			const subCategoryIds = allSubCategories.map((subCat) => subCat.id);

			const newLinks = subCategoryIds.map((subCatId) => ({
				barID: barData.id,
				subCategoryID: subCatId,
				pickupLocationID: pickupLocation.id
			}));

			await PickupLocationSubCategoryModel.bulkCreate(newLinks);

			//create relation between venue and venue user
			await VenueUserBarModel.create({
				user_id: req.user_id,
				bar_id: barData.id
			});

			await SettingModel.create({
				paramKey: 'Transaction fee',
				barID: barData.id,
				paramFix: 0.29,
				paramVar: 2.9
			});

			// req.body.operating_hours.map(async (opHours) => {
			// 	await OperatingHoursModel.create({
			// 		openingHours: opHours.opening_hours,
			// 		closingHours: opHours.closing_hours,
			// 		weekDay: opHours.week_day,
			// 		isClosed: opHours.is_closed,
			// 		barID: barData.id
			// 	});
			// });
			const generateWaitTimeEntries = (records, barID) => {
				return records.flatMap((record) =>
					commonFunction
						.timeChunkArray(record.openingHours, record.closingHours, '60')
						.map(({ startTime, endTime }) => ({
							barID,
							subCategoryID: record.subCategoryID,
							barSubCategoryOpeningHoursID:
								record.barSubCategoryOpeningHoursID || record.id,
							weekDay: record.weekDay,
							waitTime: '00:10:00',
							startTime,
							endTime
						}))
				);
			};

			const processOpeningHours = async (
				requestOperatingHours,
				barID,
				timezone,
				subCategoryIds
			) => {
				const barHoursEntries = [];
				const utcEntriesAll = [];
				const subCategoryEntriesAll = [];

				for (const opHours of requestOperatingHours) {
					const localWeekDay = parseInt(opHours.week_day, 10);
					const isClosed = opHours.is_closed == 'true' ? '1' : '0';

					const barHourEntry = {
						openingHours: opHours.opening_hours,
						closingHours: opHours.closing_hours,
						weekDay: localWeekDay,
						isClosed,
						barID
					};
					barHoursEntries.push(barHourEntry);

					const utcEntries = commonFunction
						.convertOpeningHours(barHourEntry, timezone, localWeekDay)
						.map((e) => ({
							...e,
							barID,
							isClosed
						}));
					utcEntriesAll.push(...utcEntries);

					const subEntries = subCategoryIds.map((subCategoryId) => ({
						openingHours: opHours.opening_hours,
						closingHours: opHours.closing_hours,
						weekDay: localWeekDay,
						subCategoryID: subCategoryId,
						isClosed,
						barID
					}));
					subCategoryEntriesAll.push(...subEntries);
				}

				const newBarHours = await BarOpeningHoursModel.bulkCreate(
					barHoursEntries,
					{
						returning: true
					}
				);

				for (let i = 0; i < newBarHours.length; i++) {
					const relatedUtcEntries = utcEntriesAll.filter(
						(e) => e.weekDay === newBarHours[i].weekDay
					);
					relatedUtcEntries.forEach((e) => {
						e.barOpeningHoursID = newBarHours[i].id;
					});
				}

				await BarOpeningHoursUTCModel.bulkCreate(utcEntriesAll);

				const newSubHours = await BarSubCategoryOpeningHoursModel.bulkCreate(
					subCategoryEntriesAll,
					{ returning: true }
				);

				const utcSubEntries = newSubHours.flatMap((r) =>
					commonFunction
						.convertOpeningHours(r, timezone, r.weekDay)
						.map((e) => ({
							...e,
							barID,
							isClosed: r.isClosed,
							subCategoryID: r.subCategoryID,
							barSubCategoryOpeningHoursID: r.id
						}))
				);
				await BarSubCategoryOpeningHoursUTCModel.bulkCreate(utcSubEntries);

				const waitTimeEntries = generateWaitTimeEntries(newSubHours, barID);

				const newWaitTimeEntries = await BarSubCategoryWaitTimeModel.bulkCreate(
					waitTimeEntries
				);

				const utcWaitTimeEntries = newWaitTimeEntries.flatMap((r) =>
					commonFunction
						.convertOpeningHours(
							{ openingHours: r.startTime, closingHours: r.endTime },
							timezone,
							r.weekDay
						)
						.map((e) => ({
							...e,
							barSubCategoryWaitTimeID: r.id,
							startTime: e.openingHours,
							endTime: e.closingHours,
							waitTime: r.waitTime,
							isClosed: r.isClosed,
							subCategoryID: r.subCategoryID,
							barSubCategoryOpeningHoursID: r.barSubCategoryOpeningHoursID,
							barID
						}))
				);

				await BarSubCategoryWaitTimeUTCModel.bulkCreate(utcWaitTimeEntries);

				return newBarHours;
			};

			await processOpeningHours(
				req.body.operating_hours,
				barData.id,
				barData.timezone,
				subCategoryIds
			);

			return barData;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async contactUs(req, res) {
		let name = req.body.name;
		let email = req.body.email;
		let message = req.body.message;
		var barID = req.body.bar_id ? req.body.bar_id : '';
		var mobile = req.body.mobile ? req.body.mobile : '';
		var app_version = 'Web';
		try {
			let subject = 'MyTab Venue Support';
			var mailbody =
				'<p><strong>Hi,</strong></p><p>Name : ' +
				name +
				'</p><p>Email : <a href="mailto:' +
				email +
				'">' +
				email +
				'</a></p><p>Message : ' +
				message +
				'</p><p>Bar ID : ' +
				barID +
				'</p><p>Mobile : ' +
				mobile +
				'</p><p>App Version : ' +
				app_version +
				'</p>';

			let data = await mail.sendmail(
				res,
				constant.BAREMAILTO,
				subject,
				mailbody,
				'',
				email
			);
			if (data) {
				return 1;
			} else {
				return 0;
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async delete(req, res) {
		try {
			await VenueUserBarModel.destroy({
				where: {
					bar_id: req.body.bar_id,
					user_id: req.user_id
				}
			});

			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async deleteVenue(req, res) {
		try {
			await BarModel.update(
				{
					isDeleted: 'Yes',
					updatedAt: new Date()
				},
				{
					where: {
						id: req.body.bar_id
					}
				}
			);
			await VenueUserBarModel.destroy({
				where: {
					bar_id: req.body.bar_id,
					user_id: req.user_id
				}
			});
			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getServiceType(req, res) {
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id,
					isDeleted: 'No'
				}
			});
			if (!barDetails) {
				return 0;
			} else {
				return barDetails?.serviceType;
			}
		} catch (err) {
			console.log(error);
			throw err;
		}
	},
	async updateServiceType(req, res) {
		let barId = req?.body?.bar_id;
		let serviceType = req?.body?.service_type;
		let confirmChangeFlag = req?.body?.confirm_change_flag;
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: barId,
					isDeleted: 'No'
				}
			});
			if (!barDetails) {
				return 0;
			}
			if (barDetails?.serviceType === serviceType) {
				return 1;
			}
			if (!confirmChangeFlag) {
				const switchedCount = await OrderModel.count({
					where: {
						orderServiceType:
							serviceType?.toLowerCase() === 'pickup' ? 'TABLE' : 'PICKUP',
						barID: barDetails?.id
					}
				});
				if (serviceType.toLowerCase() !== 'both' && switchedCount > 0) {
					return {
						confirmChangeFlag: true,
						switchedCount,
						serviceType: barDetails?.serviceType,
						requestServiceType: serviceType,
						popUpFlag: 1
					};
				}
			}
			/* update serviceType field */
			await BarModel.update(
				{
					serviceType: serviceType
				},
				{
					where: {
						id: barId
					}
				}
			);
			/* update waitTimeServiceType field */
			if (serviceType != 'BOTH') {
				if (serviceType != barDetails?.waitTimeServiceType) {
					if (barDetails?.waitTimeServiceType == 'BOTH') {
						await BarModel.update(
							{
								waitTimeServiceType: serviceType
							},
							{ where: { id: barDetails?.id } }
						).catch((error) => error);
					} else {
						await BarModel.update(
							{
								waitTimeServiceType: null
							},
							{ where: { id: barDetails?.id } }
						).catch((error) => error);
					}
				}
			}
			await barDetails.reload();
			var products = await ProductModel.findAll({
				where: {
					isDeleted: 'No',
					isUpdateByUser: 'No',
					barID: barId
				},
				include: [
					{
						model: ProductVariantTypes,
						as: 'product_variant_types',
						required: false,
						where: {
							isDeleted: 'No',
							isUpdateByUser: 'No'
						}
					}
				]
			});
			if (products) {
				for (var product of products) {
					for (let productVariantTypeDetail of product?.product_variant_types) {
						productVariantTypeDetail.update({ serviceType: serviceType });
					}
					product.update({ serviceType: serviceType });
				}
			}
			return 2;
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	async getWaitTimeServiceType(req, res) {
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: req.body.bar_id,
					isDeleted: 'No'
				}
			});
			if (!barDetails) {
				return 0;
			} else {
				return {
					waitTimeServiceType:
						barDetails?.waitTimeServiceType != null
							? barDetails?.waitTimeServiceType
							: '',
					serviceType:
						barDetails?.serviceType != null ? barDetails?.serviceType : ''
				};
			}
		} catch (err) {
			console.log(error);
			throw err;
		}
	},
	async updateWaitTimeServiceType(req, res) {
		let barId = req?.body?.bar_id;
		let serviceType =
			req?.body?.waitTimeServiceType != ''
				? req?.body?.waitTimeServiceType
				: null;
		try {
			let barDetails = await BarModel.findOne({
				where: {
					id: barId,
					isDeleted: 'No'
				}
			});
			if (!barDetails) {
				return 0;
			}
			if (barDetails?.waitTimeServiceType === serviceType) {
				return 1;
			}
			/* update wait time serviceType field */
			await BarModel.update(
				{
					waitTimeServiceType: serviceType
				},
				{
					where: {
						id: barId
					}
				}
			);
			return 1;
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	async getSubHeadingWaitTime(req, res) {
		try {
			let subCategoryWaitTimeDetails = await SubCategoryWaitTimeModel.findAll({
				where: {
					subCategoryID: req.body.sub_category_id,
					barID: req.body.bar_id
				},
				attributes: [
					[Sequelize.literal('waitTimeType'), 'type'],
					[
						Sequelize.literal(
							"`weekDay`,JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'startTime',TIME_FORMAT(`startTime`,'%H:%i'),'endTime',TIME_FORMAT(`endTime`,'%H:%i'),'waitTime',TIME_FORMAT(`waitTime`,'%H:%i')))"
						),
						'weekDay_WaitingTime'
					]
				],
				group: ['weekDay'],
				order: ['weekDay'],
				raw: true
			});
			if (subCategoryWaitTimeDetails) {
				let newResponse = subCategoryWaitTimeDetails.map((day) => {
					// sort response with time (ASC)
					let weekDay_WaitingTime = day.weekDay_WaitingTime.sort(function (
						a,
						b
					) {
						return a.startTime.localeCompare(b.startTime);
					});
					return { ...day, weekDay_WaitingTime: weekDay_WaitingTime };
				});
				return newResponse;
			} else {
				return 0;
			}
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	async updateSubHeadingWaitTime(req, res) {
		let barID = req?.body?.bar_id;
		let waitTimeType = req?.body?.type;
		let subCategoryID = req?.body?.sub_category_id;
		const waitTimeArray = req?.body?.data;

		try {
			let subCategoryWaitTime = await SubCategoryWaitTimeModel.findOne({
				where: {
					subCategoryID: subCategoryID,
					barID: barID
				}
			});
			if (subCategoryWaitTime) {
				await SubCategoryWaitTimeModel.update(
					{
						waitTimeType: waitTimeType
					},
					{
						where: {
							subCategoryID: subCategoryID,
							barID: barID
						}
					}
				);
				waitTimeArray &&
					waitTimeArray.map((day) => {
						day &&
							day.weekDay_WaitingTime &&
							day.weekDay_WaitingTime.map(async (item) => {
								await SubCategoryWaitTimeModel.update(
									{
										waitTime: item.waitTime >= '00:00' ? item.waitTime : '00:10'
									},
									{
										where: {
											id: item.id,
											weekDay: day.weekDay
										}
									}
								);
							});
					});
				return 1;
			} else {
				return 0;
			}
		} catch (err) {
			console.log(err);
			throw err;
		}
	},
	/* Change Password */
	async changePassword(req, res) {
		let findVenue = await BarModel.findOne({
			where: {
				id: req.body.bar_id,
				isDeleted: 'No'
			}
		});

		//bar not present
		if (!findVenue) {
			return 0;
		}

		//compare old password
		if (!Bcryptjs.compareSync(req.body.old_password, findVenue.password)) {
			return 2; //old password is incorrect
		}

		const generatedSalt = Bcryptjs.genSaltSync(10);
		let encryptedPassword = await Bcryptjs.hash(
			req.body.new_password,
			generatedSalt
		);
		//update password
		return await BarModel.update(
			{
				password: encryptedPassword,
				password_updated_at: new Date()
			},
			{
				where: {
					id: findVenue.id
				}
			}
		);
	},
	/* Change PassCode */
	async changePassCode(req, res) {
		let findVenue = await BarModel.findOne({
			where: {
				id: req.body.bar_id,
				isDeleted: 'No'
			}
		});

		//bar not present
		if (!findVenue) {
			return 0;
		}

		//Check passcode status
		if (findVenue.passcodeStatus != 'Active') {
			return 3;
		}

		//compare old passcode
		if (findVenue.passcode != req.body.old_passcode) {
			return 2;
		}

		//update password
		await BarModel.update(
			{
				passcode: req.body.passcode
			},
			{
				where: {
					id: findVenue.id
				}
			}
		);
		return 1;
	},
	/* Update PassCode Status */
	async updatePassCodeStatus(req, res) {
		let findVenue = await BarModel.findOne({
			where: {
				id: req.body.bar_id,
				isDeleted: 'No'
			}
		});

		//bar not present
		if (!findVenue) {
			return 0;
		}

		//update password
		await BarModel.update(
			{
				passcodeStatus: req.body.passCodeStatus
			},
			{
				where: {
					id: findVenue.id
				}
			}
		);
		return 1;
	},
	/* Set PassCode */
	async setPassCode(req, res) {
		let findVenue = await BarModel.findOne({
			where: {
				id: req.body.bar_id,
				isDeleted: 'No'
			}
		});

		//bar not present
		if (!findVenue) {
			return 0;
		}

		//set passcode
		await BarModel.update(
			{
				passcode: req.body.passcode
			},
			{
				where: {
					id: findVenue.id
				}
			}
		);
		return 1;
	},
	/* Verify PassCode */
	async verifyPassCode(req, res) {
		let findVenue = await BarModel.findOne({
			where: {
				id: req.body.bar_id,
				passcode: req.body.passcode,
				passcodeStatus: 'Active',
				isDeleted: 'No'
			}
		});

		//bar not present
		if (!findVenue) {
			return 0;
		}

		return 1;
	},
	async venueOtpSent(req, res) {
		try {
			let email = req.body.email;
			let barDetails = await BarModel.findOne({
				where: {
					email: email
				}
			});
			//venue is available with email
			if (barDetails) {
				return 0;
			}
			let otp = helper.generateOtp();
			let existingEmailVerificationEntry = await EmailVerificationModel.findOne(
				{ where: { email } }
			);
			if (!existingEmailVerificationEntry) {
				await EmailVerificationModel.create({
					email: email,
					otp: otp
				});
			} else {
				await EmailVerificationModel.update(
					{
						otp: otp
					},
					{
						where: {
							email
						}
					}
				);
			}

			let subject = 'Connect Your Venue To Your MyTab Management Portal';
			let mailbody =
				'<div><p>Hello ' +
				req.body.manager_name +
				',' +
				'</p><p>In order to create your venue to your MyTab Management Portal, please enter the 6-digit verification code below:</p>' +
				'<p>One-Time Code: ' +
				'<b>' +
				otp +
				'</b>' +
				' </p> ' +
				'<p>If you did not request this connection, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>' +
				'<p>Thank you, <br /> MyTab Venue Support <br />' +
				'<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p></div>';
			mail.sendmail(res, req.body.email, subject, mailbody);
			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async createVenueOtpVerify(req, res) {
		try {
			let email = req.body.email;
			let barDetails = await EmailVerificationModel.findOne({
				where: { email }
			});
			//venue is not available with email
			if (!barDetails) {
				return 0;
			}

			//OTP MISMATCH
			if (Number(barDetails.otp) != Number(req.body.otp)) {
				return 1;
			}

			await EmailVerificationModel.destroy({ where: { email } });
			return 2;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async checkStripeIntegration(req, res) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			//Invalid venue id
			if (!barDetails) {
				return 0;
			}
			const barStripeID = barDetails.stripeID;
			if (barStripeID === '' || barStripeID === null) {
				const account = await stripe.accounts.create({
					type: 'standard',
					country: 'AU',
					email: barDetails.email,
					settings: { payments: { statement_descriptor: 'MYTAB PTY LTD' } }
				});
				if (account) {
					const accountLink = await stripe.accountLinks.create({
						account: account.id,
						refresh_url: `${constant.CMS_URL}/stripe-account-process-closed?success=false&bar_id${barDetails.id}`,
						return_url: `${constant.CMS_URL}/stripe-account-process-closed?success=true&bar_id${barDetails.id}`,
						type: 'account_onboarding'
					});
					if (accountLink) {
						await BarModel.update(
							{ stripeID: account.id },
							{ where: { id: barDetails.id } }
						);
						return { isRegister: 1, url: accountLink.url };
					} else {
						return -1;
					}
				} else {
					return -1;
				}
			} else {
				let stripeData = await stripe.account
					.retrieve(barStripeID)
					.catch((err) => {
						console.log(err);
						return -1;
					});
				if (stripeData) {
					return { isRegister: 0, stripeData: stripeData };
				} else {
					return -1;
				}
			}
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async completeStripeIntegration(req, res) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			//Invalid venue id
			if (!barDetails) {
				return 0;
			}
			await BarModel.update(
				{ accountVerified: 'Approved' },
				{ where: { id: barDetails.id } }
			);
			return 1;
		} catch (error) {
			console.log(error);
			throw error;
		}
	},
	async getCustomerList(req, res) {
		try {
			let barId = req.body.bar_id;
			let barDetails = await BarModel.findOne({
				where: {
					id: barId
				}
			});
			//Invalid venue id
			if (!barDetails) {
				return 0;
			}
			// let orderDetails = await OrderModel.findAll({
			// 	// where: { id: barDetails.id },
			// 	group: ['userID']
			// });
			// const orderDetails = await OrderModel.findAll({
			// 	attributes: [
			// 		'userID', // Include the userID for grouping
			// 		[
			// 			Sequelize.fn('COUNT', Sequelize.col('relatedOrders.id')),
			// 			'orderCount'
			// 		] // Count related orders for each user
			// 	],
			// 	group: ['userID'], // Group orders by userID
			// 	include: {
			// 		model: OrderModel, // Include the Order model again (self-join)
			// 		as: 'relatedOrders' // Alias to differentiate from top-level Order attributes
			// 		// attributes: ['id'], // Specify desired order attributes
			// 		// where: { barID: 4, userID: userID } // Filter nested orders for barID 4
			// 	},
			// 	where: { barID: 4 }
			// });
			const page = 1; // Page number
			const perPage = 10; // Number of records per page
			// const searchUserName = 'um'; // Number of records per page
			const sortBy = 'lowestAge'; // Example sorting option (replace with actual sorting input)
			let orderByClause = [];
			// Step 1: Determine orderBy clause based on sortBy option
			switch (sortBy) {
				case 'newToOld':
					orderByClause = [
						[Sequelize.literal('"User->Orders.createdAt" DESC')]
					]; // Sort by user's first order date in descending order
					break;
				case 'oldToNew':
					orderByClause = [[Sequelize.literal('"User->Orders.createdAt" ASC')]]; // Sort by user's first order date in ascending order
					break;
				case 'highestAvgOrderTotal':
					orderByClause = [[Sequelize.literal('avgOrderTotal DESC')]]; // Sort by highest average order total spent per user
					break;
				case 'lowestAvgOrderTotal':
					orderByClause = [[Sequelize.literal('avgOrderTotal ASC')]]; // Sort by lowest average order total spent per user
					break;
				case 'highestTotalOrderSpent':
					orderByClause = [[Sequelize.literal('totalOrderSpent DESC')]]; // Sort by highest total order spent per user
					break;
				case 'lowestTotalOrderSpent':
					orderByClause = [[Sequelize.literal('totalOrderSpent ASC')]]; // Sort by lowest total order spent per user
					break;
				case 'highestSpent':
					orderByClause = [[Sequelize.literal('hightestSpent DESC')]]; // Sort by highest total order spent per user
					break;
				case 'lowestSpent':
					orderByClause = [[Sequelize.literal('hightestSpent ASC')]]; // Sort by lowest total order spent per user
					break;
				default:
					orderByClause = []; // Default to no sorting
					break;
			}
			// Step 2: Fetch aggregated counts per user with pagination, search, and sorting
			const aggregatedCounts = await OrderModel.findAll({
				attributes: [
					'userID',
					[Sequelize.fn('COUNT', Sequelize.col('*')), 'orderCount'],
					[Sequelize.fn('AVG', Sequelize.col('total')), 'avgOrderTotal'],
					[Sequelize.fn('SUM', Sequelize.col('total')), 'totalOrderSpent'],
					[Sequelize.fn('MAX', Sequelize.col('total')), 'hightestSpent']
				],
				group: ['userID'],
				where: {
					barID: barId // Search condition for user name
					// '$user.fullName$': {
					// 	[Sequelize.Op.like]: `%${searchUserName}%`
					// }
				},
				include: [
					{
						model: user, // Assuming UserModel is associated with OrderModel,
						attributes: [
							[
								Sequelize.literal(`CASE
								WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 18 AND 27 THEN "18 - 27 (Gen Z)"
								WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 28 AND 43 THEN "28 - 43 (Millennial)"
								WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 44 AND 59 THEN "44 - 59 (Gen X)"
								WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) BETWEEN 60 AND 78 THEN "60 - 78 (Baby Boomer)"
								ELSE "79 - 99+ (Silent Generation+)"
          		END`),
								'age_range'
							],
							'fullName',
							'email',
							'countryCode',
							'mobile'
						],
						required: false
					}
				],
				offset: (page - 1) * perPage,
				limit: perPage,
				order: orderByClause // Apply orderBy clause based on sortBy option
			});
			// Step 2: Fetch all related orders for each user
			const users = aggregatedCounts.map(async (count) => {
				const userName = count?.user?.dataValues?.fullName || 'Anonymous User'; // Default to 'anonymous user' if User is null
				const email = count?.user?.dataValues?.email || ' - '; // Default to 'anonymous user' if User is null
				const mobileNumber = count?.user?.dataValues?.mobile || ' - '; // Default to 'anonymous user' if User is null
				const ageRange =
					count?.user?.dataValues?.age_range || '79 - 99+ (Silent Generation+)'; // Default to 'anonymous user' if User is null
				const userOrders = await OrderModel.findAll({
					attributes: ['orderNo'],
					where: { userID: count.userID, barID: 4 }
				});
				return {
					userID: count.userID,
					userDetails: {
						fullName: userName,
						ageRange: ageRange,
						email: email,
						countryCode: count?.user?.dataValues?.countryCode,
						mobile: mobileNumber
					},
					orderCount: Number(count?.dataValues?.orderCount.toFixed(2)),
					avgOrderTotal: Number(count?.dataValues?.avgOrderTotal.toFixed(2)),
					totalOrderSpent: Number(
						count?.dataValues?.totalOrderSpent.toFixed(2)
					),
					hightestSpent: Number(count?.dataValues?.hightestSpent.toFixed(2)),
					relatedOrders: userOrders
				};
			});
			// Step 3: Wait for all user queries to finish
			const orderDetails = await Promise.all(users);
			// Step 4: Fetch total count of aggregatedCounts without pagination
			const totalCount = await OrderModel.count({
				distinct: true,
				col: 'userID',
				where: {
					barID: barId
					// '$user.fullName$': {
					// 	[Sequelize.Op.like]: `%${searchUserName}%`
					// }
				},
				include: [
					{
						model: user,
						required: false
					}
				]
			});
			let exportCSVData = [];
			orderDetails.map((user) => {
				exportCSVData.push({
					'Customer Name': user.userDetails.fullName,
					Email: user.userDetails.email,
					Mobile: user.userDetails.countryCode + ' ' + user.userDetails.mobile,
					Orders: user.orderCount,
					'Highest Individual Purchase': user.hightestSpent,
					'Total Spend': user.totalOrderSpent
				});
			});
			const fields = [
				'Customer Name',
				'Email',
				'Mobile',
				'Orders',
				'Highest Individual Purchase',
				'Total Spend'
			];
			const json2csvParser = new Parser({
				fields,
				defaultValue: 'NA',
				includeEmptyRows: true
			});

			return json2csvParser.parse(exportCSVData);
			// return { userList: orderDetails, totalUser: totalCount };
		} catch (error) {
			console.log(error);
			throw error;
		}
		// }
	}
};
