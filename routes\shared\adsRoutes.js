// routes/shared/adsRoutes.js
const express = require('express');
const advertiserAuth = require('../../middleware/advertiserAuth').advertiserAuthorization;
const venueAuth = require("../../middleware/venueAuth").venueAuthorization;
const MulterMiddleware = require('../../middleware/multer');
const AdsController = require('../../controllers/advertiser/adsController');

const createAdsRoutes = (userType) => {
    const router = express.Router();
    
    // Choose auth middleware based on user type
    const auth = userType === 'venue' ? venueAuth : advertiserAuth;

    // Routes
    router.get('/test', (req, res) => res.json({ message: `${userType} ads working` }));
    
    // router.post('/create-ad', auth, MulterMiddleware.singleAdPic, AdsController.addAd);
    router.post('/delete-ad', auth, AdsController.deleteAd);
    router.post('/delete-campaign', auth, AdsController.deleteCampaign);
    router.post('/update-pause-status', auth, AdsController.updatePauseStatus);
    router.post('/add-card', auth, AdsController.addCard);
    router.post('/saved-cards', AdsController.getSavedCards);
    router.post('/createAdWithPayment', MulterMiddleware.singleAdPic, auth, AdsController.createAdWithPayment);
    router.get('/payment-status', auth, AdsController.checkPaymentStatus);
    router.post('/process-payment', auth, AdsController.processPaymentWithSavedCard);
    router.put('/update-card', AdsController.updateCard);
    router.post('/create-setup-intent', auth, AdsController.createSetupIntent);

    return router;
};

module.exports = createAdsRoutes;