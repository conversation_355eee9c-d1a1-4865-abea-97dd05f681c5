var express = require('express');
var router = express.Router();

const Authorization =
	require('../../../middleware/venueAuth').venueAuthorization;
const IsVenueUserConnectedWithBar =
	require('../../../middleware/venueAuth').isVenueUserConnectedWithBar;

const discountController = require('../../../controllers/v2/venue/discountController');

router
	.route('/')
	.post(
		Authorization,
		IsVenueUserConnectedWithBar,
		discountController.addDiscount
	)
	.put(
		Authorization,
		IsVenueUserConnectedWithBar,
		discountController.updateDiscount
	)
	.delete(
		Authorization,
		IsVenueUserConnectedWithBar,
		discountController.deleteDiscount
	);

router.post(
	'/list',
	Authorization,
	IsVenueUserConnectedWithBar,
	discountController.getDiscountList
);

router.post(
	'/details',
	Authorization,
	IsVenueUserConnectedWithBar,
	discountController.getDiscountDetails
);

module.exports = router;
