/*Messages,status code and services require*/
require('dotenv').config();

const moment = require('moment');

const BarModel = require('../../../database/models').bar;
const Discount = require('../../../database/models').discount;
const DiscountSegments = require('../../../database/models').discount_segments;
const DiscountUsers = require('../../../database/models').discount_users;
const UsersModel = require('../../../database/models').user;
const SegmentsModel = require('../../../database/models').segment;
const { sequelize } = require('../../../database/models');
const message = require('../../../config/cmsMessage').cmsMessage;
//add messages
const Sequelize = require('sequelize');

const Op = Sequelize.Op;

module.exports = {
	async addDiscount(req) {
		const {
			bar_id,
			code,
			type,
			discount_type,
			discount_value,
			start_date,
			end_date,
			eligibility_type,
			segment_ids,
			user_ids,
			total_usage_limit,
			per_user_limit,
			combined_eligibility,
			is_combined_discount
		} = req.body || {};

		try {
			const bar = await BarModel.findOne({
				where: {
					id: bar_id
				}
			});
			if (!bar) {
				return {
					code: 0,
					message: 'Bar not found'
				};
			}
			const timezone = bar?.timezone;
			//change with literal
			const existingDiscount = await Discount.findAll({
				where: {
					barID: bar_id
				}
			});

			if (type === 'automatic') {
				const automaticCodes = existingDiscount.filter(
					(elem) => elem.type === 'automatic'
				);
				if (automaticCodes.length >= 10) {
					return {
						code: -3,
						message:
							'You have reached the maximum number of active automatic discounts allowed (10). To save this new discount, please delete one of your existing automatic discounts first.'
					};
				}
			}

			const isDiscountExist = existingDiscount.some(
				(obj) => obj.code.toLowerCase() === code.toLowerCase()
			);

			if (isDiscountExist) {
				return {
					code: 0,
					message: 'Discount already exists'
				};
			}

			if (
				start_date &&
				end_date &&
				(!moment(start_date, 'YYYY-MM-DD', true).isValid() ||
					!moment(end_date, 'YYYY-MM-DD', true).isValid())
			) {
				return {
					code: 0,
					message: 'Invalid Start date or End Date'
				};
			}

			const startDateUTC = timezone
				? moment.tz(start_date, timezone).utc().format()
				: moment(start_date).utc().format();

			const endDateUTC = end_date
				? timezone
					? moment.tz(end_date, timezone).utc().format()
					: moment(end_date).utc().format()
				: null;

			if (
				startDateUTC &&
				endDateUTC &&
				moment(startDateUTC).isAfter(moment(endDateUTC))
			) {
				return {
					code: 0,
					message: 'Start date cannot be after end date'
				};
			}

			// const transaction = await sequelize.transaction();

			try {
				const discount = await Discount.create(
					{
						barID: bar_id,
						code,
						type,
						discountType: discount_type,
						discountValue: discount_value,
						startDate: startDateUTC,
						endDate: endDateUTC,
						isActive: '1',
						eligibilityType: eligibility_type,
						totalUsageLimit: total_usage_limit || null,
						perUserLimit: per_user_limit || null,
						combinedEligibility: combined_eligibility,
						is_combined_discount: is_combined_discount
					}
					// { transaction }
				);

				if (
					eligibility_type === 'segment_group' &&
					segment_ids &&
					segment_ids.length > 0
				) {
					const segmentDiscountData = segment_ids.map((segment_id) => ({
						segmentID: segment_id,
						discountID: discount.id,
						barID: bar_id
					}));

					await DiscountSegments.bulkCreate(segmentDiscountData, {
						// transaction
					});
				}

				if (
					eligibility_type === 'individual_users' &&
					user_ids &&
					user_ids.length > 0
				) {
					const userEligibilityData = user_ids.map((user_id) => ({
						userID: user_id,
						discountID: discount.id,
						barID: bar_id
					}));

					await DiscountUsers.bulkCreate(
						userEligibilityData
						// { transaction }
					);
				}

				// await transaction.commit();

				return {
					code: 1,
					data: discount,
					message: 'Discount added successfully'
				};
			} catch (error) {
				// await transaction.rollback();
				console.log('Transaction error:', error);
				throw error;
			}
		} catch (error) {
			console.log('Add discount error:', error);
			throw error;
		}
	},

	async updateDiscount(req) {
		const { is_active, id, bar_id } = req.body || {};

		try {
			const discount = await Discount.findOne({
				where: {
					barID: bar_id,
					id
				}
			});

			if (!discount) {
				return {
					code: 0,
					message: 'Discount not found'
				};
			}

			const updateObj = { isActive: is_active };

			await Discount.update(updateObj, { where: { barID: bar_id, id } });

			return {
				code: 1,
				message: 'Discount updated successfully'
			};
		} catch (error) {
			console.log('Update Discount error:', error);
			throw error;
		}
	},

	async deleteDiscount(req) {
		try {
			const { id, bar_id } = req.body || {};

			const whereClause = { id, barID: bar_id };

			const discount = await Discount.findOne({
				where: whereClause
			});

			if (!discount) {
				return {
					code: 0,
					message: 'Discount not found'
				};
			}

			// const transaction = await sequelize.transaction();

			try {
				await DiscountSegments.destroy({
					where: { discountID: id }
					// transaction
				});

				await DiscountUsers.destroy({
					where: { discountID: id }
					// transaction
				});

				await Discount.destroy({
					where: { id, barID: bar_id }
					// transaction
				});

				// await transaction.commit();

				return {
					code: 1,
					message: 'Discount deleted successfully'
				};
			} catch (error) {
				// await transaction.rollback();
				console.log('Transaction error:', error);
				throw error;
			}
		} catch (error) {
			console.log('Delete Discount error:', error);
			throw error;
		}
	},

	async getDiscountList(req) {
		try {
			const { page = 1, limit = 100, bar_id, sort_by, search } = req.body;

			const offset = (page - 1) * limit;

			const whereClause = { barID: bar_id };

			if (search && search.trim()) {
				whereClause[Op.or] = [{ code: { [Op.like]: `%${search.trim()}%` } }];
			}

			let order;
			switch (sort_by) {
				case 'newest':
					order = [['createdAt', 'DESC']];
					break;
				case 'oldest':
					order = [['createdAt', 'ASC']];
					break;
				case 'alphabeticAsc':
					order = [['code', 'ASC']];
					break;
				case 'alphabeticDesc':
					order = [['code', 'DESC']];
					break;
				case 'most_used':
					order = [[Sequelize.literal('usage_count'), 'DESC']];
					break;
				case 'least_used':
					order = [[Sequelize.literal('usage_count'), 'ASC']];
					break;
				default:
					order = [['createdAt', 'DESC']];
			}

			const { rows: discounts, count: totalCount } =
				await Discount.findAndCountAll({
					attributes: [
						'id',
						'code',
						'type',
						'discountType',
						'discountValue',
						'startDate',
						'endDate',
						'isActive',
						'totalUsageLimit',
						'perUserLimit',
						'eligibilityType',
						'is_combined_discount',
						'createdAt',
						'updatedAt',
						[
							Sequelize.literal(
								'(SELECT COUNT(*) FROM order_discount WHERE order_discount.discountID = discount.id)'
							),
							'usage_count'
						]
					],
					where: whereClause,
					order,
					limit: parseInt(limit),
					offset,
					raw: true
				});

			const now = moment();
			const processedDiscount = discounts.map((discount) => {
				let phase;
				const startDate = moment(discount.startDate);
				const endDate = discount.endDate ? moment(discount.endDate) : null;

				if (startDate.isAfter(now)) {
					phase = 'scheduled';
				} else if (endDate && endDate.isBefore(now)) {
					phase = 'expired';
				} else {
					phase = 'active';
				}

				let discountMethod =
					discount.type === 'automatic' ? 'Automatic' : 'Manual';
				let discountType;
				if (discount.discountType === 'percentage') {
					discountType = `${discount.discountValue}% off order`;
				} else {
					discountType = `$${discount.discountValue} off order`;
				}

				return {
					id: discount.id,
					title: discount.code,
					phase,
					discount_method: discountMethod,
					discount_type: discountType,
					is_active: discount.isActive,
					usage_count: discount.usage_count,
					start_date: discount.startDate,
					end_date: discount.endDate,
					total_usage_limit: discount.totalUsageLimit,
					per_user_limit: discount.perUserLimit,
					eligibility_type: discount.eligibilityType,
					is_combined_discount: discount.is_combined_discount,
					created_at: discount.createdAt,
					updated_at: discount.updatedAt
				};
			});

			return {
				code: 1,
				data: {
					discounts: processedDiscount,
					pagination: {
						total: totalCount,
						page: parseInt(page),
						limit: parseInt(limit),
						pages: Math.ceil(totalCount / limit)
					}
				},
				message: 'Discount retrieved successfully'
			};
		} catch (error) {
			console.log('Get Discount list error:', error);
			throw error;
		}
	},

	async getDiscountDetails(req) {
		const { discount_id, bar_id } = req.body || {};

		try {
			const bar = await BarModel.findOne({
				where: {
					id: bar_id
				},
				attributes: ['timezone']
			});

			if (!bar) {
				return {
					code: 0,
					message: 'Bar not found.'
				};
			}

			const timezone = bar.timezone || 'UTC';

			const discount = await Discount.findOne({
				where: {
					id: discount_id,
					barID: bar_id
				},
				include: [
					{
						model: DiscountSegments,
						as: 'segments',
						attributes: ['id', 'segmentID'],
						include: [
							{
								model: SegmentsModel,
								attributes: ['name']
							}
						]
					},
					{
						model: DiscountUsers,
						as: 'eligibleUsers',
						attributes: ['id', 'userID'],
						include: [
							{
								model: UsersModel,
								attributes: ['fullName']
							}
						]
					}
				]
			});

			if (!discount) {
				return {
					code: 0,
					message: 'Discount not found'
				};
			}

			const result = {
				id: discount.id,
				bar_id: discount.barID,
				code: discount.code,
				type: discount.type,
				discount_type: discount.discountType,
				discount_value: parseFloat(discount.discountValue),
				start_date: discount.startDate
					? moment(discount.startDate).tz(timezone).format('YYYY-MM-DD')
					: null,
				end_date: discount.endDate
					? moment(discount.endDate).tz(timezone).format('YYYY-MM-DD')
					: null,
				is_active: discount.isActive,
				total_usage_limit: discount.totalUsageLimit,
				per_user_limit: discount.perUserLimit,
				eligibility_type: discount.eligibilityType,
				combined_eligibility: discount.combinedEligibility
			};

			if (discount.segments && discount.segments.length > 0) {
				result.segments = discount.segments.map((segment) => ({
					segment_id: segment.segmentID,
					segment_name: segment.segment ? segment.segment.name : null
				}));
				// result.segment_ids = discount.segments.map(
				// 	(segment) => segment.segmentID
				// );
			}

			if (discount.eligibleUsers && discount.eligibleUsers.length > 0) {
				result.users = discount.eligibleUsers.map((user) => ({
					user_id: user.userID,
					user_name: user.user ? user.user.fullName : null
					// user_email: user.user ? user.user.email : null
				}));
				// result.user_ids = discount.eligibleUsers.map((user) => user.userID);
			}

			return {
				code: 1,
				data: result,
				message: 'Discount details retrieved successfully'
			};
		} catch (error) {
			console.log('Get discount details error:', error);
			throw error;
		}
	}
};
