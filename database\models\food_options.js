'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class food_options extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
		}
	}
	food_options.init(
		{
			id: {
				type: DataTypes.BIGINT,
				autoIncrement: true,
				primaryKey: true
			},
			name: { type: DataTypes.STRING },
			initials: DataTypes.TEXT,
			status: {
				type: DataTypes.ENUM('Active', 'Inactive'),
				defaultValue: 'Active'
			},
			listingOrder: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			isDeleted: { type: DataTypes.ENUM('Yes', 'No'), defaultValue: 'No' },
			createdAt: { type: DataTypes.DATE, allowNull: false },
			updatedAt: { type: DataTypes.DATE, allowNull: false }
		},
		{
			sequelize,
			modelName: 'food_options',
			timestamps: true,
			freezeTableName: true
		}
	);
	return food_options;
};
